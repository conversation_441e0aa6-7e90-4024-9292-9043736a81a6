<template>
  <router-view v-slot="{ Component }">
    <keep-alive :include="['home', 'ResetPass', 'DeviceSet', 'EditDeviceName', 'DeviceVoice']">
      <component :is="Component" />
    </keep-alive>
  </router-view>
</template>

<script lang="ts">
import { onMounted, defineComponent } from "vue"
import config from "./config"
import { useVConsole } from "@/components/useVConsole"
// import { createElement, loadAirwallex } from "airwallex-payment-elements"
import {
  init_params,
  reloadMall,
  clickNotification,
  getUrlParamsObject,
  getConfig,
  sendAppLeave,
  opendAppLeave,
} from "@/utils"

export default defineComponent({
  name: "App",
  setup() {
    onMounted(() => {
      if (window.location.href.indexOf("app_name") !== -1) {
        const getUrlKey: any = getUrlParamsObject(window.location.href)
        getConfig(config, getUrlKey)
        config.app_id = config.app_id.replace(/#.*/, "")
      }
      const str = navigator.userAgent
      if (!!str.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
        config.platform = "ios"
      } else if (str.indexOf("Android") > -1 || str.indexOf("Adr") > -1) {
        config.platform = "android"
      }
      if (localStorage.getItem("token")) {
        let token: any = localStorage.getItem("token")
        let user_id: any = localStorage.getItem("user_id")
        if(localStorage.getItem("role") == '1'){
          useVConsole(1)
        }
        config.token = token
        config.user_id = user_id
        window.init_params = init_params
        reloadMall()
      }
      window.clickNotification = clickNotification
      window.sendAppLeave = sendAppLeave
      window.opendAppLeave = opendAppLeave
      // if (config.platform === "ios") {
      //   loadAirwallex({
      //     env: "prod", // 环境，可以是 'demo' 或 'prod'
      //     origin: window.location.origin,
      //   }).then(() => {
      //     if (config.platform === "ios") {
      //       createElement("applePayButton", {
      //         intent_id: "",
      //         client_secret: "",
      //         amount: {
      //           value: 0.01,
      //           currency: "USD",
      //         },
      //         totalPriceLabel: "EKEN",
      //         origin: window.location.origin,
      //         countryCode: "US",
      //       })
      //     }
      //   })
      // }
    })
  },
})
</script>

<style lang="less">
* {
  -webkit-touch-callout: none; /* 禁用 iOS 上的长按弹出菜单 */
  -webkit-user-select: none; /* 禁用文本选择 */
  user-select: none; /* 禁用文本选择 */
}
a {
  text-decoration: none;
  color: inherit;
}
img {
  -webkit-user-drag: none; /* 禁用拖拽 */
  user-drag: none;
}
html,
body,
p {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Segoe UI, Arial,
    Roboto, "PingFang SC", "miui", "Hiragino Sans GB", "Microsoft Yahei", sans-serif;
}
.pages {
  min-height: 100vh;
  background-color: #e2e3e5;
}
.page {
  min-height: 100vh;
  background-color: #fff;
}
.pages-data {
  min-height: 100vh;
  background-color: #f1f1f1;
}
</style>
