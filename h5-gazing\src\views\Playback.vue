<template>
  <div class="page">
    <Header title="历史事件" :videoType="1" ref="header">
      <van-icon
        name="delete-o"
        size="25"
        color="#666"
        @click="openDel"
        v-if="config.device.is_primary"
      />
    </Header>
    <div class="video-container">
      <div class="video-player">
        <video
          ref="videoElement"
          class="video-element"
          controls
          muted
          playsinline
          webkit-playsinline
        ></video>
      </div>
    </div>
    <div class="date-container" @click="showDate" ref="videoDate">
      <div class="video-time">
        {{ currentDate || "请选择时间" }}
        <van-icon name="arrow-down" size="14" class="data-icon" />
      </div>
      <div class="video-num">共 {{ videoTotal }} 个视频</div>
    </div>
    <van-calendar
      v-model:show="pickerDateStatus"
      switch-mode="month"
      :show-confirm="false"
      @select="pickerConfirm"
      :min-date="minDate"
      :max-date="maxDate"
      :formatter="formatter"
    />
    <div ref="playbackList" class="playback-list">
      <div class="data-empty" v-if="!state.videoList.length">当天无视频文件</div>
      <div v-show="state.videoList.length" class="playback-list-left">
        <TimeAxis
          ref="timeAxisDom"
          :video-list-touch-is-open="videoListTouchIsOpen"
          @svgDataScrollEmit="svgDataScrollEmit"
          @touchOpen="touchOpen"
        ></TimeAxis>
      </div>
      <div ref="playbackListRight" class="playback-list-right">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          :finished-text="state.videoList.length ? '没有更多了' : ''"
          @load="onLoad"
        >
          <van-swipe-cell
            class="video-list-item"
            v-for="(item, index) in state.videoList"
            :key="item.path"
            :data-id="index"
          >
            <div @click="clickItem(item)" class="item" :class="item.active ? 'active' : ''">
              <van-checkbox v-model="item.active" v-if="showDel"></van-checkbox>
              <div class="right-container">
                <img
                  v-if="item.type === 'PIR'"
                  class="pir-imgs"
                  src="../assets/playback/1.png"
                  alt=""
                />
                <img
                  v-else-if="item.type === 'WIFI'"
                  class="wifi-imgs"
                  src="../assets/playback/3.png"
                  alt=""
                />
                <p class="day-time" :class="item.type === 'PIR' ? 'day-time-pir' : ''">
                  {{ item.time }}
                </p>
                <!-- <span class="dian" v-if="item.is_sync === 1"></span> -->
              </div>
              <div class="left-container">
                <img class="video-cover" :src="item.image_sign_url" alt="" />
                <div class="duration">{{ item.durationS }}</div>
              </div>
              <div class="download-icon" @click.stop="downloadVideo(item.path)"></div>
            </div>
            <template #right v-if="config.device.is_primary">
              <van-button
                square
                text="删除"
                type="danger"
                @click="delVideos([item.path])"
                class="delete-button"
              />
            </template>
          </van-swipe-cell>
        </van-list>
      </div>
    </div>
    <div class="del-modal" :class="{ open: showDel }">
      <van-checkbox icon-size="25" v-model="isCheckAll" @change="checkAllChange"></van-checkbox>
      <van-icon name="delete-o" size="28" color="#fff" @click="delVideos(checkList)" />
    </div>
    <div class="del-modals" v-if="showProgress">
      <van-progress :percentage="progressValue" stroke-width="8" :show-pivot="false" />
      <div class="progress-test">{{ progressValue }}%</div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, onUnmounted, ref, reactive, watch } from "vue"
import { get_videos, delete_videos, get_videos_count } from "@/api/index"
import type { syncData, videosCountData } from "@/api/base"
import {
  getCurrentTime,
  base64EncodeM3U8,
  convertMillisecondsToTime,
  send_file_blob,
  download_file_success,
  download_file_error,
} from "@/utils"
import { downloadFile } from "@/oss"
import config from "@/config"
import Header from "@/components/Header.vue"
import TimeAxis from "@/components/TimeAxis.vue"
import { sendPlayback } from "@/components/WebRTCClient"
import Hls from "hls.js"
import { showConfirmDialog, showToast } from "vant"

const minDate = new Date(1997, 10, 1)
const maxDate = new Date(getCurrentTime())
const currentDate = ref(getCurrentTime())
const pickerDateStatus = ref(false)
const videoElement = ref<HTMLVideoElement | null>(null)
const state = reactive({
  videoList: [] as syncData[],
  pagesNum: 1,
  videosCount: {} as videosCountData,
})
const device_sn = ref<string>("")
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const shouldAutoplay = ref(false)
const isInitialLoad = ref(true) // 添加初始化标志
const activeVideoPath = ref("") // 添加变量保存当前激活的视频信息
const showDel = ref<boolean>(false)
const isCheckAll = ref(false)
const checkList = ref<Array<any>>([])
const clcikNum = ref(0)
const videoTotal = ref(0)
const header = ref<HTMLElement | null>(null)
const videoDate = ref<HTMLElement | null>(null)
interface TimeAxisInstance {
  getSvgDataPath: (videoList: any) => void
  setCurrentPosi: (id: string, type: string) => void
  resetDefaultPosi: (flag: boolean) => void;
}
const timeAxisDom = ref<TimeAxisInstance | null>(null)
const playbackList = ref<HTMLElement | null>(null)
const playbackListRight = ref<HTMLElement | null>(null)
const hls = ref<any>(null)
const hasLoadedVideosCount = ref(false)
let observer: any = null
interface VideoItemTimes {
  [key: string]: string
}
const videoItemTimes = reactive<VideoItemTimes>({}) // 收集视频列表展示区域的可视数据
let videoListCurrentScrollTop = ref<number>(0) // 指定的视频数据距离父元素顶部的距离
let videoListTimer: any = null // 用于视频数据处理完成之后, 执行时间轴刻度的渲染
let timeScaleTouchIsOpen = ref<boolean>(false)
let videoListScrolling = ref<boolean>(false) // 视频列表滚动是否结束, 从而判断是否允许控制时间轴
let videoListTouchIsOpen = ref<boolean>(false)
let scrollTopSmoothTimer: any = null // 用于视频数据处理完成之后, 执行时间轴刻度的渲染
const ws = ref<WebSocket | null>(null)
const isConnected = ref<boolean>(false)
let reconnectAttempts = 0
const maxReconnectAttempts = 5
const pendingBlobs = ref<number>(0)
const totalBlobs = ref<number>(0)
const pendingDownloadSuccess = ref<boolean>(false)
const progressValue = ref<number>(0)
const showProgress = ref<boolean>(false)

async function blobToArrayBuffer(blob: Blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result)
    reader.onerror = () => reject(reader.error)
    reader.readAsArrayBuffer(blob)
  })
}

async function arrayBufferToBase64(buffer: ArrayBuffer) {
  const uint8Array = new Uint8Array(buffer)
  let binary = ""
  const len = uint8Array.byteLength
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(uint8Array[i])
  }
  return btoa(binary)
}

const connectWebSocket = () => {
  ws.value = new WebSocket(`wss://api-v1.seewds.com/m3u8_download/ws?token=${config.token}`)

  ws.value.onopen = () => {
    isConnected.value = true
    console.log("Connected to WebSocket server", new Date().toLocaleTimeString())
  }

  ws.value.onmessage = async (event: MessageEvent) => {
    if (event.data instanceof Blob) {
      totalBlobs.value++
      pendingBlobs.value++

      const arrayBuffer: any = await blobToArrayBuffer(event.data)
      const base64Data = await arrayBufferToBase64(arrayBuffer)
      await send_file_blob({ data: base64Data, size: arrayBuffer.byteLength })
      pendingBlobs.value--

      if (pendingDownloadSuccess.value && pendingBlobs.value === 0) {
        download_file_success()
        showProgress.value = false
        pendingDownloadSuccess.value = false
        totalBlobs.value = 0
      }
      return
    }

    try {
      const message = JSON.parse(event.data)
      switch (message.type) {
        case "progress":
          progressValue.value = parseInt(message.progress)
          if (message.progress === 100) {
            if (pendingBlobs.value === 0) {
              download_file_success()
              showProgress.value = false
              totalBlobs.value = 0
            } else {
              pendingDownloadSuccess.value = true
              console.log(`Waiting for ${pendingBlobs.value} Blobs to finish processing`)
            }
          }
          break
        case "error":
          download_file_error()
          break
        default:
          console.log("收到未知类型消息: " + message.type)
      }
    } catch (e: any) {
      console.error("Failed to parse JSON message:", event.data, e)
    }
  }

  ws.value.onclose = () => {
    isConnected.value = false
    console.log("Disconnected from WebSocket server", new Date().toLocaleTimeString())
    if (reconnectAttempts < maxReconnectAttempts) {
      reconnectAttempts++
      setTimeout(connectWebSocket, 5000)
    } else {
      console.error("Maximum reconnection attempts reached.")
    }
  }

  ws.value.onerror = (error: Event) => {
    isConnected.value = false
    console.error("WebSocket error:", error)
  }
}

const sendMessage = (message: string) => {
  if (ws.value && isConnected.value) {
    ws.value.send(message)
  }
}

// 下载视频
const downloadVideo = (m3u8Url: string) => {
  showProgress.value = true
  const request = {
    type: "request",
    bucketName: "sz-iot-t1",
    endpoint: "https://oss-cn-shenzhen.aliyuncs.com",
    m3u8Url: m3u8Url,
  }
  sendMessage(JSON.stringify(request))
}

const openDel = () => {
  showDel.value = !showDel.value
  filterCheck()
  if (showDel.value) {
    clcikNum.value = 1
  } else {
    clcikNum.value = 0
    isCheckAll.value = false
  }
  checkList.value = []
}

const checkAllChange = (val: boolean) => {
  checkList.value = []
  if (val) {
    filterCheck(true)
  } else {
    filterCheck()
  }
}

const getVideosCount = () => {
  if (hasLoadedVideosCount.value) return

  get_videos_count(config.device.device_sn)
    .then((res: any) => {
      if (res.code === 0) {
        state.videosCount = res.data.videos_count || {}
        hasLoadedVideosCount.value = true
      }
    })
    .catch(() => {
      console.error("获取日历视频数量失败")
    })
}

const showDate = async () => {
  await getVideosCount()
  pickerDateStatus.value = true
}

const pickerConfirm = (value: any) => {
  currentDate.value = formatDate(value)
  pickerDateStatus.value = !pickerDateStatus.value
  ressetList()
}

const formatDate = (date: Date) => {
  return `${date.getFullYear()}/${(date.getMonth() + 1).toString().padStart(2, "0")}/${date
    .getDate()
    .toString()
    .padStart(2, "0")}`
}

// 截取path中的时分秒
function getLastSixCharsBeforeM3U8(str: string) {
  let dotIndex = str.lastIndexOf(".m3u8")
  return str.slice(dotIndex - 6, dotIndex)
}

// 过滤截取好的时分秒
function convertToTimeString(str: string) {
  let hours = str.slice(0, 2)
  let minutes = str.slice(2, 4)
  let seconds = str.slice(4, 6)
  return `${hours}:${minutes}:${seconds}`
}

const delVideos = (path: Array<any>) => {
  if (path.length === 0) return showToast("请选择您需要删除的视频")
  if (showDel.value) {
    showConfirmDialog({
      title: "提示",
      message: "是否要删除所选中的视频?",
    }).then(() => {
      del_device_videos(path)
    })
  } else {
    del_device_videos(path)
  }
}

const del_device_videos = (path: Array<any>) => {
  delete_videos(config.device.device_sn, path).then((res: any) => {
    if (res.code === 0) {
      ressetList()
    }
  })
}

const onLoad = () => {
  let date = currentDate.value.replace(/\//g, "")
  get_videos({
    product_key: config.product_key,
    device_sn: config.device_sn,
    date: Number(date),
    type: "ALL",
    page: state.pagesNum,
    page_size: 30,
  }).then((res: any) => {
    if (res.code === 0) {
      videoTotal.value = res.data.total
      loading.value = false
      if (refreshing.value) {
        state.videoList = []
        refreshing.value = false
      }
      if (res.data.list && res.data.list.length < 30) {
        finished.value = true
      }
      res.data.list &&
        res.data.list.forEach((element: syncData) => {
          element.active = activeVideoPath.value === element.path ? true : false
          if (element.path) {
            element.time = convertToTimeString(getLastSixCharsBeforeM3U8(element.path))
          }
          element.durationS = convertMillisecondsToTime(element.duration)
          if (element.image_sign_url) {
            element.image_sign_url = decodeURIComponent(element.image_sign_url)
          }
        })

      if (state.pagesNum === 1) {
        state.videoList = res.data.list || []
        if (isInitialLoad.value && res.data.list && res.data.list.length > 0) {
          const firstVideo = res.data.list[0]
          const { bucket, endpoint, path } = firstVideo
          // firstVideo.active = true
          downloadFile(bucket, endpoint, `${path}`).then((res: any) => {
            playM3U8(res)
          })
        }
      } else {
        if (res.data.list.length && isCheckAll.value) {
          res.data.list.forEach((i: syncData) => {
            i.active = true
            checkList.value.push(i.path)
          })
        }
        state.videoList = [...state.videoList, ...res.data.list]
      }
      state.pagesNum++

      // 给时间轴发送 视屏数据列表
      timeAxisDom.value!.getSvgDataPath(state.videoList)

      clearTimeout(videoListTimer)
      videoListTimer = setTimeout(() => {
        // 给每个视频数据赋予监听功能, 只要消失或者出现, 将即刻被监听到
        document.querySelectorAll(".video-list-item").forEach(item => {
          observer.observe(item) // 同一实例多次调用会被自动去重
        })
      }, 1000)
    }
  })
}

const initHls = () => {
  hls.value = new Hls({
    maxBufferHole: 1,
    highBufferWatchdogPeriod: 10,
    maxBufferLength: 60,
    backBufferLength: 30,
    liveBackBufferLength: 30,
    autoStartLoad: true,
  })
}

// 播放 M3U8 文件
async function playM3U8(data: any) {
  const base64Encoded = base64EncodeM3U8(data)
  const dataUrl = `data:application/vnd.apple.mpegurl;base64,${base64Encoded}`

  if (Hls.isSupported()) {
    hls.value.loadSource(dataUrl)
    hls.value.attachMedia(videoElement.value!)

    hls.value.on(
      Hls.Events.ERROR,
      (event: any, data: { type: string; details: string; fatal: any }) => {
        console.error("HLS.js 错误:", event, data)

        // 对于非致命错误，特别是与缓冲相关的错误，也可以尝试恢复
        if (
          data.type === Hls.ErrorTypes.MEDIA_ERROR &&
          (data.details === "bufferStalledError" || data.details === "bufferNudgeOnStall")
        ) {
          hls.value.recoverMediaError()

          // 尝试恢复后播放
          videoElement.value!.play().catch(err => {
            console.error("恢复后播放失败，请用户交互后再播放", err)
          })
        } else if (data.fatal) {
          // 对于致命错误
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log("网络错误，尝试重连...")
              hls.value.startLoad()
              break
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log("媒体错误，尝试恢复...")
              hls.value.recoverMediaError()
              break
            default:
              console.error("未知致命错误，停止加载")
              hls.value.destroy()
              break
          }
        }
      }
    )
    if (shouldAutoplay.value) {
      // M3U8 加载完成，尝试播放
      hls.value.on(Hls.Events.MANIFEST_PARSED, () => {
        videoElement.value!.play().catch(err => {
          console.error("首次播放失败（可能需要用户交互）", err)
        })
      })
    }
  } else if (videoElement.value!.canPlayType("application/vnd.apple.mpegurl")) {
    videoElement.value!.src = dataUrl
    videoElement.value!.load()

    videoElement.value!.addEventListener("loadedmetadata", () => {
      if (videoElement.value!.readyState >= 2) {
        videoElement.value!.play().catch(err => {
          console.error("iOS 自动播放失败，请用户点击播放", err)
        })
      }
    })
  } else {
    console.error("该浏览器不支持 HLS")
  }
}

// 获取m3u8视频地址
const getM3u8Url = (item: syncData) => {
  downloadFile(item.bucket, item.endpoint, `${item.path}`).then((res: any) => {
    playM3U8(res)
  })
}

const clickItem = async (item: syncData) => {
  if (showDel.value) {
    if (clcikNum.value === 1) {
      filterCheck()
    }
    if (item.active) {
      state.videoList.some(k => {
        if (k.path === item.path) {
          item.active = false
          return
        }
      })
      if (checkList.value.length > 1) {
        checkList.value.some((i, index) => {
          if (i === item.path) {
            checkList.value.splice(index, 1)
            return
          }
        })
      } else {
        checkList.value = []
      }
    } else {
      checkList.value.push(item.path)
      item.active = true
    }
    clcikNum.value = 0
  } else {
    filterCheck()
    item.active = true
    shouldAutoplay.value = true // 设置点击时自动播放
    if (item.is_sync === 0) {
      device_sn.value = item.device_sn
      sendPlayback(item)
    } else {
      getM3u8Url(item)
    }
  }
}

// 设置初始状态
const filterCheck = (status = false) => {
  state.videoList.forEach(data => {
    data.active = status
    if (status) {
      checkList.value.push(data.path)
    }
  })
}

// 获取时间轴红线所处刻度位置 指向的视频数据下标
const svgDataScrollEmit = (index: any) => {
  const targetItem = document.querySelector(`.video-list-item[data-id="${index}"]`) as HTMLElement
  const targetTop = targetItem!.offsetTop || 0
  videoListCurrentScrollTop.value = targetTop
  // playbackListRight.value!.scrollTop = targetTop
  // ----------设置平滑滚动功能
  videoListScrolling.value = true
  let step = 5 // 用来定义平滑滚动速度
  let timeNum = 3 // 几毫秒自增一次
  let sub = Math.abs(playbackListRight.value!.scrollTop - targetTop) // 滚动条位置前后差值
  const maxScroll = ref<number>(
    playbackListRight.value!.scrollHeight - playbackListRight.value!.clientHeight
  ) // 最大可滚动高度
  if (sub > 200 && sub < 500) step = 10 // 根据差值大小设置不同滚动速度
  else if (sub >= 500) step = 30
  clearInterval(scrollTopSmoothTimer)
  if (sub > 5) {
    if (playbackListRight.value!.scrollTop < targetTop) {
      scrollTopSmoothTimer = setInterval(() => {
        playbackListRight.value!.scrollTop += step
        // 判断如果在滚动条最大值左右, 则停止自增
        const isLessThenMaxScroll =
          playbackListRight.value!.scrollTop < maxScroll.value + 5 &&
          playbackListRight.value!.scrollTop > maxScroll.value - 5
        if (playbackListRight.value!.scrollTop >= targetTop || isLessThenMaxScroll) {
          videoListScrolling.value = false
          clearInterval(scrollTopSmoothTimer)
        }
      }, timeNum)
    } else if (playbackListRight.value!.scrollTop > targetTop) {
      scrollTopSmoothTimer = setInterval(() => {
        playbackListRight.value!.scrollTop -= step
        if (playbackListRight.value!.scrollTop <= targetTop) {
          videoListScrolling.value = false
          clearInterval(scrollTopSmoothTimer)
        }
      }, timeNum)
    } else {
      clearInterval(scrollTopSmoothTimer)
    }
  } else {
    clearInterval(scrollTopSmoothTimer)
  }
  // -------------------------
}

const touchOpen = (isOpen: boolean) => {
  timeScaleTouchIsOpen.value = isOpen
}

const ressetList = () => {
  finished.value = false
  state.pagesNum = 1
  loading.value = true
  isInitialLoad.value = false
  isCheckAll.value = false
  checkList.value = []
  playbackListRight.value!.scrollTop = 0
  // 重置时间轴和视频列表的位置
  timeAxisDom.value?.resetDefaultPosi(true)
  timeScaleTouchIsOpen.value = true
  setTimeout(() => {
    timeScaleTouchIsOpen.value = false
  }, 100)
  onLoad()
}

const formatter = (day: any) => {
  if (!day.date) return day

  const formatDateToString = (date: Date): string => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, "0")
    const dayNum = date.getDate().toString().padStart(2, "0")
    return `${year}${month}${dayNum}`
  }

  const dateString = formatDateToString(day.date)
  const videoCount = state.videosCount[dateString]
  const isSelected = formatDate(day.date) === currentDate.value

  const classes = [videoCount && "has-videos", isSelected && "selected-day"]
    .filter(Boolean)
    .join(" ")

  return {
    ...day,
    bottomInfo: videoCount ? String(videoCount) : "",
    className: classes || undefined,
    text: day.date.getDate(),
  }
}

watch(config.video_path, (newValue, oldValue) => {
  if (newValue && state.videoList.length) {
    // 找到当前激活的视频项
    const activeItem = state.videoList.find(item => item.active === true)
    if (activeItem) {
      downloadFile(activeItem.bucket, activeItem.endpoint, newValue).then((res: any) => {
        playM3U8(res)
      })
      state.videoList.some(item => {
        if (item.device_sn === device_sn.value) {
          item.is_sync = 1
          return
        }
      })
    }
  }
})

onMounted(() => {
  initHls()
  connectWebSocket()
  config.video_state = 0
  isInitialLoad.value = true // 确保初始状态为true

  // 监听指定元素展示的目标群
  observer = new IntersectionObserver(entries => {
    // 时间轴触屏事件开启时，不进行监听计算
    entries.map((entry: any) => {
      const isView = entry.isIntersecting
      const id = entry.target.dataset.id
      const time = entry.target.innerText.split("\n\n")[0]
      if (isView) {
        videoItemTimes[id] = time
      } else {
        delete videoItemTimes[id]
      }
    })
    if (!timeScaleTouchIsOpen.value && !videoListScrolling.value) {
      timeAxisDom.value?.setCurrentPosi(Object.keys(videoItemTimes)[0], "index") // setCurrentPosi: 设置时间轴红线位置
    }
  })

  // 监听视频列表是否执行触屏事件
  playbackListRight.value?.addEventListener("touchstart", () => {
    videoListTouchIsOpen.value = true
    videoListScrolling.value = false
  })
  playbackListRight.value?.addEventListener("touchend", () => (videoListTouchIsOpen.value = false))
})

onUnmounted(() => {
  ws.value?.close()
  ws.value = null
  hls.value.off(Hls.Events.ERROR)
  // 解除监听
  if (observer) observer.disconnect()
})
</script>
<style lang="less" scoped>
.page {
  background-color: #ededed;
  min-height: 100vh;
}
.video-container {
  width: 100vw;
  background-color: #000;
  .video {
    width: 100vw;
    border: solid;
  }
}
.date-container {
  background-color: #fff;
  padding: 0 20px;
  margin: 0;
  height: 48px;
  line-height: 48px;
  font-size: 15px;
  color: #000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .video-num {
    color: #666;
    font-size: 13px;
  }
  .video-time {
    display: flex;
    align-items: center;
    border: 1px solid #d2d2d2;
    border-radius: 16px;
    height: 30px;
    padding-left: 18px;
    padding-right: 28px;
    position: relative;
    .data-icon {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translate(0, -50%);
    }
  }
}
.date-picker {
  position: fixed;
  bottom: 0;
  width: 100vw;
  z-index: 99;
}

.playback-list {
  display: flex;
  position: relative;
  .data-empty {
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translate(-50%);
    padding: 0 12px;
    height: 35px;
    background-color: rgb(62, 69, 104);
    font-size: 14px;
    color: #fff;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .playback-list-left {
    width: 25%;
    position: relative;
    padding-right: 0;
    height: calc(51.5vh + 8px);
    overflow-y: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;
    .time-pillar {
      position: absolute;
      right: 15px;
      top: 8px;
      height: 100%;
      width: 10px;
      background-color: #d9d9d9;
      pointer-events: none;
    }
    .time-item {
      height: 100px;
      .time-item-box {
        display: flex;
        justify-content: space-between;
        padding-left: 15px;
        align-items: center;
        .time-item-text {
          font-size: 13px;
          color: #666;
        }
        .time-item-xian {
          width: 20px;
          height: 1px;
          background: #666;
        }
      }
    }
  }
  .playback-list-left::-webkit-scrollbar {
    display: none;
  }
  .playback-list-right {
    width: 75%;
    height: 51.5vh;
    overflow-y: auto;
    padding-top: 8px;
    padding-right: 10px;
  }
  .delete-button {
    height: 100% !important;
  }
  .item {
    position: relative;
    display: flex;
    background-color: #fff;
    border-radius: 10px;
    padding: 5px 10px;
    box-sizing: border-box;
    margin-bottom: 8px;
    .left-container {
      position: relative;
      height: 74px;
      .video-cover {
        min-width: 110px;
        width: 120px;
        height: 74px;
        border-radius: 10px;
      }
      .duration {
        position: absolute;
        bottom: 12px;
        right: 12px;
        color: #fff;
        font-size: 13px;
        font-weight: 400;
        height: 20px;
        line-height: 20px;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 0 8px;
        border-radius: 10px;
      }
    }
    .right-container {
      margin-right: 10px;
      padding-top: 11px;
      .name {
        margin: 0;
        font-size: 15px;
        font-weight: 700;
        min-height: 17px;
      }
      .day-time {
        font-size: 14px;
        color: #666;
      }
      .day-time-pir {
        color: #1989fa;
      }
      .dian {
        position: absolute;
        right: 10px;
        top: 8px;
        width: 2px;
        height: 2px;
        background-color: #1989fa;
        border-radius: 50%;
      }
      .pir-imgs {
        width: 30px;
        height: 30px;
      }
      .wifi-imgs {
        width: 32px;
        height: 30px;
      }
    }
    .download-icon {
      position: absolute;
      right: 12px;
      bottom: 12px;
      height: 30px;
      width: 30px;
      background: url(../assets/playback/download-icon.png) no-repeat 100% / cover;
    }
  }
  .active {
    border: 1px solid #1989fa;
  }
}
.video-player {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.video-element {
  width: 100%;
  height: auto;
}
.del-modal {
  position: fixed;
  bottom: 0;
  width: 80%;
  padding: 0 10%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #333;
  height: 0;
  overflow: hidden;
  transition: height 0.3s ease-in-out;
}
.del-modals {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 35%;
  padding: 20px 20px 10px 20px;
  margin: 0 auto;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  .progress-test {
    font-size: 16px;
    color: #fff;
    padding-top: 10px;
    text-align: center;
  }
}
.open {
  height: 80px;
}
.van-checkbox {
  margin-right: 10px;
}
:deep(.van-calendar__popup) {
  height: auto !important;
}
:deep(.van-calendar) {
  .van-calendar__day {
    .van-calendar__bottom-info {
      color: #1e6fe8;
      font-size: 10px;
    }

    &.has-videos {
      .van-calendar__bottom-info {
        color: #1e6fe8;
        font-weight: 500;
      }
    }

    &.selected-day {
      &.has-videos {
        .van-calendar__bottom-info {
          color: #fff;
        }
      }
    }
  }
}
</style>
