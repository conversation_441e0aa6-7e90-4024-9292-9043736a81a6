<template>
  <div class="pages">
    <Header title="设备重置" :left-type="1" />
    <div class="container">
      <div class="center-box">
        <img class="tip-img" :src="config.add_device_item.network_config_img" alt="" />
      </div>
      <div class="tip-text">
        press and hold the device button for 8 seconds until the light is flashing red.
      </div>
    </div>
    <div class="footer-btn">
      <van-button
        type="primary"
        round
        size="large"
        color="linear-gradient(90deg,#4774F5 3.73%,#4977F4 100%)"
        @click="deviceReset"
      >
        是
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import config from "@/config";
import { useRouter } from "vue-router"

const router = useRouter()

const deviceReset = () => {
  router.push("networkPrompt")
}
</script>

<style lang="less" scoped>
.container {
  padding: 20px;
  .center-box {
    height: 387px;
    margin-bottom: 25px;
    display: flex;
    justify-content: center;
    .tip-img {
      max-width: 100%;
      height: 100%;
    }
  }
  .tip-text {
    font-size: 18px;
    color: #333333;
    text-align: center;
    line-height: 25px;
  }
}
.footer-btn {
  position: absolute;
  bottom: 50px;
  width: 89%;
  margin: 0 auto;
  padding: 0px 20px;
  left: 0px;
}
</style>
