<template>
  <div class="extend-box">
    <div class="extend-box-top">
      <img
        class="extend-img"
        src="https://cache.gdxp.com/acce/assets/left.png"
        alt=""
      />
      <div class="extend-text">{{ $t('home.freeTrial') }}</div>
      <img
        class="extend-img"
        src="https://cache.gdxp.com/acce/assets/right.png"
        alt=""
      />
    </div>
    <div class="alarm">
      <div class="alarm-img">{{ countDay || '0' }}</div>
      <van-count-down :time="countTime" millisecond>
        <template #default="timeData">
          <span class="block">{{ timeData.hours }}</span>
          <span class="colon">:</span>
          <span class="block">{{ timeData.minutes }}</span>
          <span class="colon">:</span>
          <span class="block">{{ timeData.seconds }}</span>
          <span class="colon">.</span>
          <span class="blocks">{{ getTimeData(timeData.milliseconds) }}</span>
        </template>
      </van-count-down>
    </div>
    <div class="num-day">
      <div v-if="cloudStorage.price">
        <div class="del-text">
          <del class="num-texta">${{ cloudStorage.price1 || '0.00' }}</del>
        </div>
        <div class="num-texts">${{ cloudStorage.price2 || '0.00' }}</div>
      </div>
      <div v-else-if="cloudStorage.first_price_usd" class="num-textd">
        ${{ cloudStorage.first_price_usd
        }}<span class="num-spana">/{{ $t('home.month') }}</span>
      </div>
      <div v-else class="num-text">
        ${{ cloudStorage.price || '0.00' }}/{{ $t('home.day') }}
      </div>
      <div class="num-link">
        <div>
          <div class="nums">
            {{ cloudStorage.service_days || '365' }} {{ $t('home.days') }}
          </div>
          <div class="dss">{{ $t('home.plan') }}</div>
        </div>
        <div>
          <div class="nums">
            {{ cloudStorage.cycle_days || '30' }} {{ $t('home.days') }}
          </div>
          <div class="dss">{{ $t('home.history') }}</div>
        </div>
      </div>
    </div>
    <div class="num-tip">
      <div class="num-tip-item">
        <img
          class="num-tip-img"
          src="https://cache.gdxp.com/acce/assets/one.png"
          alt=""
        />
        <div class="num-item-text">{{ $t('home.move') }}</div>
      </div>
      <div class="num-tip-item">
        <img
          class="num-tip-img"
          src="https://cache.gdxp.com/acce/assets/two.png"
          alt=""
        />
        <div class="num-item-text">{{ $t('home.humanoid') }}</div>
      </div>
      <div class="num-tip-item">
        <img
          class="num-tip-img"
          src="https://cache.gdxp.com/acce/assets/tre.png"
          alt=""
        />
        <div class="num-item-text">{{ $t('home.military') }}</div>
      </div>
      <div class="num-tip-item">
        <img
          class="num-tip-img"
          src="https://cache.gdxp.com/acce/assets/fou.png"
          alt=""
        />
        <div class="num-item-text">{{ $t('home.stabilize') }}</div>
      </div>
    </div>
    <div class="num-footer" @click="subscription">
      {{ $t('home.subscription') }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
  cloudStorage: {
    type: Object,
    default: {},
  },
  expiryTime: {
    type: Number,
    default: 0,
  },
})

// 过滤毫秒为一位
function getTimeData(data: any) {
  let time: any
  let day: any = String(data)
  time = parseInt(String(day / 100))
  return time
}

// 倒计时
const countTime = ref(props.expiryTime * 1000)
// 倒计时天数
const countDay = ref(parseInt(String(props.expiryTime / 86400)))

// 立即订阅
const subscription = () => {
  // 区分什么终端
  const str = window.navigator.userAgent
  const windows: any = window
  if (!!str.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
    // ios端
    windows.webkit.messageHandlers.click_subscribe.postMessage({
      ...props.cloudStorage,
    })
  } else if (str.indexOf('Android') > -1 || str.indexOf('Adr') > -1) {
    // android端
    windows.android.toPurchase(
      props.cloudStorage.cycle_days,
      props.cloudStorage.service_days,
      props.cloudStorage.trial_days,
      props.cloudStorage.is_trial,
      '',
      ''
    )
  }
}
</script>

<style lang="less" scoped>
.extend-box {
  background-color: #222;
  border: 1px solid #ffcc99;
  border-radius: 10px;
  padding: 10px;
  .extend-box-top {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    .extend-img {
      width: 39px;
    }
    .extend-text {
      font-size: 18px;
      color: #f5deb3;
    }
  }
  .alarm {
    display: flex;
    height: 50px;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    .alarm-img {
      width: 44px;
      height: 44px;
      background-image: url('https://cache.gdxp.com/acce/assets/nz.png');
      background-position: center;
      background-repeat: no-repeat;
      background-size: 100%;
      font-size: 24px;
      color: #f5deb3;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
    }
  }
  .num-day {
    width: 250px;
    height: 132px;
    background-image: url('https://cache.gdxp.com/acce/assets/bj.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin: auto;
    .num-text {
      padding-top: 30px;
      font-size: 36px;
      text-align: center;
      color: #b8860b;
      margin-bottom: 10px;
    }
    .del-text {
      display: flex;
      justify-content: center;
    }
    .num-texta {
      padding-top: 10px;
      font-size: 32px;
      text-align: center;
      color: #b8860b;
      margin-bottom: 2px;
      opacity: 0.4;
    }
    .num-texts {
      font-size: 23px;
      text-align: center;
      color: #b8860b;
      margin-bottom: 10px;
    }
    .num-textd {
      padding-top: 30px;
      font-size: 36px;
      text-align: center;
      color: #333;
      margin-bottom: 10px;
      .num-spana {
        font-size: 15px;
      }
    }
    .num-link {
      display: flex;
      justify-content: space-around;
      .nums {
        font-size: 20px;
        line-height: 20px;
        color: #333;
        font-weight: 400;
        margin-bottom: 5px;
      }
      .dss {
        font-size: 15px;
        color: #333;
      }
    }
  }
  .num-tip {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    padding-top: 10px;
    .num-tip-item {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      .num-tip-img {
        width: 45px;
        height: 45px;
        margin-bottom: 5px;
      }
      .num-item-text {
        font-size: 12px;
        color: #f5deb3;
        font-weight: 100;
        padding: 0px 5px;
        text-align: center;
      }
    }
  }
  .num-footer {
    width: 260px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #333;
    background-image: url('https://cache.gdxp.com/acce/assets/btn.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
    font-weight: bold;
    margin: 0 auto;
  }
}
.block {
  display: inline-block;
  width: 25px;
  height: 32px;
  color: #fff;
  font-size: 16px;
  text-align: center;
  background-color: #333;
  line-height: 32px;
  border-radius: 4px;
}
.colon {
  color: #fff;
  margin: 0px 5px;
  font-size: 15px;
  font-weight: 600;
}
.blocks {
  display: inline-block;
  width: 32px;
  height: 32px;
  color: #fff;
  font-size: 16px;
  text-align: center;
  background-color: #ff8888;
  line-height: 32px;
  border-radius: 5px;
}
</style>
