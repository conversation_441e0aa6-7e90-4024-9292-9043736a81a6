<template>
  <div class="rate-title">{{ $t('home.thisService') }}</div>
  <div class="rate-box">
    <van-rate
      v-model="survey.star"
      :gutter="10"
      @change="rateChage"
      :size="26"
      color="#33CC99"
      void-icon="star"
      void-color="#ccc"
      :readonly="timeFirrd"
    />
  </div>
  <div class="rate-text">{{ $t('home.solved') }}</div>
  <div class="rate-data">
    <div
      class="rate-add"
      :class="survey.solved === 1 ? 'active-add' : ''"
      @click="openData(1)"
    >
      <img
        v-if="survey.solved === 1"
        class="rate-img"
        src="https://cache.gdxp.com/acce/assets/dzb.png"
        alt=""
      />
      <img
        v-else
        class="rate-img"
        src="https://cache.gdxp.com/acce/assets/dz.png"
        alt=""
      />
      <div>{{ $t('home.yes') }}</div>
    </div>
    <div
      class="rate-add"
      :class="survey.solved === 0 ? 'active-add' : ''"
      @click="openData(0)"
    >
      <img
        v-if="survey.solved === 0"
        class="rate-img"
        src="https://cache.gdxp.com/acce/assets/bzb.png"
        alt=""
      />
      <img
        v-else
        class="rate-img"
        src="https://cache.gdxp.com/acce/assets/bz.png"
        alt=""
      />
      <div>{{ $t('home.no') }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, toRefs, onMounted, ref } from 'vue'
import { saveAppraise } from '@/api'
import moment from 'moment'
import config from '@/config'
import { debounce } from '@/utils'

const props = defineProps({
  survey: {
    type: Object,
    default: {},
  },
  msgId: Number,
})

const state = reactive({
  survey: props.survey,
})
const { survey } = toRefs(state)

const timeFirrd = ref(false)

const openData = debounce((index: any) => {
  timeFirrd.value = !(localStorage.getItem('evaluateTime') &&
      moment(localStorage.getItem('evaluateTime')).diff(
          moment().format('YYYY-MM-DD HH:mm:ss'),
          'minute'
      ) > -2 &&
      moment(localStorage.getItem('evaluateTime')).diff(
          moment().format('YYYY-MM-DD HH:mm:ss'),
          'minute'
      ) < 1);
  if (timeFirrd.value) return
  state.survey.solved = index
  fetch()
}, 200)

const rateChage = () => {
  fetch()
}

// 两分钟之内修改评价
function fetch() {
  saveAppraise(
    config.sessionId,
    props.msgId,
    state.survey.star,
    state.survey.solved
  )
}

onMounted(() => {
  timeFirrd.value = !(localStorage.getItem('evaluateTime') &&
      moment(localStorage.getItem('evaluateTime')).diff(
          moment().format('YYYY-MM-DD HH:mm:ss'),
          'minute'
      ) > -2 &&
      moment(localStorage.getItem('evaluateTime')).diff(
          moment().format('YYYY-MM-DD HH:mm:ss'),
          'minute'
      ) < 1);
})
</script>

<style lang="less" scoped>
.rate-title {
  font-size: 15px;
  color: #333;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.rate-box {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}
.rate-text {
  font-size: 14px;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}
.rate-data {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  .rate-add {
    width: 70px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #999;
    font-size: 15px;
    color: #999;
    border-radius: 20px;
    margin-right: 15px;
    .rate-img {
      width: 15px;
      height: 15px;
      margin-right: 2px;
    }
  }
}
.active-add {
  background-color: #33cc99;
  color: #fff !important;
  border: none !important;
}
</style>
