{"name": "aiwt-app-h5", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "build:dev": "vite build --mode development"}, "dependencies": {"axios": "^1.6.7", "js-md5": "^0.8.3", "qs": "^6.11.2", "vant": "^4.8.5", "vue": "^3.4.15", "vue-router": "^4.2.5"}, "devDependencies": {"@tsconfig/node20": "^20.1.2", "@types/node": "^20.11.10", "@types/qs": "^6.9.12", "@vitejs/plugin-vue": "^5.0.3", "@vue/tsconfig": "^0.5.1", "npm-run-all2": "^6.1.1", "sass": "^1.71.1", "sass-loader": "^14.1.1", "typescript": "~5.3.0", "vite": "^5.0.11", "vue-i18n": "^9.10.1", "vue-tsc": "^1.8.27"}}