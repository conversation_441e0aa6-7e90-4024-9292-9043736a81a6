<template>
  <div class="security-reporting">
    <h1>Vulnerability Report System Overview</h1>
    <p class="">
      Our Vulnerability Report System is designed to enhance the security and
      reliability of our Aiwit App
      <img
        style="width: 16px; vertical-align: sub"
        src="../assets/logo.png"
        alt=""
        srcset=""
      />. This system provides a structured and transparent process for
      reporting and addressing security vulnerabilities identified by users,
      security researchers, or other stakeholders.
    </p>

    <h2>Purpose</h2>
    <p class="">The purpose of the Vulnerability Report System is to:</p>
    <p class="">
      1.Enable users and researchers to report potential vulnerabilities quickly
      and efficiently.
    </p>
    <p class="">
      2. Ensure timely review, validation, and resolution of reported issues.
    </p>
    <p class="">
      3. Foster collaboration between our development team and the wider
      security community.
    </p>
    <p class="">
      4. Build trust by maintaining a secure platform for all our customers.
    </p>

    <h2>How It Works</h2>
    <p class="">1. Submission of Reports</p>
    <p class="">
      • Users or researchers can submit vulnerability reports via the dedicated
      Vulnerability Reporting Form available on our website.
    </p>
    <p class="">
      • The form collects details such as the reporter’s contact information, a
      description of the issue, steps to reproduce it, and supporting evidence
      (e.g., screenshots, logs).
    </p>

    <p class="">2. Acknowledgment</p>
    <p class="">
      • Upon submission, an acknowledgment email is sent to the reporter,
      confirming receipt of the report.
    </p>

    <h2>3. Review and Triage</h2>
    <p class="">
      • Our security team evaluates the report to verify the issue and assess
      its severity.
    </p>
    <p>
      • Reports are classified based on the Common Vulnerability Scoring System
      (CVSS) to prioritize fixes.
    </p>

    <h2>4. Resolution</h2>
    <p class="">
      • Verified vulnerabilities are assigned to the appropriate development
      team for resolution.
    </p>
    <p class="">
      • The team works on implementing fixes and rigorously tests the solutions
      to ensure the issue is resolved without introducing new vulnerabilities.
    </p>

    <h2>5. Feedback and Closure</h2>
    <p class="">• Once resolved, the reporter is notified of the outcome.</p>
    <p class="">
      • If applicable, we express our gratitude by offering contributors a
      brand-new device as a token of appreciation for their effort and
      contribution to our platform’s security.
    </p>

    <h2>Scope of Reports</h2>
    <p class="">We welcome vulnerability reports related to:</p>
    <p class="">• Authentication and authorization issues.</p>
    <p class="">• Data breaches or privacy concerns.</p>
    <p class="">• Application or API security flaws.</p>
    <p class="">
      • Network-related vulnerabilities specific to the doorbell camera.
    </p>
    <p class="">
      • Other issues that may compromise the security or functionality of our
      app.
    </p>

    <h2>Exclusions</h2>
    <p class="">
      Reports regarding the following may not be eligible for resolution under
      this program:
    </p>
    <p class="">• General user support or feature requests.</p>
    <p class="">• Previously reported issues.</p>
    <p class="">• Vulnerabilities in third-party tools or libraries.</p>

    <h2>Responsible Disclosure Guidelines</h2>
    <p class="">
      To ensure the safety of our users, we ask that all reporters adhere to
      responsible disclosure practices:
    </p>
    <p class="">
      • Avoid exploiting the vulnerability beyond what is necessary to
      demonstrate its presence.
    </p>
    <p class="">
      • Refrain from sharing details of the vulnerability publicly until it has
      been resolved.
    </p>
    <p class="">
      • Allow us sufficient time to investigate and address the issue
    </p>

    <h2>Contact Us</h2>
    <p class="">
      If you have identified a vulnerability, please report it via our
      Vulnerability Reporting Form or email our security team directly at<a
        href="mailto:<EMAIL>"
        rel="noopener noreferrer"
        target="_blank"
      >
        <EMAIL>
      </a>
    </p>
    <p class="">
      We value your contributions to improving the security of our platform and
      appreciate your partnership in maintaining the safety of our customers.
    </p>
    <div class="submit-button-container">
      <div @click="handleEnterSubmit" class="submit-button pointer">
        Submit Report
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Headers from "@/components/Headers.vue";
import { showToast } from "vant";
import { useRouter } from "vue-router";
const router = useRouter();
const nav = (url: string) => {
  router.push(url);
};
const handleEnterSubmit = () => {
  // if (localStorage.getItem("isLogin") === "true") {
  nav("/VulnerabilityReport");
  // } else {
  //   showToast("Please log in first");
  //   setTimeout(() => {
  //     nav("/login");
  //   }, 1500);
  // }
};
</script>
<style lang="less" scoped>
@import "../styles/common.less";
.security-reporting {
  color: rgb(29, 29, 31);
  font-size: 18px;
  background-color: #fff;
  line-height: 23px;
  width: 75%;
  margin: 0 auto;
  padding-top: @ptt;

  h1 {
    text-align: center;
    color: #000000d9;
    font-size: 24px;
    font-weight: 600;
    line-height: 26px;
  }

  h2 {
    font-weight: 600;
    margin-top: 16px;
    margin-bottom: 16px;
    line-height: 25px;
    font-size: 20px;
    color: #000000d9;
  }

  .mt16 {
    margin-top: 16px;
  }

  a {
    color: #00ace7;
  }
  .submit-button-container {
    display: flex;
    justify-content: center;
    .submit-button {
      background-color: #0071e3;
      border-radius: 980px;
      display: inline-block;
      color: #fff;
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      margin: 50px auto;
      padding: 15px 30px;
    }
  }
}
.pointer {
  cursor: pointer;
}
</style>
