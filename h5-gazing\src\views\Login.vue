<template>
  <div class="pages">
    <div class="login-top">
      <div class="login-title">
        <div>{{ "Welcome" }}</div>
        <div>{{ loginType === "1" ? "登录" : "注册" }}</div>
      </div>
      <div class="login-img"><img src="../assets/logo.png" alt="logo" /></div>
    </div>
    <div class="input-one">
      <div class="input-wrapper" v-if="loginType === '1'">
        <div
          class="floating-label"
          v-if="!username && isUsernameFocused"
          :class="{ active: isUsernameFocused }"
        >
          输入邮箱
        </div>
        <input
          class="input-name"
          v-model="username"
          name="userName"
          placeholder="输入邮箱"
          type="text"
          ref="usernameInput"
          @focus="isUsernameFocused = true"
          @blur="isUsernameFocused = false"
        />
        <van-icon v-show="username" name="clear" class="clear-icon" @click="clearUsername" />
      </div>
      <div class="input-wrapper" v-else>
        <div
          class="floating-label"
          v-if="!username && isUsernameFocused"
          :class="{ active: isUsernameFocused }"
        >
          输入邮箱
        </div>
        <input
          class="input-name"
          v-model="username"
          placeholder="输入邮箱"
          name="userName"
          type="text"
          @focus="isUsernameFocused = true"
          @blur="isUsernameFocused = false"
        />
      </div>
    </div>
    <div class="input-one" v-if="loginType === '2'">
      <div class="input-wrapper">
        <div
          class="floating-label"
          v-if="!re_email && isReEmailFocused"
          :class="{ active: isReEmailFocused }"
        >
          确认邮箱
        </div>
        <input
          class="input-name"
          v-model="re_email"
          placeholder="确认邮箱"
          name="re_email"
          type="text"
          @focus="isReEmailFocused = true"
          @blur="isReEmailFocused = false"
        />
      </div>
    </div>
    <div class="input-one">
      <div class="input-wrapper" v-if="loginType === '1'">
        <div
          class="floating-label"
          v-if="!password && isPasswordFocused"
          :class="{ active: isPasswordFocused }"
        >
          输入密码
        </div>
        <input
          class="input-name"
          v-model="password"
          name="password"
          placeholder="输入密码"
          :type="showPassword ? 'text' : 'password'"
          @focus="isPasswordFocused = true"
          @blur="isPasswordFocused = false"
        />
        <van-icon
          :name="showPassword ? 'eye-o' : 'closed-eye'"
          class="clear-icon"
          @click="showPassword = !showPassword"
        />
      </div>
      <div class="input-wrapper" v-else>
        <div
          class="floating-label"
          v-if="!password && isPasswordFocused"
          :class="{ active: isPasswordFocused }"
        >
          输入密码
        </div>
        <input
          class="input-name"
          v-model="password"
          name="password"
          type="password"
          placeholder="输入密码"
          @focus="isPasswordFocused = true"
          @blur="isPasswordFocused = false"
        />
      </div>
    </div>
    <div class="remember-reset">
      <van-checkbox icon-size="14" v-if="loginType === '1'" v-model="config.rememberAccount.value">
        记住账号
      </van-checkbox>
      <router-link class="reset-pass" to="/resetPass" v-if="loginType === '1'">
        忘记密码
      </router-link>
    </div>
    <div class="login-sub">
      <van-button class="login-btn" round block type="primary" color="#3D5AFD" @click="onSubmit">
        {{ loginType === "1" ? "登录" : "注册" }}
      </van-button>
    </div>
    <div class="footer-reg">
      <van-button class="reg-btn" round type="primary" @click="tabLogin">
        {{ loginType === "1" ? "注册" : "登录" }}
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"
import type { loginData, registerData } from "@/api/base"
import { submitRegister, submitLogin } from "@/api/index"
import config from "@/config"
import { showToast, showFailToast } from "vant"
import { MQTTInit } from "@/components/WebRTCClient"
import { init_params, reloadMall } from "@/utils"
import { useVConsole } from "@/components/useVConsole"

const route = useRoute()
const router = useRouter()
const username = ref<string>("")
const re_email = ref<string>("")
const password = ref<any>("")
const loginType = ref<string>(String(route.params.type))
const usernameInput = ref<HTMLInputElement | null>(null)
const showPassword = ref<boolean>(false)
const isUsernameFocused = ref(false)
const isReEmailFocused = ref(false)
const isPasswordFocused = ref(false)

const tabLogin = () => {
  if (loginType.value === "1") {
    loginType.value = "2"
  } else if (loginType.value === "2") {
    loginType.value = "1"
    if (localStorage.getItem("username")) {
      username.value = localStorage.getItem("username") || ""
      config.rememberAccount.value = true
    }
  }
}

const onSubmit = () => {
  if (!username.value)
    return showToast(`请输入您要${loginType.value === "2" ? "注册" : "登录"}的邮箱`)
  if (loginType.value === "2") {
    if (!re_email.value) return showToast("请输入确认邮箱后再提交")
    if (username.value !== re_email.value) return showToast("输入邮箱不一致,请重新输入")
    const params = {
      email: username.value,
      password: String(password.value),
    } as registerData
    saveRegister(params)
  } else {
    if (!password.value) return showToast("请输入密码后再提交")
    const params = {
      username: username.value,
      password: String(password.value),
    } as loginData
    saveLogin(params)
  }
}

const clearUsername = () => {
  username.value = ""
  usernameInput.value?.focus()
}

const saveRegister = (params: registerData) => {
  submitRegister(params).then((res: any) => {
    if (res.code === 0) {
      config.key = res.data.qr_key
      loginType.value = "1"
      router.push("/activate/1")
    }
  })
}

const saveLogin = (params: loginData) => {
  submitLogin(params).then((res: any) => {
    if (res.code === 0) {
      if (res.data) {
        config.token = res.data.token
        config.user_id = res.data.user_id
        config.username.value = res.data.username
        config.mqtt_status = 1
        localStorage.setItem("token", res.data.token)
        localStorage.setItem("user_id", res.data.user_id)
        localStorage.setItem("username", res.data.username)
        localStorage.setItem("role", res.data.role)
        if (res.data.role === 1) {
          useVConsole(1)
        }
        reloadMall()
        MQTTInit()
        setTimeout(() => {
          router.push("/home")
        }, 600)
      }
    } else {
      showFailToast(res.msg)
    }
  })
}

onMounted(() => {
  window.init_params = init_params
  // 只在登录模式下读取记住的账号
  if (loginType.value === "1" && localStorage.getItem("username")) {
    username.value = localStorage.getItem("username") || ""
    config.rememberAccount.value = true
  }
})
</script>

<style lang="less" scoped>
:deep(.van-checkbox__label) {
  margin-left: 5px;
}
.pages {
  padding: 0 10%;
}
.login-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 120px;
  margin-bottom: 15px;

  .login-img {
    img {
      width: 80px;
      height: 80px;
    }
  }

  .login-title {
    font-size: 28px;
    font-weight: bold;
    color: #000;
    text-align: left;

    div {
      line-height: 1.5;
    }
  }
}

.input-one {
  margin-bottom: 20px;

  .input-wrapper {
    position: relative;
    width: 100%;

    .floating-label {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 14px;
      color: #999;
      transition: all 0.3s;
      padding: 2px;
      pointer-events: none;
      background-color: transparent;

      &.active {
        top: 0px;
        font-size: 12px;
        color: #1e6fe8;
        background-color: #e2e3e5;
      }
    }

    .clear-icon {
      position: absolute;
      right: 12px; // 改为35px使图标靠近输入框右边
      top: 50%;
      transform: translateY(-50%);
      color: #999;
      font-size: 16px;
      padding: 5px;
      cursor: pointer;
      z-index: 1;

      &:active {
        opacity: 0.8;
      }
    }
  }

  .input-name {
    width: 100%;
    height: 45px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    padding-left: 16px;
    padding-right: 40px;
    border-radius: 26px;
    border: 1px solid #dddddd;
    font-size: 14px;
    color: #1e6fe8;
    transition: border-color 0.3s;

    &:focus {
      border-color: #1e6fe8;
      border-width: 2px;
      outline: none;

      & + .floating-label {
        color: #1e6fe8;
      }
    }
  }

  // 分离出登录模式下的输入框特殊样式
  .input-wrapper .input-name {
    width: 83%;
  }
}

.remember-reset {
  display: flex;
  justify-content: space-between;
  align-items: center;

  :deep(.van-checkbox) {
    color: #666;
    font-size: 12px;

    .van-checkbox__icon {
      border-radius: 4px;

      .van-icon {
        border-radius: 4px;
      }
    }
  }
}

.reset-pass {
  padding: 0 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 26px;
  background-color: #e9f0fa;
  border-radius: 15px;
  color: #1e6fe8;
  font-size: 12px;
}

.login-sub {
  padding-top: 50px;
  margin-bottom: 20px;
  .login-btn {
    font-size: 16px;
    font-weight: bold;
    height: 50px;
  }
}

.footer-reg {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 16px;
  font-weight: bold;

  .reg-btn {
    background-color: #fff;
    border-color: #fff;
    height: 30px;
    color: #222;
    padding: 0 20px;
  }
}
</style>
