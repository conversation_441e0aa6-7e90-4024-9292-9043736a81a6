<template>
  <div class="software-updates">
    <h1>Product Support Policy Overview</h1>
    <p>
      We are dedicated to delivering ongoing security updates for our camera
      products. These updates typically include the latest security patches,
      fixes for identified vulnerabilities, and enhancements to system security
      and stability.
    </p>
    <p>
      To help you determine whether your device is eligible for continued
      security updates, we will regularly publish and maintain an updated list
      of supported products below.
    </p>
    <div class="table-container">
      <table border="1">
        <thead>
          <tr>
            <th scope="col">Product</th>
            <th scope="col">Model</th>
            <th scope="col">Version of Software</th>
            <th scope="col">Last release time</th>
            <th scope="col">Support period</th>
            <th scope="col">Guaranteed updates until</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in tableData" :key="item.id">
            <td>{{ item.product_type }}</td>
            <td>{{ item.model }}</td>
            <td>{{ item.hardware_version }}</td>
            <td>{{ item.last_release_time }}</td>
            <td>{{ item.support_cycle }}</td>
            <td>{{ item.end_update_datetime }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <p>Disclaimer:</p>
    <p>
      1. The information on this page is provided for reference only. Please
      refer to the actual security updates received on your device for the most
      accurate details
    </p>
    <p>
      2. Delivery timelines for security patches may vary depending on factors
      such as region and device model.
    </p>
    <p>
      3. Security update policies, including the list of supported devices, are
      subject to change and will be reviewed periodically.
    </p>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import Headers from "@/components/Headers.vue";
const tableData: any = ref(null);

const fetchData = async () => {
  try {
    const response = await fetch(
      "https://company.ekenelectronics.com/api/get_wz_product_support_info?page=1&limit=30"
    );
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const result = await response.json();
    tableData.value = result.data;
  } catch (err) {}
};
onMounted(() => {
  fetchData();
});
</script>
<style lang="less" scoped>
@import "../styles/common.less";

.software-updates {
  background-color: #fff;
  color: #282828;
  font-size: 18px;
  line-height: 1.8;
  padding-top: @ptt;
  padding-bottom: 30px;
  width: 75%;
  margin: 0 auto;

  h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 20px 0 16px;
  }

  .table-container {
    margin: 30px 0 50px;
    overflow-x: scroll;

    table {
      border-collapse: collapse;
      border: 1px solid #ccc;
      width: 95%;

      th {
        font-size: 20px;
        font-weight: 600;
      }

      td {
        padding: 0 6px;
        text-align: center;
        min-width: 140px;
      }
    }
    /* 全局滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px; /* 垂直滚动条的宽度 */
      height: 6px; /* 水平滚动条的高度 */
    }

    /* 滚动条轨道样式 */
    &::-webkit-scrollbar-track {
      background: transparent; /* 轨道背景色 */
      border-radius: 6px; /* 轨道圆角 */
    }

    /* 滚动条滑块样式 */
    &::-webkit-scrollbar-thumb {
      background: #888; /* 滑块背景色 */
      border-radius: 6px; /* 滑块圆角 */
    }

    /* 滑块 hover 样式 */
    &::-webkit-scrollbar-thumb:hover {
      background: #555; /* 滑块 hover 时的背景色 */
    }
  }
}
</style>
