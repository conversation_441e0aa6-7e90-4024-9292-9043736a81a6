<template>
  <div class="pages">
    <Header title="修改设备名称" :left-type="1" />
    <div class="from-data">
      <div class="from-title">设备名称</div>
      <input class="footer-input" v-model.trim="textValue" type="text" />
      <div class="goods-list">
        <div
          class="goods-item"
          v-for="(item, index) in nameList"
          :key="index"
          @click="setName(item)"
        >
          {{ item }}
        </div>
      </div>
      <van-button
        type="primary"
        class="primary-btn"
        color="#3D5AFD"
        @click="sendClick"
        size="large"
        round
      >
        确定
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import { ref, onActivated } from "vue"
import config from "@/config"
import { editDevice } from "@/api/index"
import { showToast } from "vant"
import { useRouter } from "vue-router"
import { useHomeStore } from "@/stores/home"

const router = useRouter()
const homeStore = useHomeStore()
const nameList = ref(["客厅", "卧室", "后花园", "后院", "前门", "办公室"])
const textValue = ref("")

const setName = (item: any) => {
  if (textValue.value === item) return
  textValue.value = item
}

const sendClick = async () => {
  const res: any = await editDevice(config.product_key, config.device_sn, textValue.value)
  if (res.code === 0) {
    showToast("修改成功")
    config.device.nickname = textValue.value
    setTimeout(() => {
      router.push("/home")
    }, 2000)
  }
}

onActivated(() => {
  textValue.value = config.device.nickname
})
</script>

<style lang="less" scoped>
.from-data {
  padding: 50px 20px 0px 20px;
  .from-title {
    font-size: 16px;
    color: #222;
    font-weight: 700;
    margin-bottom: 10px;
  }
  .footer-input {
    width: 95%;
    height: 50px;
    border-radius: 25px;
    padding-left: 25px;
    border: 1px solid #fff;
    background-color: #fff;
    outline: none;
    font-size: 16px;
    color: #222;
    font-weight: 700;
    -webkit-appearance: none;
    line-height: 50px;
    margin-bottom: 10px;
  }
  .goods-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 0px 20px;
    .goods-item {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0px 15px;
      width: 50px;
      background-color: #fff;
      border-radius: 20px;
      margin-bottom: 10px;
      font-size: 14px;
    }
  }
  .primary-btn {
    font-size: 16px;
  }
}
</style>
