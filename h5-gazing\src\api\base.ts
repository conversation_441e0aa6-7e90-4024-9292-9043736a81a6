export interface registerData {
  email: string
  password: string
}

export interface loginData {
  username: string
  password: string
}

export interface videoParams {
  product_key: string
  device_sn: string
  date: number
  type: string
  page: number
  page_size: number
}

export interface deviceData {
  device_sn: string
  product_key: string
  sn_show: string
  img_url: string
  last_img_url: string
  nickname: string
  battery_level: number
  timezone: string
  is_charging: boolean
  wifi_name: string
  is_primary: boolean
  status: number
  area: string
  model: number
  ota_state: number
  ota_progress: number
  product_img: string
  ota_item: otaItemData
  function_bitmap: number
  project_id: number
  doorbell_diagram: string
  volume: number
  need_subscribe: number
  cloud_product_id: string
  attributes: propertiesData
  sort:number
  unreadCount: number
}

export interface propertiesData {
  PIR: number
  detection_area: number
  detection_area_list: string
  human_detection: number
  human_detection_state: number
  lamplight: number
  person_marker: number
  light_leve: number
  ota_version: string
  battery_level: number
  wifi_rssi: number
  wifi_mac: string
  speed_mbps:number
  mcu_version: string
  mac_address: string
  sim_number: string
  soc_version: string
  wifi_ssid:string
  audio_decoder:{
    clockRate:string
    mimeType:string
    packetsReceived:number
    packetsLost:number
  }
  video_decoder:{
    clockRate:string
    mimeType:string
    packetsReceived:number
    packetsLost:number
  }
}

export interface deviceLogData {
  living: {
    debug_str:string
    upload_rate:number
  }
  esi: string
  datetime: string
  wakeup_stat:string
  wakeup_count:number
  other_user:string
  transpond_delay:number
  upload_cloud_delay:number
  recording:number
}

interface debugItem {
  sessionID:string
  info:string
  upload_rate:number
}

export interface propertyData {
  category: string
  value: number | string
}

export interface deviceConfig {
  [key: string]: deviceConfigItem
}

export interface deviceConfigItem {
  bs_track_mode_enabled: number
  bs_track_mode_value: number
  c_night_mode_enabled: number
  c_night_mode_value: number
  change_net: number
  conventional_power_enabled: number
  detetion_device_enabled: number
  dual_camera: number
  guard_position_enable: number
  intercom: number
  is_4g_card: number
  is_have_sim: number
  mobile_trigger_light_enabled: number
  mobile_trigger_light_value: number
  nn_label_enabled: number
  nn_label_value: number
  oss: number
  pantilt_device_enabled: number
  pir: number
  reverse_video_enabled: number
  ring: number
  video_definition_switch_enabled: number
  resolution:number
}

export interface otaItemData {
  device_sn: string
  download_url: string
  target_mcu_version: string
  target_soc_version: string
}

export interface webrtcData {
  SG: webrtcItem
  US: webrtcItem
  DE: webrtcItem
  CN: webrtcItem
}

export interface webrtcItem {
  app_signal: string
  dev_signal: string
  turn_addr: string
  turn_pwd: string
  turn_user: string
}

export interface syncData {
  //回放视频列表
  device_sn: string
  duration: number
  durationS: string
  path: string
  image_sign_url: string
  video_sign_url: string
  bucket: string
  endpoint: string
  oss_id: number
  oss_status: number
  video_status: number
  date: number
  is_sync: number
  day_time?: string
  time?: string
  active: boolean
  type: string
  oss_from_id: number
}

export interface submitOrderParams {
  shipping_code: string
  postcode: string
  products: unknown[]
  cart_ids: number[]
  remark: string
  sim_device?: {
    device_sn: string
    name: string
    sim_uuid?: string
  }
  sign: string
}

export interface goodsListProduct {
  master_id: number
  reward: string
  image: string
  skuList: string[]
  quantity: number
  price: string
  product_id: number
  name: string
  model: string
  attrs: Array<defaultSkuData>
  cart_id?: number
  is_virtual: number
}

export interface defaultSkuData {
  product_id: number
  suk: string[]
  stock: number
  sales: number
  price: string
  image: string
  unique: string
  cost: string
  bar_code: string
  ot_price: string
  vip_price: string
  weight: string
  volume: string
  product_attr_value_id: number
}

//分享者数据
export interface shareData {
  create_at: number
  email: string
  user_id: string
}

//添加设备列表
export interface addDeviceData {
  id: number
  bluetooth: number
  name: string
  product_item_img: string
  is_4g_card: number
  is_https: number
  is_redled_display: number
  parent_id: number
  single_line: number
  product_4_img: string
  network_config_img:string
  children: Array<addDeviceItem>
}

export interface addDeviceItem {
  id: number
  bluetooth: number
  name: string
  product_item_img: string
  is_4g_card: number
  is_https: number
  is_redled_display: number
  parent_id: number
  single_line: number
  product_4_img: string
  network_config_img:string
}

export interface expandParams {
  session_id: string
  device_sn: string
  cloud_product_id: string
  cycle_days: string
  event: string
}

export interface backParams {
  device_sn: string
  session_id: string
  is_test: string
}

export interface logParams {
  device_sn: string
  os: number
  link: string
  sign: string
}

export interface appleParams {
  product_key: string
  device_sn: string
  is_sandbox: number
  product_id: string
  order_sn: string
  src: number
  receipt_data: string
  password: string
  fee_type: string
  fee_total: number
}

export interface googleParams {
  purchase_token: string
  device_sn: string
  package_name: string
  subscription_id: string
  order_sn: string
  src: number
  fee_type: string
  fee_total: number
}

export interface exportData {
  currency: string
  price: number
  cycle_days: number
  trial_days: number
  service_days: number
  product_id: string
  group: number
  device_sn?: string
  src?: number
}

// 聊天列表数据
export interface chatData {
  device_sn: string
  name: string
  product_image: string
  screen:string
  tip:number
  chat_tab_display: Array<number>
  badge: Array<number>
  chat_content: {
    id: number
    uid: number
    question_id: number
    role: number
    tgmsg_id: number
    dateline: number
    screen: number
    expiry_day: number
    is_read: number
    content: string
    text: string
    time: string
  }
}

// 视频回放计数
export interface videosCountData {
  [key: string]: number
}

// 首页列表用户手动排序参数
export interface sortsParams {
  device_sn: string
  sort:number
}

// 客服-聊天消息列表入参
export interface chatListsParams {
  device_sn: string
  screen: number
  page: number
}

// 客服-聊天消息列表
export interface chatLists {
  content: {
    txt: string
    img?: string
    url?: string
  } // 聊天内容 必需
  dateline: string // 时间 必需
  device_sn: string  // 设备号 必需
  is_read: number // 是否已读 必需
  is_tgmsg: number // 是否为推广消息0-否1-是 必需
  tgmsg_id: number // 推广消息id 必需
  screen: number // 必需
  role: number // 角色0-用户1-客服
  id: string
  class: string
  msg_id: string
  ctime: number
}

// 客服-聊天消息发送入参
export interface screenSayParams {
  device_sn: string
  screen: number
  content: {
    txt: string
  }
}

// 客服-聊天消息已读消息入参
export interface screenReadParams {
  device_sn: string
  screen: number
  msg_id: number[]
}

// 客服-聊天消息未读消息列表获取入参
export interface screenUnreadCountParams {
  device_sns: string[]
  screen: number
}