import http from './request';


// 获取聊天信息
export function getChat(sessionId:string,device_sn:string,screen:number,page:number,src:number) {
    return http(`/chat/list_with_screen_v5/${sessionId}/${device_sn}/${screen}/${page}/${src}`);
}

// 上传图片
export function imageUpload(params:object) {
    return http('/chat_image_upload', {
        method: 'POST',
        params: params
    },
    {'Content-Type':'multipart/form-data'}
    );
}


// 获取阿里云OSS服务key
export function getOssKey(sessionId:string) {
    return http(`/sts/token/${sessionId}/oss-us-west-1.aliyuncs.com/9`);
}


// 获取兑换卡信息接口
export function getExchange(code_sn:string) {
    return http(`/chat/code_sn_info/${code_sn}`);
}


// 兑换码兑换接口
export function saveExchange(sessionId:string,device_sn:string,code:string) {
    return http(`/pay/redeem_code_v2/${sessionId}/${device_sn}/${code}`);
}

// 获取用户输入地址接口
export function get_auto_customer_star_info(sessionId:string,device_sn:string,sign:string) {
    return http(`/chat/get_auto_customer_star_info/${sessionId}/${device_sn}/${sign}`);
}

// 获取轮播数据
export function get_product_discount_info(token:string) {
    return http(`/get_product_discount_info/${token}`);
}

// 获取msg_id
export function get_gift_msg_id(token:string,device_sn:string) {
    return http(`/get_gift_msg_id/${token}/${device_sn}`);
}

// 统计点击复制邀请码记录
export function stat_code_copy(token:string,code:string,sign:string) {
    return http(`/webview/stat_code_copy/${token}/${code}/${sign}`);
}

// 统计点击复制跳转亚马逊记录
export function stat_link_jump(token:string,link:string,sign:string) {
    return http(`/webview/stat_link_jump/${token}/${link}/${sign}`);
}

// 统计进入邀请码界面记录
export function stat_open(token:string) {
    return http(`/webview/stat_open/${token}`);
}