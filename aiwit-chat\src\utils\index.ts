/**
 * @description 获取localStorage
 * @param {String} key Storage名称
 * @returns {String}
 */
export function localGet(key: string) {
  const value = window.localStorage.getItem(key)
  try {
    return JSON.parse(window.localStorage.getItem(key) as string)
  } catch (error) {
    return value
  }
}

/**
 * @description 存储localStorage
 * @param {String} key Storage名称
 * @param {*} value Storage值
 * @returns {void}
 */
export function localSet(key: string, value: any) {
  window.localStorage.setItem(key, JSON.stringify(value))
}

/**
 * @description 清除localStorage
 * @param {String} key Storage名称
 * @returns {void}
 */
export function localRemove(key: string) {
  window.localStorage.removeItem(key)
}

/**
 * @description 清除所有localStorage
 * @returns {void}
 */
export function localClear() {
  window.localStorage.clear()
}

/**
 * @description 生成随机数
 * @param {Number} min 最小值
 * @param {Number} max 最大值
 * @returns {Number}
 */
export function randomNum(min: number, max: number): number {
  let num = Math.floor(Math.random() * (min - max) + max)
  return num
}

/**
 * @description 获取浏览器默认语言
 * @returns {String}
 */
export function getBrowserLang() {
  let browserLang = navigator.language ? navigator.language : navigator.browserLanguage
  let defaultBrowserLang = ""
  if (["cn", "zh", "zh-cn"].includes(browserLang.toLowerCase())) {
    defaultBrowserLang = "zh"
  } else {
    defaultBrowserLang = "en"
  }
  return defaultBrowserLang
}

/**
 * @description 获取url地址参数
 * @param url
 * @returns {object|boolean}
 */

export function getUrlParamsObject(url: string) {
  if (url.indexOf("?") == -1) return false
  let arr = url.split("?")
  let params = arr[1].split("&")
  let obj = {} as any
  for (let i = 0; i < params.length; i++) {
    let param = params[i].split("=")
    obj[param[0]] = param[1]
  }
  return obj
}

/**
 * @description 赋值全局变量参数
 * @param obj
 * @returns {object}
 * @param obj1
 * @returns {object}
 */
export function getConfig(obj: object, obj1: object) {
  obj = Object.assign(obj, obj1)
}

/**
 * @description 单独设置axios请求头
 * @param asios
 * @param arrays
 * @returns {void}
 */
export function setAxiosHeader(
  asios: { defaults: { headers: { common: Record<string, unknown> } } },
  arrays: { key: string; value: string }[]
) {
  arrays.forEach(item => {
    asios.defaults.headers.common[item.key] = item.value
  })
}

/**
 * @description 判断android/ios
 * @returns {string}
 */
export function checkplatform() {
  const u = navigator.userAgent
  const isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1
  const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
  if (isAndroid) return "android"
  if (isiOS) return "ios"
}

/**
 * @description 调用原生方法跳转到客服聊天
 * @returns {string}
 */
export function navDetail(item: any) {
  if (checkplatform() == "ios") {
    window.webkit.messageHandlers.send_device_sn.postMessage({
      device_sn: item.device_sn,
    })
  } else {
    window.android.send_device_sn(item.device_sn)
  }
}

/**
 * @description 过滤数据
 */
export function filterData(list: any) {
  list.forEach((el: any) => {
    el.title$ = el.name
    el.tip$ = el.badge.reduce((acc: number, cur: number) => acc + cur, 0)
    el.content$ = el.chat_content?.text
    el.time$ = el.chat_content?.time
    el.pic$ = el.product_image
    el.screen$ = el.chat_tab_display.join("-")
  })
}

/**
 * @description 节流函数
 */
export function debounce(func: any, wait: number, immediate: boolean) {
  let timeout: any // 定义一个计时器变量，用于延迟执行函数
  return (...args: any[]) => {
    // 返回一个包装后的函数
    const later = function () {
      // 定义延迟执行的函数
      timeout = null // 清空计时器变量
      if (!immediate) func.apply(func, args) // 若非立即执行，则调用待防抖函数
    }
    const callNow = immediate && !timeout // 是否立即调用函数的条件
    clearTimeout(timeout) // 清空计时器
    timeout = setTimeout(later, wait) // 创建新的计时器，延迟执行函数
    if (callNow) func.apply(func, args) // 如果满足立即调用条件，则立即执行函数
  }
}
