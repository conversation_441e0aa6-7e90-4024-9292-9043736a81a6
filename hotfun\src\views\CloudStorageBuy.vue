<template>
  <div class="pages">
    <div class="pages-title">
      <van-icon name="arrow-left" size="25" color="#FFFFFF" @click="backHome" />
    </div>
    <div class="content">
      <div class="plan-card">
        <div class="plan-title">Monthly</div>
        <div class="price-section">
          <span class="price">$ 5.0</span>
        </div>
        <div class="divider" />
        <div class="features">
          <div class="feature-item">
            <span class="dot"></span>
            <span class="feature-text">1 account:Full access to premium features</span>
          </div>
          <div class="feature-item">
            <span class="dot"></span>
            <span class="feature-text">Cancel anytime:No commitments</span>
          </div>
        </div>
        <div class="trial-button" @click="subscribeNow">
          <span>Subscribe Now</span>
        </div>
      </div>
      <div class="plan-card">
        <div class="plan-title">Half Year</div>
        <div class="price-section">
          <span class="price">$ 38.9</span>
        </div>
        <div class="divider" />
        <div class="features">
          <div class="feature-item">
            <span class="dot"></span>
            <span class="feature-text">1 account:Full access to premium features</span>
          </div>
          <div class="feature-item">
            <span class="dot"></span>
            <span class="feature-text">Cancel anytime:No commitments</span>
          </div>
          <div class="feature-item">
            <span class="dot"></span>
            <span class="feature-text">Free trial:Try it for 30 days</span>
          </div>
        </div>
        <div class="trial-button" @click="startFreeTrial">
          <span>Start free trial</span>
        </div>
      </div>
      <div class="plan-card">
        <div class="plan-title">Year</div>
        <div class="price-section">
          <span class="price">$ 39.9</span>
        </div>
        <div class="divider" />
        <div class="features">
          <div class="feature-item">
            <span class="dot"></span>
            <span class="feature-text">1 account:Full access to premium features</span>
          </div>
          <div class="feature-item">
            <span class="dot"></span>
            <span class="feature-text">Cancel anytime:No commitments</span>
          </div>
          <div class="feature-item">
            <span class="dot"></span>
            <span class="feature-text">Free trial:Try it for 30 days</span>
          </div>
        </div>
        <div class="trial-button" @click="startFreeTrial">
          <span>Start free trial</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref,onMounted } from "vue"
import { useRouter } from "vue-router"

const router = useRouter()
const windowHeight = ref(window.innerHeight)

const initHeight = () => {
  const elements = document.querySelectorAll(".plan-card")
  elements.forEach((el: any) => {
    el.style.height = `${(windowHeight.value - 125) / 3}px`
  })
}

const subscribeNow = () => {}
const startFreeTrial = () => {}

const backHome = () => {
  router.go(-1)
}

onMounted(()=>{
  initHeight()
})
</script>

<style lang="less" scoped>
.pages {
  padding: 0 16px;
  background-color: #003333;
  height: 100%;

  .ios-top {
    height: 40px;
  }

  .pages-title {
    height: 40px;
    padding-top: constant(safe-area-inset-top); /* iOS < 11.2 */
    padding-top: env(safe-area-inset-top); /* iOS >= 11.2 */
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .plan-card {
    background-color: #eeeeee;
    border-radius: 12px;
    padding: 12px 12px 0 12px;

    .plan-title {
      font-size: 16px;
      font-weight: 500;
      color: #003333;
      letter-spacing: -0.33px;
    }

    .price-section {
      display: flex;
      align-items: baseline;
      line-height: 1.2;
      color: #de8d13;

      .price {
        font-size: 28px;
      }
    }

    .divider {
      height: 1px;
      background-color: #d1d5db;
      margin-bottom: 10px;
    }

    .features {
      margin-bottom: 18px;

      .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }

        .dot {
          width: 6px;
          height: 6px;
          background-color: #9ca3af;
          border-radius: 50%;
          margin-right: 0.75rem;
          flex-shrink: 0;
        }

        .feature-text {
          font-size: 12px;
          color: #003333;
          letter-spacing: 0;
        }
      }
    }

    .trial-button {
      width: 100%;
      height: 42px;
      border-radius: 21px;
      background-color: #f4c370;
      color: #0b3b3b;
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      line-height: 42px;
      letter-spacing: 0;
    }
  }
}
</style>
