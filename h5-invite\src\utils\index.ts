import config from "@/config"
import router from "@/router"
import { closeToast, showLoadingToast } from "vant"
import { stat_code_copy } from "@/api"
import { md5 } from "js-md5"

// 自动补0
function formatTime(time: number) {
  return time < 10 ? `0${time}` : time
}

// 获取当前年月日
export function updateTime() {
  const now = new Date()
  const year = now.getFullYear() //年
  const month = now.getMonth() + 1 //月
  const day = now.getDate() //日
  const hours = now.getHours() //小时数
  const minutes = now.getMinutes() //分钟数
  const seconds = now.getSeconds() //秒数
  // const week = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"][now.getDay()]  //星期
  //想展示什么  对应的展示即可
  return `${year}/${formatTime(month)}/${formatTime(day)} ${formatTime(hours)}:${formatTime(
    minutes
  )}:${formatTime(seconds)}`
}

// 4位随机数
export function randomNum() {
  return Math.floor(Math.random() * 4000 + 1000)
}

// 截取图片路径里的参数
export function getTypeFromUrl(url: string) {
  const match = url.match(/[?&]type=([^&#]*)/)
  return match ? match[1] : ""
}

// 读取照片
export function readImg(file: any) {
  return new Promise((resolve, reject) => {
    let img: HTMLImageElement = new Image()
    const reader = new FileReader()
    reader.onload = function (e: any) {
      img.src = e.target.result
      base64ToFile(img.src, new Date().getTime() + ".png")
    }
    reader.onerror = e => {
      reject(e)
    }
    reader.readAsDataURL(file)
    img.onload = () => {
      resolve(img)
    }
    img.onerror = e => {
      reject(e)
    }
  })
}

export function compressImg(img: any, type: any, mx: any, mh: any) {
  return new Promise(resolve => {
    const canvas = document.createElement("canvas")
    const context: any = canvas.getContext("2d")
    const { width: originWidth, height: originHeight } = img
    // 最大尺寸限制
    const maxWidth = mx
    const maxHeight = mh
    // 目标尺寸
    var targetWidth = originWidth
    var targetHeight = originHeight
    if (originWidth > maxWidth || originHeight > maxHeight) {
      if (originWidth / originHeight > 1) {
        // 宽图片
        targetWidth = maxWidth
        targetHeight = Math.round(maxWidth * (originHeight / originWidth))
      } else {
        // 高图片
        targetHeight = maxHeight
        targetWidth = Math.round(maxHeight * (originWidth / originHeight))
      }
    }
    canvas.width = targetWidth
    canvas.height = targetHeight
    context.clearRect(0, 0, targetWidth, targetHeight)
    // 图片绘制
    context.drawImage(img, 0, 0, targetWidth, targetHeight)
    resolve(canvas.toDataURL(type, 0.2))
  })
}

// 解码base64
export function base64ToFile(urlData: any, fileName: string) {
  let arr = urlData.split(",")
  let mine = arr[0].match(/:(.*?);/)[1]
  let bytes = atob(arr[1]) //解码base64
  let n = bytes.length
  let ia = new Uint8Array(n)
  while (n--) {
    ia[n] = bytes.charCodeAt(n)
  }
  return new File([ia], fileName, { type: mine })
}

/**
 * @description 获取url地址参数
 * @param url
 * @returns {object|boolean}
 */

export function getUrlParamsObject(url: string) {
  if (url.indexOf("?") == -1) return false
  let arr = url.split("?")
  let params = arr[1].split("&")
  let obj = {} as any
  for (let i = 0; i < params.length; i++) {
    let param = params[i].split("=")
    obj[param[0]] = param[1]
  }
  return obj
}

/**
 * @description 赋值全局变量参数
 * @param obj
 * @returns {object}
 * @param obj1
 * @returns {object}
 */
export function getConfig(obj: object, obj1: object) {
  obj = Object.assign(obj, obj1)
}

//点击加loading状态
export function showLoading() {
  showLoadingToast({
    message: "Loading...",
    forbidClick: true,
  })
}

// 点击调用原生复制
export function copyCode(discount_code: string) {
  try {
    if (config.platform == "android") {
      window.android.click_copy(discount_code)
    } else {
      window.webkit.messageHandlers.click_copy.postMessage(discount_code)
    }
    stat_code_copy(
      config.token,
      discount_code,
      md5(`${config.token}/${discount_code + "EKDB_ni&Hb&Zt&zz^7qn9"}`)
    )
  } catch {
    console.log("不在内嵌手机端")
  }
}

// 点击调用跳转
export function click_home_family() {
  try {
    if (config.platform == "android") {
      window.android.click_home_family("跳转全家桶")
    } else {
      window.webkit.messageHandlers.click_home_family.postMessage("跳转全家桶")
    }
  } catch {
    console.log("不在内嵌手机端")
  }
}

export function click_discount(url: string) {
  try {
    if (config.platform == "android") {
      window.android.click_discount(url)
    } else {
      window.webkit.messageHandlers.click_discount.postMessage(url)
    }
  } catch {
    console.log("不在内嵌手机端")
    window.location.href = url
  }
}

// 点击通知APP刷新礼物按钮状态
export function refresh_gift_btn() {
  try {
    if (config.platform == "android") {
      window.android.refresh_gift_btn("用户已提交表单，需要刷新礼物按钮")
    } else {
      window.webkit.messageHandlers.refresh_gift_btn.postMessage("用户已提交表单，需要刷新礼物按钮")
    }
  } catch {
    console.log("不在内嵌手机端")
  }
}

// 点击了亚马逊返回事件
export function amxBack() {
  if (router.currentRoute.value.name === "guide") {
    closeToast()
  }
}

// 点击通知APP返回首页
export function click_back() {
  try {
    if (config.platform == "android") {
      window.android.click_back("回到APP首页")
    } else {
      window.webkit.messageHandlers.click_back.postMessage("回到APP首页")
    }
  } catch {
    console.log("不在内嵌手机端")
  }
}
