<template>
  <div class="container">
    <div class="modal-invite-data">
      <div class="modal-invite-from-title">
        <h1>Claim Your Cash Reward</h1>
        <p>Enter your details to receive your cash reward</p>
      </div>
      <form class="shipping-form" @submit.prevent="handleSubmit">
        <div class="floating-label-group">
          <input
            type="text"
            id="name"
            class="floating-label-input"
            placeholder=" "
            v-model="form.name"
            :class="{ error: errors.name }"
          />
          <label for="name" class="floating-label required">Full Name</label>
          <div class="error-message" v-if="errors.name">{{ errors.name }}</div>
        </div>

        <div class="floating-label-group">
          <input
            email
            id="email"
            class="floating-label-input"
            placeholder=" "
            v-model="form.email"
            :class="{ error: errors.email }"
          />
          <label for="email" class="floating-label required">PayPal Email Address</label>
          <div class="error-message" v-if="errors.email">{{ errors.email }}</div>
        </div>
        <div class="note">
          This must be the email linked to your PayPal account. Your cash reward will be sent here.
        </div>
        <button type="submit" class="submit-button">Confirm</button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, onUnmounted } from "vue"
import config from "@/config"
import { showLoadingToast, showToast, showDialog, closeToast } from "vant"
import { createWebSocket, sendSock, closeSock } from "@/utils/socket"
import router from "@/router"

interface Form {
  name: string
  email: string
}

const form = reactive<Form>({
  name: "",
  email: "",
})
const errors = reactive<{
  name?: string
  email?: string
}>({})

const validateForm = (): boolean => {
  let isValid = true
  if (!form.name.trim()) {
    errors.name = "Please enter your name"
    isValid = false
  } else {
    delete errors.name
  }
  if (!form.email.trim()) {
    errors.email = "Please enter a valid phone number"
    isValid = false
  } else {
    delete errors.email
  }
  return isValid
}

// 提交评价
const handleSubmit = () => {
  if (validateForm()) {
    if (config.websockStatus === 0) {
      closeSock()
      setTimeout(() => {
        createWebSocket(global_callback)
      }, 100)
    } else {
      showLoadingToast({
        message: "Loading...",
        //禁止背景点击
        forbidClick: true,
      })
      const params = JSON.stringify(
        Object.assign(form, {
          cmd: "gift_info_handle",
          msg_id: config.msg_id,
          device_sn: config.device_sn,
        })
      )
      sendSock(params)
    }
  }
}

// 连接websocket模块
const global_callback = (msg: any) => {
  if (msg) {
    if (msg.cmd === "gift_info_handle") {
      if (msg.resultCode === 0) {
        closeToast()
        showDialog({
          title: "Thank You!",
          message:
            "Your PayPal information has been submitted successfully.\nWe'll process your cash reward within 6-7 business days.",
        }).then(() => {
          inviteClose()
        })
      } else {
        closeToast()
        showToast("Please upload screenshots of reviews with star rating")
      }
    } else if (msg.resultCode == 0 && msg.cmd === "heartbeat") {
      //正在连接心跳
      config.websockStatus = 1
    }
  }
}

const inviteClose = () => {
  router.push("/")
}

onMounted(() => {
  showLoadingToast({
    message: "Loading...",
    //禁止背景点击
    forbidClick: true,
  })
  setTimeout(() => {
    createWebSocket(global_callback)
  }, 800)
  setTimeout(() => {
    closeToast()
  }, 1500)
})

onUnmounted(() => {
  closeSock()
})
</script>

<style lang="less" scoped>
.container {
  background-color: #fff;
  min-height: 100vh;
}
.modal-invite-from-title {
  color: #fff;
  word-break: normal;
  overflow-wrap: break-word;
  background: linear-gradient(135deg, #8a2be2, #ff69b4);
  height: 55px;
  padding-top: 15px;
  text-align: center;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  h1 {
    font-size: 20px;
    margin-bottom: 6px;
  }
  p {
    font-size: 14px;
  }
}
.shipping-form {
  padding: 20px 15px;
  .floating-label-group {
    position: relative;
    margin-bottom: 16px;
  }

  .floating-label-input {
    width: 95%;
    height: 35px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s;
    padding-top: 15px;
    padding-left: 15px;
  }

  .floating-label {
    position: absolute;
    top: 17px;
    left: 16px;
    font-size: 16px;
    color: #999;
    pointer-events: none;
    transition: all 0.2s;
  }
  .note {
    font-size: 12px;
    color: #666;
    margin-top: 20px;
    line-height: 1.4;
    text-align: center;
  }
  .floating-label-input:focus ~ .floating-label,
  .floating-label-input:not(:placeholder-shown) ~ .floating-label {
    top: 8px;
    left: 16px;
    font-size: 12px;
    color: #8a2be2;
  }

  .floating-label-input:focus {
    border-color: #8a2be2;
    box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.15);
  }
  .error-message {
    color: #d9534f;
    font-size: 12px;
    margin-top: 5px;
  }

  .required::after {
    content: " *";
    color: #d9534f;
  }
  .submit-button {
    display: block;
    background-color: #4caf50;
    color: white;
    font-size: 16px;
    font-weight: bold;
    padding: 14px 0;
    border-radius: 30px;
    text-decoration: none;
    margin: 25px auto 0;
    text-align: center;
    width: 100%;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
  }
  .form-row {
    display: flex;
    gap: 10px;
    .floating-label-input {
      width: 90% !important;
    }
  }
}
</style>
