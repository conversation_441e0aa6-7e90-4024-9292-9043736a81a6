<template>
  <div class="share-data">
    <Header title="邮箱分享" />
    <div class="from-data">
      <div class="from-title">将设备分享给</div>
      <input
        class="footer-input"
        v-model.trim="textValue"
        type="text"
        placeholder="Email address"
      />
      <van-button type="primary" color="#4774F5" @click="sendClick" size="large" round>
        确定
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import { ref } from "vue"
import { device_share_email } from "@/api/index"
import config from "@/config"
import { showToast } from "vant"
import { useRouter } from "vue-router"

const router = useRouter()
const textValue = ref("")
const sendClick = () => {
  device_share_email(
    config.product_key,
    config.device_sn,
    textValue.value,
    config.device.nickname
  ).then((res: any) => {
    if (res.code === 0) {
      showToast("设备分享成功")
      setTimeout(() => {
        router.push("/")
      }, 2000)
    }
  })
}
</script>

<style lang="less" scoped>
.share-data {
  height: 100vh;
  background: #f8f8f8;
  box-sizing: border-box;
  .from-data {
    padding: 50px 20px 0px 20px;
    .from-title {
      font-size: 16px;
      color: #494854;
      font-weight: 700;
      margin-bottom: 10px;
    }
    .footer-input {
      width: 100%;
      height: 50px;
      border-radius: 25px;
      padding-left: 12px;
      border: 1px solid #fff;
      background-color: #ffffff;
      outline: none;
      font-size: 16px;
      color: #494854;
      font-weight: 700;
      -webkit-appearance: none;
      line-height: 50px;
      margin-bottom: 30px;
      box-sizing: border-box;
      &::placeholder {
        color: #848CA4;
        font-weight: 400;
      }
    }
  }
}
</style>
