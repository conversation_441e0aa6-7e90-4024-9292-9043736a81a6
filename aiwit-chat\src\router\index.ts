import { createRouter, createWebHashHistory } from 'vue-router'
import config from '@/config'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'chat',
      component: () => import('@/views/ChatView.vue'),
    },
    {
      path: '/kementChatView',
      name: 'kementChatView',
      component: () => import('@/views/KementChatView.vue'),
    },
    {
      path: '/seeingChatView',
      name: 'seeingChatView',
      component: () => import('@/views/SeeingChatView.vue'),
    },
    {
      path: '/zoroiixChatView',
      name: 'zoroiixChatView',
      component: () => import('@/views/ZoroiixChatView.vue'),
    },
    {
      path: '/404',
      name: '404',
      component: () => import('@/components/ErrorMessage/404.vue'),
    },
    {
      path: '/:pathMatch(.*)*',
      component: () => import('@/components/ErrorMessage/404.vue'),
    },
  ],
})

// 路由拦截
router.beforeEach((to, from, next) => {
  if (config.app_name.toLocaleLowerCase() === 'kement' && to.path == '/') {
    next('/kementChatView')
  } else if (config.app_name.toLocaleLowerCase() === 'seeing' && to.path == '/') {
    next('/seeingChatView')
  } else if (config.app_name.toLocaleLowerCase() === 'zoroiix' && to.path == '/') {
    next('/zoroiixChatView')
  } else {
    next()
  }
})

export default router
