// utils/secret.ts
import CryptoJS from 'crypto-js';

// 定义密钥和初始向量（根据需要进行修改）
const KEY = 'c707248ydlvxfrmx'; // 必须是16、24或32位
const IV = 'd73b3e7763dd8d99'; // 必须是16位

/**
 * AES加密
 * @param word 要加密的字符串
 * @param keyStr 密钥
 * @param ivStr 初始向量
 * @returns 加密后的字符串（Base64）
 */
export const Encrypt = (word: string, keyStr = KEY, ivStr = IV): string => {
    const key = CryptoJS.enc.Utf8.parse(keyStr);
    const iv = CryptoJS.enc.Utf8.parse(ivStr);
    const srcs = CryptoJS.enc.Utf8.parse(word);
    const encrypted = CryptoJS.AES.encrypt(srcs, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    });
    return encrypted.toString();
};

/**
 * AES解密
 * @param word 要解密的字符串
 * @param keyStr 密钥
 * @param ivStr 初始向量
 * @returns 解密后的字符串
 */
export const Decrypt = (word: string, keyStr = KEY, ivStr = IV): string => {
    const key = CryptoJS.enc.Utf8.parse(keyStr);
    const iv = CryptoJS.enc.Utf8.parse(ivStr);
    const decrypt = CryptoJS.AES.decrypt(word, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    });
    return CryptoJS.enc.Utf8.stringify(decrypt);
};