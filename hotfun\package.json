{"name": "hotfun", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "preview": "vite preview", "build": "vite build --mode test", "build:prod": "vite build --mode production", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false"}, "dependencies": {"ali-oss": "^6.23.0", "axios": "^1.6.0", "js-md5": "^0.8.3", "postcss-px-to-viewport-8-plugin": "^1.2.5", "vant": "^4.8.5", "vconsole": "^3.15.1", "vue": "^3.3.4", "vue-router": "^4.3.0"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/jsdom": "^21.1.3", "@types/node": "^18.18.5", "@vitejs/plugin-vue": "^4.4.0", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.4.0", "jsdom": "^22.1.0", "less": "^4.2.0", "npm-run-all2": "^6.1.1", "typescript": "~5.2.0", "vite": "^4.4.11", "vue-tsc": "^1.8.19"}}