<template>
  <div class="pages">
    <Header />
    <div class="container">
      <div class="net-title">连接到 2.4 GHz Wi-Fi网络</div>
      <div class="new-tip">
        此设备只能连接到2.4GHz Wi-Fi网络。请确保您的手机和设备已连接到同一个2.4 GHz网络。
      </div>
      <div class="new-xian"></div>
      <div class="step-item">
        <div class="item-left">01</div>
        <div class="item-right item-two">转至手机的“设置”>“Wi-Fi”。连接到 2.4GHZ网络。</div>
      </div>
      <div class="step-item stop-two">
        <div class="item-left item-left-two">02</div>
        <div class="item-right item-two">返回 Camtro 应用。</div>
      </div>
      <div class="step-item">
        <div class="item-left">03</div>
        <div class="item-right">轻触“继续”允许 Camtro 访问您的位置以查找2.4GHZ网络。</div>
      </div>
      <div class="tip-text">这会使Camtro 应用指向与您的手机网络相同的网络。</div>
    </div>
    <div class="footer-btn">
      <van-button
        type="primary"
        round
        size="large"
        color="linear-gradient(90deg,#4774F5 3.73%,#4977F4 100%)"
        @click="handleWifi"
      >
        继续
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import config from "@/config"
import { useRouter } from "vue-router"
import { click_search_device } from "@/utils"

const router = useRouter()

const handleWifi = () => {
  if (config.add_device_item.bluetooth === 1) {
    click_search_device()
    router.push("searchDevice")
  } else {
    router.push("configurationNetwork")
  }
}
</script>

<style lang="less" scoped>
.container {
  padding: 20px;
  .net-title {
    font-size: 22px;
    color: #222;
    font-weight: 700;
    margin-bottom: 15px;
  }
  .new-tip {
    font-size: 12px;
    color: #666666;
    margin-bottom: 15px;
    line-height: 20px;
  }
  .new-xian {
    border: 1px solid #f3f3f3;
    margin-bottom: 15px;
  }
  .step-item {
    height: 61px;
    display: flex;
    align-items: center;
    .item-left {
      width: 53px;
      font-size: 20px;
      color: #4774f5;
      font-weight: 900;
      height: 75%;
    }
    .item-left-two {
      height: auto;
      margin-bottom: 2px;
    }
    .item-right {
      flex: 1;
      font-size: 16px;
      color: #222222;
      line-height: 22px;
    }
  }
  .stop-two {
    margin-bottom: 10px;
  }
  .item-two {
    border-bottom: 1px solid #f3f3f3;
    height: 100%;
    display: flex;
    align-items: center;
  }
  .tip-text {
    padding-top: 10px;
    font-size: 12px;
    color: #999999;
    padding-left: 53px;
  }
}
.footer-btn {
  position: absolute;
  bottom: 50px;
  width: 89%;
  margin: 0 auto;
  padding: 0px 20px;
  left: 0px;
}
.modal {
  padding: 20px;
  .modal-title {
    font-size: 18px;
    color: #222;
    font-weight: 700;
    margin-bottom: 20px;
  }
  .wifi-box {
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .wifi-left {
      font-size: 24px;
      color: #4774f5;
      font-weight: 700;
    }
    .cut-img {
      width: 32px;
      height: 32px;
    }
  }
  .wifi-text {
    font-size: 16px;
    color: #4774f5;
    font-weight: 700;
    margin-bottom: 25px;
  }
  .wifi-tip {
    font-size: 13px;
    color: #222;
    display: flex;
    align-items: center;
    height: 40px;
    border-radius: 8px;
    background-color: #f5f5f5;
    padding-left: 10px;
    margin-bottom: 15px;
  }
  .modal-btn {
    margin-bottom: 20px;
  }
}
</style>
