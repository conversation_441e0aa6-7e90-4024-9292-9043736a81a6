<template>
  <div class="nav-container">
    <!-- <img class="logo" src="../assets/logo.png" alt="" /> -->
    <span @click="nav('/')" class="title">EKEN ELECTRONICS LIMITED </span>
  </div>
</template>
<script lang="ts" setup>
import { useRouter } from "vue-router";
const router = useRouter();
const nav = (url: string) => {
  router.replace(url);
};
</script>
<style scoped lang="less">
.nav-container {
  display: flex;
  align-items: center;
  padding: 20px 6% 0;
  .logo {
    width: 30px;
    margin-right: 10px;
  }
  .title {
    color: #000;
    font-size: 18px;
    font-weight: bolder;
    line-height: 60px;
    cursor: pointer;
  }
}
</style>
