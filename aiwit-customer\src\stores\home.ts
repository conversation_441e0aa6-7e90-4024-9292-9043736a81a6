import { ref,computed } from 'vue';
import { defineStore } from 'pinia';
import moment from 'moment';

// 首页tabbar缓存
export const useHomeStore = defineStore('home', () => {
    // 显示几个导航
    const htmlType = ref(['1','0','0','1']);

    const screen = ref(0);
    const platform = ref('android');

    function increment(data:any) {
        htmlType.value = data;
    }

    function getScreen(data:any) {
        if(data>0){
            screen.value = data;
        }
    }

    function getPlatform() {
        const str = navigator.userAgent;
        if(!!str.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)){
            // ios端
            platform.value = 'ios';
        } else if(str.indexOf('Android') > -1 || str.indexOf('Adr') > -1) {
            // android端
            platform.value = 'android';
        };

    }

    // 过滤底部tabbar是否渲染
    const tabbarStatus = computed(()=>{
        return (htmlType.value[0] === '1'&& htmlType.value[1] === '1')||(htmlType.value[0] === '1'&& htmlType.value[2] === '1')||(htmlType.value[1] === '1'&& htmlType.value[2] === '1')
    })

    return { htmlType, increment,screen,getScreen,tabbarStatus,getPlatform,platform };
})
