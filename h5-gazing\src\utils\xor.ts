import config from "@/config"

export function encrypt(plaintext: string) {
  let encrypted = ""

  for (let i = 0; i < plaintext.length; i++) {
    encrypted += String.fromCharCode(
      plaintext.charCodeAt(i) ^ config.xor_key.charCodeAt(i % config.xor_key.length)
    )
  }

  return config.const_header+'12' + encrypted
}

export function decryptMessage(encryptedMessage: string) {
  // 检查包头
  if (encryptedMessage.slice(0, config.const_header.length) !== config.const_header) {
    throw new Error("Invalid header")
  }
  let message = encryptedMessage.substring(config.const_header.length+2)
  let decrypted = ""

  for (let i = 0; i < message.length; i++) {
    decrypted += String.fromCharCode(
      message.charCodeAt(i) ^ config.xor_key.charCodeAt(i % config.xor_key.length)
    )
  }

  return decrypted
}
