<template>
  <router-view></router-view>
</template>

<script setup lang="ts">
import config from "./config"
import { getUrlParamsObject, getConfig, send_oss_params, init_params } from "@/utils"

const str = navigator.userAgent
if (!!str.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
  config.platform = "ios"
} else if (str.indexOf("Android") > -1 || str.indexOf("Adr") > -1) {
  config.platform = "android"
}

// 线上逻辑
if (window.location.href.indexOf("session_id") !== -1) {
  const getUrlKey: any = getUrlParamsObject(window.location.href)
  getConfig(config, getUrlKey)
} else {
  // 设置全局参数适用本地调试
  config.session_id = "ef809068fbc6a85956690241c472cc4c"
  config.card_id = "683fb9763749f466f8632829"
}
// send_oss_params()
// window.init_params = init_params
</script>

<style lang="less">
html,
body,
p {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Segoe UI, Arial,
    Roboto, "PingFang SC", "miui", "Hiragino Sans GB", "Microsoft Yahei", sans-serif;
}
.pages {
  min-height: 100vh;
  background-color: #f8f8f8;
}
</style>
