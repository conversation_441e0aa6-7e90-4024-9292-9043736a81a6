/**
 * @description 获取url地址参数
 * @param url
 * @returns {object|boolean}
 */

export function getUrlParamsObject(url: string) {
  if (url.indexOf('?') == -1) return false
  let arr = url.split('?')
  let params = arr[1].split('&')
  let obj = {} as any
  for (let i = 0; i < params.length; i++) {
    let param = params[i].split('=')
    obj[param[0]] = param[1]
  }
  return obj
}

/**
 * @description 赋值全局变量参数
 * @param obj
 * @returns {object}
 * @param obj1
 * @returns {object}
 */
export function getConfig(obj: object, obj1: object) {
  obj = Object.assign(obj, obj1)
}

// 处理安卓随机数据中的id
export function getRandomElement(arr: any) {
  if (arr.length === 0) return null
  const randomIndex = Math.floor(Math.random() * arr.length)
  return arr[randomIndex]
}
