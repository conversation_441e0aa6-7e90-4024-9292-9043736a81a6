<template>
  <RouterView></RouterView>
</template>
<script setup lang="ts">
import { i18n } from "@/lang"
import config from "./config"
import { getUrlParamsObject, getConfig, amxBack } from "@/utils"
import { onMounted } from "vue"

const str = navigator.userAgent
if (!!str.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
  config.platform = "ios"
} else if (str.indexOf("Android") > -1 || str.indexOf("Adr") > -1) {
  config.platform = "android"
}

// 线上逻辑
if (window.location.href.indexOf("token") !== -1) {
  // 截取sessionId
  let url = window.location.href.replace(/#.*/, "")
  const getUrlKey: any = getUrlParamsObject(url)
  getConfig(config, getUrlKey)
  //截取语言
  if (config.lang === "cn") {
    localStorage.setItem("language", "zh")
    i18n.global.locale.value = "zh"
  } else {
    localStorage.setItem("language", "en")
    i18n.global.locale.value = "en"
  }
} else {
  // 设置全局参数适用本地调试
  config.token = "fecdea1072a0e415b97807f63f89d413"
  config.app_version = "2.8.2"
  config.countdown = "6000"
  config.event = "2"
  config.discount = "60"
}

onMounted(() => {
  window.amxBack = amxBack
})
</script>
<style lang="less" scoped>
* {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}
html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Segoe UI, Arial,
    Roboto, "PingFang SC", "miui", "Hiragino Sans GB", "Microsoft Yahei", sans-serif;
}
</style>
