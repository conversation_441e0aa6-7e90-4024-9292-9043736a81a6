<template>
  <div class="form-container" :key="route.fullPath">
    <van-form class="van-form-class" @submit="onSubmitSignIn">
      <h1 class="h1">Reset password</h1>

      <div>
        <van-field
          v-model="formData.email"
          label=""
          placeholder="Email"
          type="email"
          :rules="[{ required: true, message: 'Email is required!' }]"
        />
      </div>

      <van-field
        v-model="formData.emailCode"
        label=""
        placeholder="Verification code"
        :rules="[{ required: true, message: 'Verification code is required!' }]"
      >
        <template #button>
          <van-button
            class="get-code-button"
            @click="getEmailCode"
            size="small"
            type="primary"
            :disabled="requiredEmali"
            >{{
              !emailCodeTimerFlag ? emailCodeTimer + "s" : "Get Code"
            }}</van-button
          >
        </template>
      </van-field>

      <van-field
        v-model="formData.password"
        label=""
        placeholder="password"
        type="password"
        :eye-see="true"
        :rules="[{ required: true, message: 'Password is required!' }]"
      />
      <van-field
        v-model="formData.repeatPassword"
        label=""
        placeholder="Repeat password"
        type="password"
        :eye-see="true"
        :rules="[{ required: true, message: 'Repeat password is required!' }]"
      />

      <van-button
        class="submit-button"
        type="primary"
        block
        :disabled="
          !formData.email ||
          !formData.emailCode ||
          !formData.password ||
          !formData.repeatPassword
        "
        native-type="submit"
      >
        Reset password
      </van-button>
    </van-form>
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from "vue";
import Headers from "@/components/Headers.vue";
import { showToast } from "vant";
import { useRoute, useRouter } from "vue-router";
import { md5 } from "js-md5";

const router = useRouter();
const route = useRoute();

interface FormData {
  country: { name: string; code: string };
  email: string;
  emailCode: string;
  password: string;
  repeatPassword: string;
}

/**
 *  signType 0 注册 1 忘记密码 2 登录
 **/
const formData = ref<FormData>({
  country: { name: "", code: "" },
  email: "",
  emailCode: "",
  password: "",
  repeatPassword: "",
});

const nav = (url: string) => router.push(url);
// Submit
const onSubmitSignIn = async () => {
  const { email, password, emailCode } = formData.value;
  const encryptedPassword = md5(password);

  // Modified
  if (formData.value.password !== formData.value.repeatPassword) {
    showToast("Please enter the same password twice");
    return;
  }

  const response = await fetch("https://company.ekenelectronics.com/api/reset_password", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      email: email,
      code: emailCode,
      pass_word: encryptedPassword,
    }),
  });
  const { code, msg } = (await response.json()) as any;
  if (code == 0) {
    showToast("Modified successfully");
    router.replace("/login");
  } else {
    showToast(msg);
  }
};
// Get code
const emailCodeTimer = ref(0);
const emailCodeTimerFlag = ref(true);
const getCode = ref<string>("");
const getEmailCode = async () => {
  const response = await fetch("https://company.ekenelectronics.com/api/send_email_code", {
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ email: formData.value.email }),
  });

  const { data, code, msg } = (await response.json()) as any;

  if (code == 0) {
    getCode.value = data.code;

    emailCodeTimer.value = 60;
    emailCodeTimerFlag.value = false;
    const emailCodeInterval = setInterval(() => {
      if (emailCodeTimer.value > 0) {
        emailCodeTimer.value--;
      } else {
        clearInterval(emailCodeInterval);
        emailCodeTimerFlag.value = true;
      }
    }, 1000);
  } else {
    showToast(msg);
  }
};

const requiredEmali = computed(
  () => !formData.value.email || !emailCodeTimerFlag.value
);
</script>

<style lang="less" scoped>
.form-container {
  background: url(../assets/sign/signbg.png) no-repeat 100% / cover;
  position: relative;
  height: calc(100vh);
  background-color: rgb(246, 247, 250);
  overflow: hidden;
  padding-top: 130px;

  .van-form-class {
    h1 {
      color: black;
      text-align: center;
      padding: 0px 0 10px;
    }
    background-color: #fff;
    width: 400px;
    margin: 100px auto 0;
    padding: 30px 20px 30px;
    border-radius: 10px;
    overflow: hidden;
    .get-code-button {
      width: 90px;
    }
    .submit-button {
      margin-top: 30px;
      border-radius: 6px;
    }
  }

  .forgot-create-container {
    display: flex;
    justify-content: space-between;
    padding: 5px 10px;
    .text-button {
      color: #00000099;
      font-size: 13px;
      cursor: pointer;
    }
    .create-text {
      color: #1989fa;
    }
  }
  @media screen and (max-width: 550px) {
    .van-form-class {
      width: 90vw;
    }
  }
}
</style>
