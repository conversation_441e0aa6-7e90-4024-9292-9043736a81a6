import { createRouter, createWebHashHistory } from "vue-router"
import Home from "../views/Home.vue"
import config from "@/config"

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      redirect: "/home",
    },
    {
      path: "/home",
      name: "home",
      component: Home,
    },
    {
      path: "/deviceDetails",
      name: "deviceDetails",
      component: () => import("@/views/DeviceDetails.vue"),
    },
    {
      path: "/guide",
      name: "guide",
      component: () => import("@/views/Guide.vue"),
    },
    {
      path: "/login/:type",
      name: "login",
      component: () => import("@/views/Login.vue"),
    },
    {
      path: "/device",
      name: "device",
      component: () => import("@/views/AddDevice.vue"),
    },
    {
      path: "/deviceList",
      name: "deviceList",
      component: () => import("@/views/DeviceList.vue"),
    },
    {
      path: "/deviceReset",
      name: "deviceReset",
      component: () => import("@/views/DeviceReset.vue"),
    },
    {
      path: "/configurationNetwork",
      name: "configurationNetwork",
      component: () => import("@/views/ConfigurationNetwork.vue"),
    },
    {
      path: "/networkPrompt",
      name: "networkPrompt",
      component: () => import("@/views/NetworkPrompt.vue"),
    },
    {
      path: "/scanCode",
      name: "scanCode",
      component: () => import("@/views/ScanCode.vue"),
    },
    {
      path: "/qrCode",
      name: "qrCode",
      component: () => import("@/views/QrCode.vue"),
    },
    {
      path: "/playback",
      name: "playback",
      component: () => import("@/views/Playback.vue"),
    },
    {
      path: "/shareCode",
      name: "shareCode",
      component: () => import("@/views/ShareCode.vue"),
    },
    {
      path: "/shareDevice",
      name: "shareDevice",
      component: () => import("@/views/ShareDevice.vue"),
    },
    {
      path: "/deviceInfo",
      name: "deviceInfo",
      component: () => import("@/views/DeviceInfo.vue"),
    },
    {
      path: "/resetPass",
      name: "resetPass",
      component: () => import("@/views/ResetPass.vue"),
    },
    {
      path: "/retrievePass/:type",
      name: "retrievePass",
      component: () => import("@/views/RetrievePass.vue"),
    },
    {
      path: "/activate/:type",
      name: "activate",
      component: () => import("@/views/Activate.vue"),
    },
    {
      path: "/deviceSet",
      name: "deviceSet",
      component: () => import("../views/DeviceSet.vue"),
    },
    {
      path: "/deviceDel",
      name: "deviceDel",
      component: () => import("../views/DeviceDel.vue"),
    },
    {
      path: "/wifiReset",
      name: "wifiReset",
      component: () => import("../views/WiFiReset.vue"),
    },
    {
      path: "/wifiSet",
      name: "wifiSet",
      component: () => import("../views/WiFiSet.vue"),
    },
    {
      path: "/applePay",
      name: "applePay",
      component: () => import("../views/AppleBuyPay.vue"),
    },
    {
      path: "/editName",
      name: "editName",
      component: () => import("../views/EditDeviceName.vue"),
    },
    {
      path: "/emailShareDevice",
      name: "emailShareDevice",
      component: () => import("../views/EmailShareDevice.vue"),
    },
    {
      path: "/transferDevice",
      name: "transferDevice",
      component: () => import("../views/TransferDevice.vue"),
    },
    {
      path: "/deviceVoice",
      name: "deviceVoice",
      component: () => import("../views/DeviceVoice.vue"),
    },
    {
      path: "/volumeSetting",
      name: "volumeSetting",
      component: () => import("../views/VolumeSetting.vue"),
    },
    {
      path: "/humanDetection",
      name: "humanDetection",
      component: () => import("../views/HumanDetection.vue"),
    },
    {
      path: "/lamplight",
      name: "lamplight",
      component: () => import("../views/LamplightSet.vue"),
    },
    {
      path: "/cloudStorage",
      name: "cloudStorage",
      component: () => import("../views/CloudStorage.vue"),
    },
    {
      path: "/cloudStorageBuy",
      name: "cloudStorageBuy",
      component: () => import("../views/CloudStorageBuy.vue"),
    },
    {
      path: "/searchDevice",
      name: "searchDevice",
      component: () => import("../views/SearchDevice.vue"),
    },
    {
      path: "/account",
      name: "account",
      component: () => import("../views/Account.vue"),
    },
    {
      path: "/myDevice",
      name: "myDevice",
      component: () => import("../views/MyDevice.vue"),
    },
    {
      path: "/aboutApp",
      name: "aboutApp",
      component: () => import("../views/AboutApp.vue"),
    },
    {
      path: "/iframe",
      name: "iframe",
      component: () => import("../views/Iframe.vue"),
    },
    {
      path: "/cancelEmail",
      name: "cancelEmail",
      component: () => import("../views/CancelEmail.vue"),
    },
    {
      path: "/customer",
      name: "customer",
      component: () => import("../views/Customer.vue"),
    }
  ],
  scrollBehavior: () => ({ top: 0 }),
})

// 路由拦截
router.beforeEach((to, from, next) => {
  if (!config.token && to.path == "/home" && !localStorage.getItem("token")) {
    next('/guide')
  } else {
    next()
  }
})

export default router
