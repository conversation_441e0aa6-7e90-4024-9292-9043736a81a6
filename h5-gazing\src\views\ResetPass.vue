<template>
  <div class="pages">
    <Header title="" :left-type="1" />
    <div class="reset-top">
      <div class="reset-top-left">找回密码</div>
      <img class="reset-img" src="../assets/logo.png" alt="logo" />
    </div>
    <div class="input-one">
      <input
        class="input-name"
        v-model="username"
        name="userName"
        type="text"
        placeholder="请输入账号"
      />
    </div>
    <div class="reset-sub">
      <van-button
        round
        block
        :disabled="disEmail"
        type="primary"
        color="#1E6FE8"
        @click="sendEmail"
      >
        {{ countNum === 60 ? "发送邮件" : `发送邮件 ${countNum}` }}
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import Header from "@/components/Header.vue"
import { forgot_password } from "@/api"
import { showToast,showDialog } from "vant";

const username = ref<string>("")
const countNum = ref<number>(60)
const disEmail = ref<boolean>(false)

const sendEmail = () => {
  if(!username.value)return showToast('请先输入邮箱')
  forgot_password(username.value).then((res: any) => {
    if (res.code === 0) {
      disEmail.value = true
      showDialog({
        message: '已发送到您的邮箱，请查收后点击邮件中链接重置密码',
      })
      const intervalId = setInterval(() => {
        if (countNum.value > 0) {
          countNum.value--
        } else {
          disEmail.value = false
          countNum.value = 60
          clearInterval(intervalId)
        }
      }, 1000)
    }
  })
}
</script>

<style lang="less" scoped>
.reset-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 50px;
  padding: 0 25px;

  .reset-img {
    width: 100px;
    height: 100px;
  }

  .reset-top-left {
    font-size: 20px;
    color: #000;
  }
}

.input-one {
  padding: 0 25px;
  margin-bottom: 20px;

  .input-name {
    width: 94%;
    height: 45px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    padding-left: 16px;
    border-radius: 26px;
    border: 1px solid #dddddd;
    font-size: 14px;
    color: #333;
  }
}

.reset-sub {
  padding: 50px 16px 0 16px;
  margin-bottom: 20px;
  font-weight: bold;
}
</style>
