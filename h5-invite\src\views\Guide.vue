<template>
  <div class="container">
    <div class="coupon-card">
      <div class="coupon-header">
        <h1 class="offer-text">{{ state.goodsList.title }}</h1>
        <span
          class="promo-text"
          v-html="state.goodsList.detail"
        ></span>
        <div class="max-img">
          <van-image
            @click="maxImgs(state.goodsList.detail_img)"
            fit="contain"
            height="200"
            :src=" state.goodsList.detail_img || ''"
          >
            <template v-slot:loading>
              <van-loading type="spinner" size="30" />
            </template>
          </van-image>
        </div>
        <p class="description">
          {{  state.goodsList.detail_two || "" }}
        </p>
      </div>
      <div class="coupon-actions">
        <div class="coupon-actions-text">
          {{ state.goodsList.discount_code || "" }}
        </div>
        <button
          class="btn copy-btn"
          @click="copyCode(state.goodsList.discount_code || '')"
        >
          Copy
        </button>
      </div>
      <button class="btn shop-now-btn" @click="shopNow">Go to Amazon</button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, onMounted } from "vue"
import config from "@/config"
import { copyCode } from "@/utils"
import { get_product_discount_info, stat_link_jump, stat_open } from "@/api"
import type { listData } from "@/api/base"
import { showLoadingToast, showImagePreview } from "vant"
import { md5 } from "js-md5"

const state = reactive({
  goodsList: {} as listData,
})

const maxImgs = (img: string) => {
  showImagePreview([img])
}

const fetch = () => {
  get_product_discount_info(config.token).then((res: any) => {
    if (res.resultCode === 0) {
      if (res.content && res.content.length > 0) {
        const item = res.content.find((item:listData)=> item.discount_code === config.discount_code)
        if(item){
          state.goodsList = item
        }else {
          state.goodsList = res.content[0]
        }
      }
    }
  })
}

// 跳转到购物页面
const shopNow = () => {
  showLoadingToast({
    message: "Loading...",
    forbidClick: true,
  })
  stat_link_jump(
    config.token,
    encodeURIComponent(state.goodsList.url_link),
    md5(
      `${config.token}/${encodeURIComponent(state.goodsList.url_link) + "EKDB_ni&Hb&Zt&zz^7qn9"}`
    )
  )
  setTimeout(() => {
    window.location.href = decodeURIComponent(state.goodsList.url_link)
  }, 1500)
}

const statOpen = () => {
  stat_open(config.token)
}

onMounted(() => {
  fetch()
  statOpen()
})
</script>

<style lang="less" scoped>
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #007bff; /* 背景颜色与图片一致 */
  padding: 16px;
  box-sizing: border-box;
}

.coupon-card {
  background-image: url("../assets/cart.png");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  padding: 20px 20px 40px 20px;
  min-height: 380px;
  position: relative;
  width: 100%;
}
.coupon-header {
  margin-bottom: 20px;
}
.promo-text {
  font-size: 16px;
  font-weight: bold;
  color: #222;
  min-height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 21px;
  margin-bottom: 20px;
}
.offer-text {
  font-size: 38px;
  font-weight: bold;
  color: #183bee;
  text-align: center;
  margin-bottom: 10px;
  min-height: 76px;
}

.description {
  font-size: 16px;
  color: red;
  margin-bottom: 5px;
  text-align: center;
  padding-top: 20px;
  height: 16px;
}

.coupon-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f6f6f6;
  border: 1px solid #dddddd;
  font-size: 20px;
  border-radius: 28px;
  height: 50px;
  color: #222;
}
.coupon-actions-text {
  padding-left: 20px;
}
.max-img {
  display: flex;
  justify-content: center;
  height: 200px;
}
.btn {
  height: 50px;
  border: none;
  border-radius: 28px;
  cursor: pointer;
  font-size: 20px;
  transition: background-color 0.3s;
}

.copy-btn {
  width: 101px;
  background-color: #183bee;
  color: white;
}

.shop-now-btn {
  width: 100%;
  background-color: #183bee;
  color: white;
  margin-top: 20px;
  padding: 12px;
}

.copy-btn:hover,
.shop-now-btn:hover {
  background-color: #183bee;
}
</style>
