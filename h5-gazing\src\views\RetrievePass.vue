<template>
  <Header title="" :left-type="1" />
  <div class="pages">
    <div class="reset-top">
      <div class="reset-top-left">重置密码</div>
      <img class="reset-img" src="../assets/logo.png" alt="logo" />
    </div>
    <div class="input-one" v-if="re_pass_type === '1'">
      <input
        class="input-name"
        v-model="old_password"
        type="password"
        placeholder="Please enter your old password"
      />
    </div>
    <div class="input-one">
      <input
        class="input-name"
        v-model="new_password"
        type="password"
        placeholder="Please enter your new password"
      />
    </div>
    <div class="input-one">
      <input
        class="input-name"
        v-model="re_new_password"
        type="password"
        placeholder="Please verify your new password"
      />
    </div>
    <div class="reset-sub">
      <van-button class="reset-btn" round block type="primary" color="#3D5AFD" @click="sendEmail">
        重置密码
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import Header from "@/components/Header.vue"
import { reset_password_proactively, reset_password_passive } from "@/api"
import { showFailToast, showToast } from "vant"
import { useRoute, useRouter } from "vue-router"
import config from "@/config"

const route = useRoute()
const router = useRouter()
const old_password = ref<string>("")
const new_password = ref<string>("")
const re_new_password = ref<string>("")
const re_pass_type = ref<string>(String(route.params.type))
const token = ref<string>(String(route.query.token))
const random = ref<string>(String(route.query.random))

const sendEmail = () => {
  if (!re_new_password.value) return showFailToast("请输入密码后再提交")
  if (new_password.value !== re_new_password.value)
    return showFailToast("输入新密码不一致,请重新输入")
  if (re_pass_type.value === "1") {
    reset_password_proactively(old_password.value, new_password.value).then((res: any) => {
      if (res.code === 0) {
        goHome()
      }
    })
  } else {
    reset_password_passive(new_password.value, token.value, random.value).then((res: any) => {
      if (res.code === 0) {
        goHome()
      }
    })
  }
}

const goHome = () => {
  localStorage.clear()
  sessionStorage.clear()
  if (config.rememberAccount.value) {
    localStorage.setItem("username", String(config.username.value))
  }
  showToast("重置成功")
  if (re_pass_type.value === "1") {
    setTimeout(() => {
      router.push("/guide")
    }, 2000)
  }
}
</script>

<style lang="less" scoped>
.pages {
  padding: 0 10%;
}
.reset-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 50px;

  .reset-img {
    width: 80px;
    height: 80px;
  }

  .reset-top-left {
    font-size: 20px;
    font-weight: bold;
    color: #000;
  }
}

.input-one {
  margin-bottom: 20px;

  .input-name {
    width: 93%;
    height: 45px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    padding-left: 16px;
    border-radius: 26px;
    border: 1px solid #dddddd;
    font-size: 14px;
    color: #333;
  }
}

.reset-pass {
  font-size: 12px;
  color: #3d5afd;
  display: flex;
  justify-content: flex-end;
}

.reset-sub {
  padding-top: 30px;
  margin-bottom: 20px;
  .reset-btn {
    font-size: 16px;
  }
}
</style>
