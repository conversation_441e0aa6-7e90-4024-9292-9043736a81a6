<template>
  <van-popup
    class="payment-popup"
    v-model:show="showBottom"
    position="bottom"
    closeable
    round
    @close="payClose"
  >
    <h1 class="title">确认付款</h1>
    <div class="parameter">
      <p class="price">
        <GoodsPrice :price="price" />
      </p>
    </div>
    <van-cell>
      <ApplePay :price="String(price)" :order-id="String(orderId)" />
    </van-cell>
  </van-popup>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import GoodsPrice from './GoodsPrice.vue'
import ApplePay from './ApplePay.vue'
import { showToast } from 'vant'

const props = defineProps({
  price: Number,
  orderId: String
})

const showBottom = ref<boolean>(false)
const orderId = ref(props.orderId)
const handleDisplay = (data: string) => {
  orderId.value = data
  showBottom.value = true
}

// 只是为了展示
defineExpose({ handleDisplay })

const payClose = () => {
  showToast('您取消了支付')
}
</script>
<style lang="less" scoped>
.payment-popup {
  padding: 0 0 20px 0;

  .title {
    padding: 15px;
  }

  .parameter {
    padding: 10px 0;

    .price {
      color: #f18d00;
      text-align: center;
      font-size: 30px;
      font-weight: bolder;
    }
  }
}
</style>
