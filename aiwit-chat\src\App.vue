<template>
  <router-view />
  <div class="footer-flex"></div>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { onMounted } from 'vue'
import {
  getUrlParamsObject,
  getConfig,
  checkplatform,
  setAxiosHeader,
} from './utils'
import config from '@/config'
import http from '@/api/index'

const i18n = useI18n()
let getUrlParams = null
if (checkplatform() == 'android') {
  getUrlParams = getUrlParamsObject(window.location.href)
} else {
  getUrlParams = getUrlParamsObject(window.location.search)
}
const { lang } = getUrlParams
getConfig(config, getUrlParams)

onMounted(() => {
  i18n.locale.value = ['cn', 'zh', 'zh-cn'].includes(lang.toLowerCase())
    ? 'zh'
    : 'en'
  const params = [
    { key: 'AppName', value: config.app_name },
    {
      key: 'AppVersion',
      value:
        config.version.indexOf('_') !== -1
          ? config.version.slice(0, config.version.indexOf('_'))
          : config.version,
    },
    { key: 'Os', value: checkplatform() == 'ios' ? '1' : '2' },
  ]
  setAxiosHeader(http.service, params)
})
</script>
<style  scoped>
.footer-flex{
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 10px;
  background-color: #f7f7f7;
}
</style>