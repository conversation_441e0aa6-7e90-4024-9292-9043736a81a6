<template>
  <div ref="timeAxisDom" class="time-axis">
    <div ref="redLineDom" class="red-line"></div>
    <div ref="svgBoxDom" class="svg-box">
      <svg ref="svgDataDom" class="data-svg">
        <path :d="svgDataDrawPath" fill="#3d5afd"></path>
      </svg>
      <svg ref="svgTimeScaleDom" class="time-scale">
        <path :d="svgTimeScale" stroke="#666666" stroke-width="1"></path>
      </svg>
      <canvas ref="svgCanvas" class="time-scale-text"></canvas>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineExpose, defineEmits } from "vue"
import type { syncData } from "@/api/base"

// 时间轴刻度数据
interface TimeAxisData {
  second: number // 当前刻度在24小时内处于第几秒
  time: string // 时:分:秒
}
// 视频数据新增字段
interface PosiData {
  posiX: number // 数据在时间轴内所在的位置
  posiH: number // 数据在时间轴内应有的高度
  posi: string // 数据在<path>中的规范值
  ms: number // 数据在24小时内处于第几秒
}
type MergeType = syncData & TimeAxisData & PosiData
interface VideoData extends MergeType {}

const props = defineProps({
  videoListTouchIsOpen: Boolean,
})

const svgWidth = ref<number>(window.innerWidth * 0.25 - 10) // 时间轴宽度
let viedoData = ref<VideoData[]>([]) // 视频数据列表
const totalHeight = ref<number>(13000) // 时间轴总该高度 (注意: 高度超过10925且window.devicePixelRatio为3时, canvas绘画结果会变形)
const svgDataDrawPath = ref<string>("") // 时间轴右边的视频数据svg
const svgTimeScale = ref<string>("") // 时间轴svg
const secondsToADay = ref<number>(86400) // 一整天的秒数
const secondsToADay2 = ref<number>(86400) // 一整天的秒数(用于创建时间轴数据)
const redLineDom = ref<HTMLElement | null>(null) // 红线 DOM
const timeAxisDom = ref<HTMLElement | null>(null)
const svgBoxDom = ref<HTMLElement | null>(null)
const svgCanvas = ref<HTMLElement | null>(null) // 时间轴左侧数字 DOM
const svgTimeScaleDom = ref<HTMLElement | null>(null) // 时间轴左侧刻度 DOM
const svgDataDom = ref<HTMLElement | null>(null) // 时间轴右侧数据 DOM
const proportion = ref(0) // 时间轴高度与秒数的比例
const timeData = ref<TimeAxisData[]>([]) // 一整天的时间数据
const timeScaleList = ref<string[]>([]) // 时间轴左边刻度path数据
const dpr = ref<number>(window.devicePixelRatio || 1) // 设备物理像素与CSS像素的比率, 此处用在canvas
const scrollFinallyTimer = ref<NodeJS.Timeout | undefined>(undefined) // 用于监听滚动时间轴停下来之后, 以获取最终刻度时间
let listenSvgScroll = (e: Event): void => {} // 监听滚动
const historyPosi = ref<number>(0) // 存储最后一次红线刻度位置
const defaultIsOpen = ref<boolean>(true) // 是否设置默认刻度
const touchIsMove = ref<number>(0) // 触屏之后是否有移动, 非0则移动
const videoListTouchIsOpen = ref<boolean>(props.videoListTouchIsOpen)
/**
 * svgDataScrollEmit: 将红线刻度位置发送给视频数据列表
 * touchOpen: 告诉父元素是否正在进行触屏事件
 */
const emit = defineEmits(["svgDataScrollEmit", "touchOpen"])
const targetIndex = ref<number>(0) // 存储最后一次红线刻度位置

// 处理并绘画时间轴右边的数据
const getSvgDataPath = (list: VideoData[]) => {
  let posiList: string[] = [] // path的属性d的值
  list.map((item: VideoData) => {
    let tl = <number[]>item.time?.split(":")?.map(v => parseFloat(v)) // 获取时间时分秒
    let ms = tl[0] * 60 * 60 + tl[1] * 60 + tl[2] * 1 // 获取该时间的总秒数
    let timeLong = <number>(item.duration / 1000) // 获取单个视频总秒数(毫秒 转为 秒)
    proportion.value = totalHeight.value / secondsToADay.value // 计算可视高度 和 总秒数 的比例
    let posiX = ms * proportion.value // 获取当前时间 在可是高度中 所处的位置
    let posiH = timeLong / 10 // 单个视频播放秒数按比例缩小(可调整, 在界面上不影响视觉效果就行)
    item.posiX = parseFloat((totalHeight.value - posiX).toFixed(2)) // 翻转数据集
    item.posiH = posiH
    let pathD = dataTransDataToPosi(item) // 转为<path d="">数据
    item.posi = pathD
    item.ms = ms
    posiList.push(pathD)
    return item
  })
  viedoData.value = list // 存储数据备用
  svgDataDrawPath.value = posiList.join(",")

  getTimeScale() // 创造并绘画时间轴数据

  setTimeout(() => {
    // 设置红线默认位置
    if (defaultIsOpen.value) {
      setCurrentPosi(list[0]?.posiX) // 默认红线刻度指向第一个数据的位置
      defaultIsOpen.value = false
    }

    listenToTouchEvent(cp => {
      // 监听时间轴滚动, 停止后, 获取红线最终指向的时间刻度
      contrastRecentlyData(list, cp) // 计算距离该时间刻度最近的数据, 并吸附到该条数据上
    })
  }, 200)
}

const resetDefaultPosi = (isOpen: boolean) => {
  defaultIsOpen.value = isOpen
}

// 转为<path d="">数据
const dataTransDataToPosi = (d: VideoData): string => {
  let resPosi = `M0 ${d.posiX - d.posiH}, 0 ${d.posiX}, 10 ${d.posiX}, 10 ${d.posiX - d.posiH}, 0 ${
    d.posiX - d.posiH
  }`
  return resPosi
}

// 创造时间轴数据, 获取每隔十分钟的时分数据
const getTimeScale = () => {
  // 从有数据的地方开始绘画时间轴刻度, 减少循环
  const firstDataMs: number = viedoData.value.length ? viedoData.value[0]!.ms : 0
  timeData.value = [] // 初始化
  // const lastDataMs: number = viedoData.value.length ? viedoData.value[viedoData.value.length - 1]!.ms : secondsToADay2.value
  secondsToADay2.value =
    secondsToADay2.value - parseFloat(((secondsToADay2.value - firstDataMs) / 600).toFixed(0)) * 600
  if (secondsToADay2.value < firstDataMs) secondsToADay2.value += 600 // 确保首条数据之前有显示刻度

  while (secondsToADay2.value >= 0) {
    let times = [],
      t = 0,
      m = 0,
      s = secondsToADay2.value
    if (secondsToADay2.value >= 3600) {
      t = parseFloat((secondsToADay2.value / 3600 + "").split(".")[0])
    }
    s = secondsToADay2.value - t * 3600
    times.push(t < 10 ? "0" + t : t)
    if (secondsToADay2.value >= 60) {
      m = parseFloat((s / 60 + "").split(".")[0]) * 1
    }
    times.push(m < 10 ? "0" + m : m)
    if (secondsToADay2.value >= 0) {
      timeData.value.push({
        second: secondsToADay2.value,
        time: times.join(":"),
      })
      secondsToADay2.value -= 600
    }
  }
  setTimeScaleToSvg()
}

// 绘画时间轴
const setTimeScaleToSvg = () => {
  // 使用canvas绘画时间轴文字
  let sc = svgCanvas.value as HTMLCanvasElement
  sc.width = (svgWidth.value - 10) * dpr.value
  sc.height = totalHeight.value * dpr.value
  sc.style.width = svgWidth.value - 10 + "px"
  sc.style.height = totalHeight.value + "px"
  let svgCtx = sc.getContext("2d") as CanvasRenderingContext2D
  svgCtx.font = `${14 * dpr.value}px Arial`
  svgCtx.fillStyle = `#666666`

  timeScaleList.value = [] // 初始化

  // 生成时间轴刻度
  timeData.value.map((r: TimeAxisData, i: number) => {
    let ms: number = r.second
    let posiH: number = 1
    let posiX: number = parseFloat((totalHeight.value - ms * proportion.value).toFixed(2))
    let res: string = scaleTransDataToPosi(posiX)
    timeScaleList.value.push(res)
    let offsetY: number = i !== timeData.value.length - 1 ? 5 : 0 // 向上或者向下偏移多少px
    svgCtx.fillText(r.time, (svgWidth.value - 70) * dpr.value, (posiX + offsetY) * dpr.value)
  })
  timeScaleList.value.reverse()
  svgTimeScale.value = timeScaleList.value.join(",")
}

// 转为<path d="">数据
const scaleTransDataToPosi = (posiX: number): string => {
  let resPosi = `M${svgWidth.value - 25} ${posiX} L${svgWidth.value - 10} ${posiX}`
  return resPosi
}

// 设置时间轴位置
const setCurrentPosi = (num: number = 0, type: string = "top") => {
  if (type === "top") {
    historyPosi.value = num ? num - 100 : 0
  } else if (type === "index") {
    if (viedoData.value[num]) {
      historyPosi.value = viedoData.value[num].posiX - 100
    }
  }
  timeAxisDom.value!.scrollTop = historyPosi.value
}

const listenToTouchEvent = (callback: (d: number) => void) => {
  timeAxisDom.value!.addEventListener("touchstart", scrollTouchstart)
  timeAxisDom.value!.addEventListener("touchmove", scrollTouchmove)
  // timeAxisDom.value.addEventListener('touchend', scrollTouchend)

  listenSvgScroll = e => {
    let targetDom = <HTMLElement | null>e.target
    // 红线刻度所处位置
    if (touchIsMove.value && !videoListTouchIsOpen.value) {
      historyPosi.value = targetDom!.scrollTop + 100
      callback(historyPosi.value)
    }
    // 当用力滑动滚动条然后快速松开时, 会有个惯性滚动的过程, 等待惯性滚动完成后, 重新设置红线位置
    if (touchIsMove.value) {
      if (scrollFinallyTimer.value) {
        clearTimeout(scrollFinallyTimer.value)
        scrollFinallyTimer.value = undefined
        scrollFinallyTimer.value = setTimeout(() => scrollTouchend(), 200)
      }
    }
  }
}

// 监听手指触屏开始
function scrollTouchstart(e: Event) {
  // 添加滚动监听事件
  timeAxisDom.value!.removeEventListener("scroll", listenSvgScroll)
  timeAxisDom.value!.addEventListener("scroll", listenSvgScroll)
  emit("touchOpen", true)
}

const scrollTouchmove = () => {
  // 保存移动痕迹
  touchIsMove.value++
  emit("touchOpen", true)
}

const scrollTouchend = () => {
  if (touchIsMove.value && !videoListTouchIsOpen.value) {
    setCurrentPosi(historyPosi.value)
    touchIsMove.value = 0
  }
  emit("touchOpen", false)
}

// 计算并设置红线位置
const contrastRecentlyData = (resData: VideoData[], cp: number) => {
  var allRecently: number[] = []
  var posiXList: number[] = []
  resData.map(item => {
    allRecently.push(Math.abs(parseFloat((item.posiX - cp).toFixed(0))))
    posiXList.push(item.posiX)
  })
  const minRecently = Math.min(...allRecently)
  targetIndex.value = allRecently.indexOf(minRecently)

  historyPosi.value = resData[targetIndex.value].posiX

  // 将数据下标返回给父组件
  emit("svgDataScrollEmit", targetIndex.value)
}

onMounted(() => {
  // 动态设置宽高度, 确保不会被转为vh和vw, 避免影响计算精确度
  svgBoxDom.value!.style.height = totalHeight.value + "px"
  svgDataDom.value!.style.height = totalHeight.value + "px"
  svgTimeScaleDom.value!.style.height = totalHeight.value + "px"
  redLineDom.value!.style.top = 100 + "px"
  timeAxisDom.value!.style.width = svgWidth.value + "px"
  svgDataDom.value!.style.width = 10 + "px"
  svgTimeScaleDom.value!.style.width = svgWidth.value - 10 + "px"
})

defineExpose({ getSvgDataPath, setCurrentPosi, resetDefaultPosi })
</script>

<style lang="less" scoped>
.time-axis {
  // background: white;
  width: 100px;
  height: calc(51.5vh + 8px);
  overflow-y: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
  .red-line {
    width: 100%;
    height: 1px;
    background: red;
    position: sticky;
    left: 10px;
    // top: 100px;
    z-index: 900;
  }
  .svg-box {
    width: 100%;
    position: relative;
    .data-svg {
      width: 10px;
      position: absolute;
      top: 0;
      right: 0;
      background: #e3e3e3;
    }
    .time-scale-text {
      position: absolute;
      top: 0;
      left: 0;
    }
  }
}
</style>
