<template>
  <div class="pages-data">
    <van-nav-bar
      :fixed="true"
      title="注销账号"
      :safe-area-inset-top="config.platform === 'ios'"
      @click-left="onClickLeft"
      :border="false"
      class="nav-bar"
    >
      <template #left>
        <div class="header-left">
          <van-icon name="arrow-left" color="#303A58" size="18" />
        </div>
      </template>
    </van-nav-bar>
    <div v-if="showCancel === 1" class="email-box">
      <div class="pass-tip">提交申请前请先输入密码，确认是您本人操作</div>
      <div class="email-name">{{ config.username.value }}</div>
      <div class="input-wrapper">
        <input
          class="input-name"
          v-model="password"
          name="password"
          placeholder="输入密码"
          type="password"
        />
      </div>
      <van-button
        class="login-btn"
        round
        block
        type="primary"
        color="#3D5AFD"
        @click="confirmCancel"
      >
        确定
      </van-button>
    </div>
    <div class="email-box" v-else-if="showCancel === 2">
      <div class="email-suu">
        <img class="email-suu-img" src="../assets/add.png" alt="" />
      </div>
      <van-button
        class="login-btn"
        round
        block
        type="primary"
        color="#3D5AFD"
        @click="cancelComplete"
      >
        完成
      </van-button>
      <div class="time-data">{{ countNum }} 秒后跳转登陆页</div>
    </div>
    <div class="email-box" v-else>
      <div class="email-data">
        <div class="email-top">
          <img class="email-img" src="../assets/acc.png" alt="" />
          <div class="email-top-text">如何注销账号</div>
        </div>
        <div class="email-center">1.您的视频记录将被清空，会员权益也将被清空并无法恢复；</div>
        <div class="email-center">2.没有未完结的在线交易；</div>
        <div class="email-center">3.您的账号不存在正在进行中的违规处罚与权限记录；</div>
        <div class="email-center">
          4.当前账户无法再登陆，但您可以使用当前账户重新注册一个全新的账户；
        </div>
      </div>
      <van-button class="login-btn" round block type="primary" color="#3D5AFD" @click="cancelEmail">
        申请注销
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useHomeStore } from "@/stores/home"
import { computed, ref } from "vue"
import { showDialog, showToast } from "vant"
import config from "@/config"
import { deregister } from "@/api"
import { useRouter } from "vue-router"

const homeStore = useHomeStore()
const router = useRouter()
const showCancel = ref<number>(0)
const password = ref<string>("")
const countNum = ref<number>(5)
const intervalId = ref<any>(null)

const deviceList = computed(() => {
  return homeStore.state.deviceList
})

const fetch = () => {
  intervalId.value = setInterval(() => {
    if (countNum.value > 0) {
      countNum.value--
    } else {
      cancelComplete()
    }
  }, 1000)
}

const cancelEmail = () => {
  if (deviceList.value.length) {
    showDialog({
      message: "此账号还关联有设备，暂不能注销账号。如果要注销账号，请删除此账号上面的所有设备.",
    })
  } else {
    showCancel.value = 1
  }
}

const confirmCancel = () => {
  if (!password.value) return showToast("请先输入密码后再提交")
  deregister(password.value).then((res: any) => {
    if (res.code === 0) {
      showCancel.value = 2
      fetch()
    }
  })
}

const cancelComplete = () => {
  localStorage.clear()
  sessionStorage.clear()
  if (config.rememberAccount.value) {
    localStorage.setItem("username", String(config.username.value))
  }
  router.push("/guide")
  setTimeout(() => {
    window.location.reload()
  }, 80);
}

const onClickLeft = () => {
  if (showCancel.value === 2) return
  if (showCancel.value === 1) {
    showCancel.value = 0
  } else {
    router.go(-1);
  }
}
</script>

<style lang="less" scoped>
.email-box {
  padding: 20px;
  .email-data {
    background-color: #fff;
    border-radius: 16px;
    padding: 16px;
    height: 70vh;
    margin-bottom: 20px;
    .email-top {
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20px;
      padding-top: 10px;
      .email-img {
        width: 90px;
        height: 81px;
        margin-bottom: 15px;
      }
      .email-top-text {
        font-size: 18px;
        color: #f65252;
      }
    }
    .email-center {
      font-size: 16px;
      color: #333;
      line-height: 25px;
      margin-bottom: 10px;
    }
  }
  .pass-tip {
    font-size: 14px;
    color: #888ea0;
    text-align: center;
    padding-top: 30px;
    margin-bottom: 30px;
  }
  .email-name {
    font-size: 16px;
    color: #000;
    font-weight: bold;
    text-align: center;
    height: 18px;
    margin-bottom: 20px;
  }
  .input-wrapper {
    margin-bottom: 20px;
    .input-name {
      width: 83%;
      height: 45px;
      background-color: #fff;
      display: flex;
      align-items: center;
      padding-left: 16px;
      padding-right: 40px;
      border-radius: 26px;
      border: 1px solid #fff;
      font-size: 14px;
      color: #333;
    }
  }
  .email-suu {
    padding-top: 30px;
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    .email-suu-img {
      width: 90px;
      height: 81px;
    }
  }
  .time-data {
    padding-top: 20px;
    font-size: 14px;
    color: #848ca4;
    text-align: center;
  }
}
.header-left {
  display: inline-block;
  width: 36px;
  height: 36px;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
</style>
