import { createRouter, createWebHashHistory } from 'vue-router'
import ChatView from '../views/ChatView.vue'
import config from '@/config'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'chat',
      component: ChatView,
    },
    {
      path: '/chatList',
      name: 'chatList',
      component: () => import('../views/ChatList.vue')
    },
    {
      path: '/cloud',
      name: 'cloud',
      component: () => import('../views/CloudStorage.vue')
    },
    {
      path: '/cloudBuy',
      name: 'cloudBuy',
      component: () => import('../views/CloudStorageBuy.vue')
    }
  ],
})

// 路由拦截
// router.beforeEach((to, from, next) => {
//   if (config.webview === 'cloud' && to.path == '/') {
//     next('/cloud')
//   }else {
//     next()
//   }
// })

export default router
