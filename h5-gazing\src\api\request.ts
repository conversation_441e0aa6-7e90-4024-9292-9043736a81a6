import axios from "axios"
import configs from "@/config"
import router from "@/router"
import { encrypKeyRSA, generateAESKey, encrypt, decrypt } from "@/utils"
import { showToast, showLoadingToast, closeToast, setToastDefaultOptions } from "vant"
import config from "@/config"

//  设置提示时效
setToastDefaultOptions({ duration: 3500 })

const service = axios.create({
  // 环境变量，需要在.env文件中配置
  baseURL: "",
  timeout: 20000,
})

// loading 次数
// let loadingCount = 0
service.interceptors.request.use(
  config => {
    if (config.url?.startsWith("/service_api")) {
      config.baseURL = "https://api-v1.seewds.com/"
    }else {
      config.baseURL = import.meta.env.VITE_APP_BASE_API
    }
    config.headers["x-Token"] = configs.token
    config.headers["x-Date"] = configs.toRFC1123
    config.headers["x-key"] = encrypKeyRSA(configs.aesBase64Key)
    config.headers["x-App"] = configs.app_name.toLowerCase()
    config.headers["x-OS"] = configs.platform
    config.headers["x-Lang"] = configs.lang
    config.headers["x-Timezone"] = configs.timezone
    config.headers["x-App-ID"] = configs.app_id
    config.headers["x-App-Test"] = configs.app_is_dev
    config.headers["Authorization"] = "Bearer " + configs.token
    // showLoadingToast({
    //   message: "加载中...",
    //   //禁止背景点击
    //   forbidClick: true,
    // })
    // loadingCount++
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  response => {
    //关闭loading
    // loadingCount--
    // if (loadingCount === 0) {
    //   closeToast()
    // }
    return response
  },
  error => {
    closeToast()
    // 处理异常情况，根据项目实际情况处理或不处理
    if (error && error.response) {
      // 根据约定的响应码处理
      switch (error.response.status) {
        case 401:
          error.message = error.response.data.msg
          break
        case 403:
          error.message = "拒绝访问"
          break
        case 502:
          error.message = "服务器端出错"
          break
        default:
          error.message = `连接错误${error.response.status}`
      }
    } else {
      // 超时处理
      error.message = "服务器响应超时，请刷新当前页"
    }
    showToast(error.message)
    if (error.response.status === 401) {
      localStorage.clear()
      sessionStorage.clear()
      router.push("/guide")
      return
    }
    return Promise.resolve(error.response)
  }
)

// 封装请求
const Request = (
  url: string,
  options = { method: "", params: {} },
  headers = { "Content-Type": "text/plain", "x-Token": "", "x-Date": "" }
) => {
  let method = options.method || "get"
  let params = options.params || {}
  if (method === "get" || method === "GET") {
    return new Promise((resolve, reject) => {
      service
        .get(url, { params, headers })
        .then(res => {
          if (res && res.data) {
            if (res.data.code != 0) {
              showToast(res.data.msg ? res.data.msg : "服务器错误，请稍后再试")
            }
            resolve(res.data)
          }
        })
        .catch(err => {
          reject(err)
        })
    })
  } else {
    generateAESKey()
    return new Promise((resolve, reject) => {
      service
        .post(url, encrypt(JSON.stringify(params), config.aesKey), { headers })
        .then(res => {
          if (res && res.data) {
            res.data.data = JSON.parse(decrypt(res.data.data, config.aesKey))
            console.log(res.data)
            if (res.data.code != 0) {
              showToast(res.data.msg ? res.data.msg : "服务器错误，请稍后再试")
            }
            resolve(res.data)
          }
        })
        .catch(err => {
          reject(err)
        })
    })
  }
}
export default Request
