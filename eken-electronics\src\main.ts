import "./assets/main.css"

import { createApp } from "vue"
import App from "./App.vue"
import router from "./router"
import "normalize.css"

import { Form, Field, CellGroup, Uploader, <PERSON><PERSON>, Picker, <PERSON>up, To<PERSON> } from "vant"
import "vant/lib/index.css"
import "@vant/touch-emulator"
import "./styles/fonts.css"

const app = createApp(App)

app.use(router)
app.use(Form)
app.use(Field)
app.use(CellGroup)
app.use(Uploader)
app.use(Button)
app.use(Picker)
app.use(Popup)
app.use(Toast)

app.mount("#app")
