import CryptoJS from "crypto-js"
import config from "@/config"
import JSEncrypt from "jsencrypt"
import router from "@/router"
import { appLeave, setProperty,disconnectMqtt } from "@/components/WebRTCClient"
import { submitLogout } from "@/api"
import type { exportData } from "@/api/base"
import { showLoadingToast, showToast } from "vant"
import { get_oss_info } from "@/oss";

/**
 * @description 获取url地址参数
 * @param url
 * @returns {object|boolean}
 */

export function getUrlParamsObject(url: string) {
  if (url.indexOf("?") == -1) return false
  let arr = url.split("?")
  let params = arr[1].split("&")
  let obj = {} as any
  for (let i = 0; i < params.length; i++) {
    let param = params[i].split("=")
    obj[param[0]] = param[1]
  }
  return obj
}

/**
 * @description 赋值全局变量参数
 * @param obj
 * @returns {object}
 * @param obj1
 * @returns {object}
 */
export function getConfig(obj: object, obj1: object) {
  obj = Object.assign(obj, obj1)
}

export function getCurrentTime() {
  // 创建一个新的Date对象
  let today = new Date()
  // 获取年份
  let year = today.getFullYear()
  // 获取月份（注意：月份是从0开始的，所以需要加1）
  let month = today.getMonth() + 1
  // 获取日期
  let date = today.getDate()

  // 格式化月份和日期，确保它们始终是两位数
  let formattedMonth = month < 10 ? "0" + month : month
  let formattedDate = date < 10 ? "0" + date : date

  // 组合成最终的日期字符串
  return `${year}/${formattedMonth}/${formattedDate}`
}

// 把日期时间转成RFC1123格式
export function toRFC1123() {
  let now = new Date()
  config.toRFC1123 =  now.toUTCString()
}

// 公钥
let pubKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA594g9QJrtHPVtxPnFVnK
glgpXO877+wQ/uVogCS/6+0El5RHI+3barWs7dO0g7gEJamjhdgTEX5GOw3EuCd0
hlTGWdku0Scjd/Lp4cOGtYMZjHrEyb8Y2fD4QJwsT6cSTmqP0hfbrsxFFaE7A2f5
W94KFADSaPMdm9eMKCu9WWtWGgIXG42BjLnWmQrjTHGQFbZ1bZrtyOrdM6l2cYW0
4WetDOHmWZTGdl7q8CtaeF7sTfYtRzz/0ZILKub6RAssMlwHsO2fuvBvwcObp+zg
XnVZT2aa8JLQTjty5SrEOEyV5il96eDnZAmJJYseRG4xipZMiZQhCQX3se5Yp0EY
iwIDAQAB
-----END PUBLIC KEY-----`

//RSA加密
export function encrypKeyRSA(str: string) {
  let encryptStr = new JSEncrypt()
  encryptStr.setPublicKey(pubKey) //设置 加密公钥
  const data = encryptStr.encrypt(str) //进行加密
  return data
}

// 生成AESkey
export function generateAESKey() {
  // 生成一个随机的 32 字节（256 位）密钥
  const key = CryptoJS.lib.WordArray.random(16)
  // 将密钥转换为十六进制字符串
  const hexKey = key.toString()
  config.aesKey = hexKey
  toRFC1123()
  config.aesBase64Key = `${btoa(hexKey)};${btoa("1234567890123456")};${config.toRFC1123}`
}

// //AES加密
export function encrypt(word: any, key: string) {
  const wei = "1234567890123456"
  key = CryptoJS.enc.Utf8.parse(key)
  const iv = CryptoJS.enc.Utf8.parse(wei)
  var encrypted = CryptoJS.AES.encrypt(word, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  }).toString()
  return encrypted
}

//AES解密
export function decrypt(word: any, key: string) {
  const wei = "1234567890123456"
  key = CryptoJS.enc.Utf8.parse(key)
  const iv = CryptoJS.enc.Utf8.parse(wei)
  var decrypted = CryptoJS.AES.decrypt(word, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  })
  var decryptedText = decrypted.toString(CryptoJS.enc.Utf8)
  return decryptedText
}

// 根据时区转义年月日
export function timestampToDate(timestamp: number, timeZone: string) {
  // 创建一个新的 Date 对象，参数是毫秒数，所以需要将秒乘以 1000
  let date = new Date(timestamp * 1000)

  // 使用 toLocaleDateString 方法并指定时区和日期格式
  let options: any = {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    timeZone: timeZone,
  }

  return date.toLocaleDateString(undefined, options)
}

// 发送通知让APP传参数
export function reloadMall() {
  try {
    if (config.platform == "android") {
      window.android.send_params("发送参数给h5")
    } else {
      window.webkit.messageHandlers.send_params.postMessage("发送参数给h5")
    }
  } catch (log) {
    config.app_id = `APP-${config.user_id}`
    console.log("不在内嵌手机端,APPID取不到,发送通知让APP传参数", log)
  }
}

// 收到参数通知
export function receiveParams() {
  try {
    if (config.platform == "android") {
      window.android.receive_params("收到了传过来的参数")
    } else {
      window.webkit.messageHandlers.receive_params.postMessage("收到了传过来的参数")
    }
  } catch {
    console.log("不在内嵌手机端,收到参数通知")
  }
}

// 点击拉起APP时区界面
export function clickZone(is_primary:boolean) {
  if(!is_primary)return showToast('只有管理者才有修改权限')
  try {
    if (config.platform == "android") {
      window.android.click_time_zone("点击了时区")
    } else {
      window.webkit.messageHandlers.click_time_zone.postMessage("点击了时区")
    }
  } catch {
    console.log("不在内嵌手机端,点击拉起APP时区界面")
  }
}

// 点击复制
export function clickCopy(text: string) {
  try {
    if (config.platform == "android") {
      window.android.click_copy(text)
    } else {
      window.webkit.messageHandlers.click_copy.postMessage(text)
    }
  } catch {
    console.log("不在内嵌手机端,点击复制")
  }
}

// 点击截图到相册
export function screenshot(tip:string) {
  try {
    if (config.platform == "android") {
      window.android.click_screenshot(tip)
    } else {
      window.webkit.messageHandlers.click_screenshot.postMessage(tip)
    }
  } catch {
    console.log("不在内嵌手机端,点击截图到相册")
  }
}

// 点击侦测区域
export function detectArea() {
  try {
    if (config.platform == "android") {
      window.android.click_detect_area(
        decodeURIComponent(config.device.last_img_url),
        config.device.attributes.detection_area ?? 0,
        config.device.attributes.detection_area_list ?? ""
      )
    } else {
      window.webkit.messageHandlers.click_detect_area.postMessage({
        url: decodeURIComponent(config.device.last_img_url),
        enable: config.device.attributes.detection_area
          ? config.device.attributes.detection_area
          : "",
        list: config.device.attributes.detection_area_list
          ? config.device.attributes.detection_area_list
          : "",
      })
    }
  } catch {
    console.log("不在内嵌手机端,点击侦测区域")
  }
}

// 通知APP获取位置权限
export function acquire_location(item: object) {
  try {
    if (config.platform == "android") {
      window.android.acquire_location()
    } else {
      window.webkit.messageHandlers.acquire_location.postMessage({ ...item })
    }
  } catch (log) {
    console.log("不在内嵌手机端,通知APP获取位置权限", log)
  }
}

// 通知安卓APP提示用户需要麦克风权限
export function get_microphone() {
  try {
    if (config.platform == "android") {
      window.android.get_microphone("提示用户需要麦克风权限")
    }
  } catch {
    console.log("不在内嵌手机端,通知安卓APP提示用户需要麦克风权限")
  }
}

// 通知APP提示用户需要蓝牙权限
export function inform_bluetooth() {
  try {
    if (config.platform == "android") {
      window.android.inform_bluetooth("提示用户需要蓝牙权限")
    } else {
      window.webkit.messageHandlers.inform_bluetooth.postMessage("提示用户需要蓝牙权限")
    }
  } catch {
    console.log("不在内嵌手机端,通知APP提示用户需要蓝牙权限")
  }
}

// 通知APP获取蓝牙是否打开
export function get_bluetooth() {
  try {
    if (config.platform == "android") {
      window.android.get_bluetooth("用户是否打开蓝牙")
    } else {
      window.webkit.messageHandlers.get_bluetooth.postMessage("用户是否打开蓝牙")
    }
  } catch {
    router.push("configurationNetwork")
    console.log("不在内嵌手机端,用户是否打开蓝牙")
  }
}

// 通知APP开始蓝牙搜索
export function click_search_device() {
  try {
    if (config.platform == "android") {
      window.android.click_search_device("开始搜索蓝牙设备")
    } else {
      window.webkit.messageHandlers.click_search_device.postMessage("开始搜索蓝牙设备")
    }
  } catch {
    console.log("不在内嵌手机端,用户是否打开蓝牙")
  }
}

// 通知APP结束蓝牙搜索
export function end_search_bluetooth() {
  try {
    if (config.platform == "android") {
      window.android.end_search_bluetooth("结束搜索蓝牙设备")
    } else {
      window.webkit.messageHandlers.end_search_bluetooth.postMessage("结束搜索蓝牙设备")
    }
  } catch {
    console.log("不在内嵌手机端,通知APP结束蓝牙搜索")
  }
}

// 搜索到蓝牙以后返回要通知APP断开
export function break_bluetooth() {
  try {
    if (config.platform == "android") {
      window.android.break_bluetooth("断开蓝牙设备")
    } else {
      window.webkit.messageHandlers.break_bluetooth.postMessage("断开蓝牙设备")
    }
  } catch (log) {
    console.log("不在内嵌手机端,搜索到蓝牙以后返回要通知APP断开", log)
  }
}

// 通知APP用户输入的wifi账号密码
export function send_wifi_params(name: string, password: string, qrkey: string) {
  try {
    if (config.platform == "android") {
      window.android.send_wifi_params(name, password, qrkey)
    } else {
      window.webkit.messageHandlers.send_wifi_params.postMessage({ name, password, qrkey })
    }
  } catch {
    console.log("不在内嵌手机端,通知APP用户输入的wifi账号密码")
  }
}

// 点击套餐订阅跟APP交互
export function subscribe(item: exportData) {
  showLoadingToast({
    message: "Loading...",
    forbidClick: true,
  })
  item.device_sn = config.device.device_sn
  item.src = 4
  try {
    if (config.platform == "android") {
      window.android.click_subscribe(item.product_id)
    } else {
      window.webkit.messageHandlers.click_subscribe.postMessage({ ...item })
    }
  } catch (log) {
    console.log("不在内嵌手机端,点击套餐订阅跟APP交互", log)
  }
}

// 发送视频流给原生
export function send_file_blob(data:any) {
  try {
    if (config.platform == "android") {
      window.android.send_file_blob(JSON.stringify(data))
    } else {
      window.webkit.messageHandlers.send_file_blob.postMessage(data)
    }
  } catch (log) {
    console.log("不在内嵌手机端,发送视频流给原生", log)
  }
}

// 通知APP视频流下载完成
export function download_file_success() {
  try {
    if (config.platform == "android") {
      window.android.download_file_success('下载完成')
    } else {
      window.webkit.messageHandlers.download_file_success.postMessage('视频转换完成')
    }
  } catch (log) {
    console.log("不在内嵌手机端,通知APP视频流下载完成", log)
  }
}

// 通知APP视频流下载失败
export function download_file_error() {
  try {
    if (config.platform == "android") {
      window.android.download_file_error('下载失败')
    } else {
      window.webkit.messageHandlers.download_file_error.postMessage('下载失败')
    }
  } catch (log) {
    console.log("不在内嵌手机端,通知APP视频流下载失败", log)
  }
}

// 通知APP打开相机
export async function click_camera() {
  try {
    const res:any = await get_oss_info()
    if (config.platform == "android") {
      window.android.click_camera(JSON.stringify(res))
    } else {
      window.webkit.messageHandlers.click_camera.postMessage({...res})
    }
  } catch {
    console.log("不在内嵌手机端,通知APP打开相机")
  }
}

// 通知APP打开相册
export async function click_album() {
  try {
    const res:any = await get_oss_info()
    if (config.platform == "android") {
      window.android.click_album(JSON.stringify(res))
    } else {
      window.webkit.messageHandlers.click_album.postMessage({...res})
    }
  } catch {
    console.log("不在内嵌手机端,通知APP打开相册")
  }
}

// 接收APP蓝牙连接成功
export function bluetooth_link_successful() {}

// 接收侦测区域参数
export function detectAreaParams(enable: number, width: string, height: string, list: string) {
  setProperty([
    { category: "detection_area", value: enable },
    { category: "detection_area_enable", value: enable },
    { category: "detection_area_list", value: list },
  ])
}

// 接收APP传过来的参数
export function init_params(
  appName: string,
  appLang: string,
  appOS: string,
  appVersion: string,
  appId: string,
  informToken: string,
  appTestflight: string,
  appIsDev: string
) {
  if (appName) {
    config.app_name = appName
  }
  if (appLang) {
    config.lang = appLang
  }
  if (appOS) {
    config.app_os = Number(appOS)
  }
  if (appVersion) {
    config.app_version = appVersion
  }
  if (appId) {
    config.app_id = appId
  }
  if (informToken) {
    config.app_informToken = informToken
  }
  if (appTestflight) {
    config.app_test_flight = Number(appTestflight)
  }
  if (appIsDev) {
    config.app_is_dev = Number(appIsDev)
  }
  receiveParams()
}

// APP点击通知调用的方法
export function clickNotification(device_sn: string) {
  localStorage.setItem('device_sn',device_sn)
}

//等待APP运行后再执行通知任务
export function executeNotification (device_sn:string){
  if (device_sn && config.deviceList.length) {
    config.deviceList.some(item => {
      if (item.device_sn === device_sn) {
        config.product_key = item.product_key
        config.device_sn = item.device_sn
        config.device = item
        if (config.device.area === "US") {
          config.webrtc_item = config.webrtc_list.US
        } else if (config.device.area === "SG") {
          config.webrtc_item = config.webrtc_list.SG
        } else if (config.device.area === "DE") {
          config.webrtc_item = config.webrtc_list.DE
        } else if (config.device.area === "CN") {
          config.webrtc_item = config.webrtc_list.CN
        }
        config.wakeUp_type = 1
        return
      }
    })
    localStorage.removeItem("device_sn")
    if (config.device.status === 1 || config.device.status === 2) {
      router.push("/deviceDetails")
    } else {
      router.push("/playback")
    }
  }
}

// APP后台运行通知服务器
export function sendAppLeave() {
  appLeave()
}

// APP后台运行重新回到APP时通知服务器
export function opendAppLeave() {
  // SetAppInfo()
  window.location.reload()
}


// 转义时间
export function convertMillisecondsToTime(milliseconds: number, type?: string) {
  const totalSeconds = Math.floor(milliseconds / 1000)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60

  const hoursPad = type === "mute" ? 1 : 2;
  
  if (hours > 0 || type === "mute") {
    return [
      hours.toString().padStart(hoursPad, "0"),
      minutes.toString().padStart(2, "0"),
      seconds.toString().padStart(2, "0"),
    ].join(":")
  }
  
  return [
    minutes.toString().padStart(2, "0"),
    seconds.toString().padStart(2, "0"),
  ].join(":")
}

export function base64EncodeM3U8(m3u8Content: string) {
  return btoa(m3u8Content)
}

// 退出登陆清缓存并刷新页面
export function clearLocalCache() {
  submitLogout().then((res: any) => {
    if (res.code === 0) {
      localStorage.clear()
      sessionStorage.clear()
      disconnectMqtt()
      if(config.rememberAccount.value){
        localStorage.setItem("username", String(config.username.value))
      }
      router.push('/guide')
      setTimeout(() => {
        window.location.reload()
      }, 100);
    }
  })
}

// 初始化媒体设备兼容性
export function initMediaDevicesPolyfill() {
  // 兼容性处理 mediaDevices
  if (navigator.mediaDevices === undefined) {
    Object.defineProperty(navigator, 'mediaDevices', {
      value: {},
      writable: true
    })
  }

  // 兼容性处理 getUserMedia
  if (navigator.mediaDevices.getUserMedia === undefined) {
    navigator.mediaDevices.getUserMedia = function(constraints) {
      const getUserMedia = (navigator as any).webkitGetUserMedia || (navigator as any).mozGetUserMedia

      if (!getUserMedia) {
        return Promise.reject(
          new Error('getUserMedia is not implemented in this browser')
        )
      }

      return new Promise((resolve, reject) => {
        getUserMedia.call(navigator, constraints, resolve, reject)
      })
    }
  }
}

// 防抖
// 不管怎么点击，只在500毫秒后触发事件，500毫秒内点击也要等到500毫秒后再触发事件
export const debounce = (fn: Function, delay = 500) => {
  let timer: any = null
  return (...args: any[]) => {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fn(...args)
    }, delay)
  }
}