# Figma MCP 代码生成规则

## 基本规则

- **重要**：如果 Figma Dev Mode MCP 服务器返回 localhost 图片或 SVG 源，直接使用该源
- **重要**：不要导入/添加新的图标包，所有资源都应该在 Figma 数据中
- **重要**：如果提供了 localhost 源，不要使用或创建占位符

## Vue 3 + TypeScript 代码生成规则

### 组件结构
- 使用 Vue 3 Composition API
- 使用 `<script setup lang="ts">` 语法
- 使用 TypeScript 进行类型定义
- 组件名使用 PascalCase

### 样式系统
- 优先使用 Vant 组件库的组件
- 使用 Vant 的主题定制功能
- 从 Figma 变量中提取设计令牌
- 避免硬编码值，使用 Figma 中的设计令牌
- 移动端优先的响应式设计
- 可以结合 Tailwind CSS 进行细节调整

### 文件组织
- UI 组件放在 `src/components/ui/` 目录
- 布局组件放在 `src/components/layout/` 目录
- 业务组件放在 `src/components/business/` 目录
- 避免内联样式，除非确实必要

### 代码质量
- 遵循 WCAG 无障碍要求
- 添加组件文档和注释
- 优先考虑 Figma 保真度以精确匹配设计
- 使用语义化的 HTML 标签

### 命名约定
- 组件文件：PascalCase.vue
- 变量和函数：camelCase
- 常量：UPPER_SNAKE_CASE
- CSS 类：kebab-case（Tailwind）

## 示例代码结构

```vue
<template>
  <div class="page-container">
    <!-- 优先使用 Vant 组件 -->
    <van-nav-bar title="页面标题" left-arrow @click-left="onBack" />

    <div class="content">
      <van-cell-group>
        <van-cell title="单元格" value="内容" />
      </van-cell-group>

      <van-button type="primary" block>
        主要按钮
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showToast } from 'vant'

// 类型定义
interface Props {
  title?: string
}

// Props
const props = withDefaults(defineProps<Props>(), {
  title: '默认标题'
})

// 响应式数据
const loading = ref(false)

// 方法
const onBack = () => {
  // 返回逻辑
}

// 计算属性
const computedValue = computed(() => {
  return props.title.toUpperCase()
})
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.content {
  padding: 16px;
}
</style>
```

## Vant 组件映射建议

- **按钮** → `van-button`
- **输入框** → `van-field`
- **列表项** → `van-cell`
- **卡片** → `van-card` 或自定义 div
- **导航栏** → `van-nav-bar`
- **标签页** → `van-tabs`
- **弹窗** → `van-popup`
- **加载** → `van-loading`

## 最佳实践

1. **组件拆分**：将大型设计拆分为小的、可重用的组件
2. **设计令牌**：从 Figma 变量中提取颜色、间距、字体等
3. **响应式**：确保组件在不同屏幕尺寸下正常工作
4. **可访问性**：添加适当的 ARIA 标签和键盘导航支持
5. **性能**：使用 Vue 3 的性能优化特性（如 v-memo）
