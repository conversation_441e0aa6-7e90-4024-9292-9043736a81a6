import * as <PERSON><PERSON> from "paho-mqtt"
import config from "@/config"
import { showDialog } from "vant"
import { useHomeStore } from "@/stores/home"
import { SetAppInfo, setupMessageHandler } from "@/components/WebRTCClient"

function uint8ArrayToString(uint8Array: any) {
  const decoder = new TextDecoder("utf-8") // 默认就是 'utf-8'
  return decoder.decode(uint8Array)
}

export class MqttService {
  private client: Paho.Client
  // private host = import.meta.env.MODE === 'production'?'p1.seewds.com/ws1':'rd.gdxp.com/ws1' // 替换为你的MQTT代理地址
  private host = "p1.seewds.com/ws1" // 替换为你的MQTT代理地址
  private clientId = config.app_id ? config.app_id : config.user_id
  private useSSL = true // 根据实际情况设置是否使用SSL
  private messageHandler: (message: Paho.Message, payloadString: string) => void //添加赋值方法确保能正常收到消息
  private reconnectTimeout: NodeJS.Timeout | null = null
  private reconnectDelay = 3000

  constructor() {
    const url = this.useSSL ? `wss://${this.host}` : `ws://${this.host}:`
    this.client = new Paho.Client(url, this.clientId)

    this.messageHandler = (message, payloadString) => {}

    // 设置连接丢失回调
    this.client.onConnectionLost = (responseObject: any) => {
      if (responseObject.errorCode !== 0) {
        console.log("onConnectionLost:", responseObject.errorMessage)
        this.reconnect()
      }
    }

    // 设置消息到达回调
    this.client.onMessageArrived = (message: Paho.Message) => {
      // 你可以在这里处理接收到的消息
      if (message.payloadBytes) {
        // 处理二进制数据
        const str = uint8ArrayToString(message.payloadBytes)
        this.messageHandler(message, str)
      } else {
        // 处理文本消息
        console.log("进来字符串解析")
        this.messageHandler(message, message.payloadString || "")
      }
    }
  }

  public connect(): void {
    this.client.connect({
      timeout: 3,
      userName: config.MQTTUsername,
      password: config.MQTTPassword,
      cleanSession: true,
      keepAliveInterval: 30,
      onSuccess: () => {
        console.log("MQTT 连接成功")
        this.subscribe(`v1/${config.user_id}/message`)
        SetAppInfo()
        const homeStore = useHomeStore()
        homeStore.getDeviceList(1)
        setupMessageHandler() // 连接成功后设置处理逻辑
        if (this.reconnectTimeout) {
          clearTimeout(this.reconnectTimeout)
          this.reconnectTimeout = null
        }
      },
      onFailure: (message: { errorMessage: string }) => {
        console.error("连接失败:", message.errorMessage)
        showDialog({ message: `连接MQTT错误:${message.errorMessage}` })
        const homeStore = useHomeStore()
        homeStore.getDeviceList(1)
        this.reconnect()
      },
    })
  }

  private reconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }

    this.reconnectTimeout = setTimeout(() => {
      console.log("Attempting to reconnect to MQTT...")
      this.connect()
    }, this.reconnectDelay)
  }

  public subscribe(
    topic: string,
    onSuccess?: (grant: any) => void,
    onFailure?: (error: any) => void
  ): void {
    const subscriptionOptions: any = {
      qos: 1,
      onSuccess: (grant: any) => {
        console.log(`Subscribed to topic: ${topic}`)
        if (onSuccess) {
          onSuccess(grant)
        }
      },
      onFailure: (error: any) => {
        console.log(`Failed to subscribe to topic: ${topic}, error: ${error}`)
        if (onFailure) {
          onFailure(error)
        }
      },
    }
    this.client.subscribe(topic, subscriptionOptions)
  }

  public publish(topic: string, payload: string): void {
    const message = new Paho.Message(payload)
    message.destinationName = topic
    message.qos = 1
    this.client.send(message)
  }

  public setHandleMessage(handler: (message: Paho.Message, payloadString: string) => void): void {
    this.messageHandler = handler
  }

  public disconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }
    
    if (this.client.isConnected()) {
      try {
        this.client.disconnect()
        console.log("MQTT 连接已断开")
      } catch (error) {
        console.error("断开 MQTT 连接失败:", error)
        showDialog({ message: `断开 MQTT 连接错误:${error}` })
      }
    } else {
      console.log("MQTT 客户端未连接，无需断开")
    }
  }
}
