{"name": "h5-gazing", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "preview": "vite preview", "build": "vite build --mode test", "build:prod": "vite build --mode production", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false"}, "dependencies": {"@vitejs/plugin-vue": "^5.2.1", "airwallex-payment-elements": "^1.62.0", "ali-oss": "^6.21.0", "axios": "^1.6.0", "crypto-js": "^4.2.0", "fastclick": "^1.0.6", "hls.js": "^1.5.17", "js-md5": "^0.8.3", "jsencrypt": "^3.3.2", "moment": "^2.30.1", "paho-mqtt": "^1.1.0", "pinia": "^2.2.6", "postcss-px-to-viewport-8-plugin": "^1.2.5", "qrcode.vue": "^3.4.1", "vant": "^4.9.19", "vconsole": "^3.15.1", "vue": "^3.5.12", "vue-draggable-plus": "^0.6.0", "vue-i18n": "^11.1.5", "vue-qrcode-reader": "^5.5.11", "vue-router": "^4.3.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/jsdom": "^21.1.3", "@types/mqtt": "^2.5.0", "@types/paho-mqtt": "^1.0.10", "@vitejs/plugin-legacy": "^6.0.0", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.4.0", "jsdom": "^22.1.0", "less": "^4.2.0", "npm-run-all2": "^6.1.1", "typescript": "~5.2.0", "vite": "^6.0.0", "vue-tsc": "^1.8.19"}}