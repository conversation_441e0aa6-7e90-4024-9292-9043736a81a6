<template>
  <div class="fullscreen">
    <!-- <p class="error">{{ error }}</p> -->

    <qrcode-stream
      :constraints="selectedConstraints"
      :track="trackFunctionSelected.value"
      :formats="['qr_code']"
      @error="onError"
      @detect="onDetect"
      @camera-on="onCameraReady"
    >
      <div class="mask"></div>
      <img @click="back" class="back-icon" src="../assets/backicon.png" alt="back" />
    </qrcode-stream>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router"
import { ref } from "vue"
import { QrcodeStream } from "vue-qrcode-reader"
import { get_device_share, network_config } from "@/api/index"
import { showSuccessToast, showFailToast } from "vant"
import { useHomeStore } from "@/stores/home"

const router = useRouter()
const homeStore = useHomeStore()

function onDetect(detectedCodes: any[]) {
  let result = detectedCodes.map((code: { rawValue: any }) => code.rawValue)
  fetch(result[0])
}

const fetch = (code: string) => {
  if (code.length > 30) {
    network_config(code).then((res: any) => {
      if (res.code === 0) {
        showSuccessToast("注册成功")
        if (homeStore.state.deviceList.length) {
          homeStore.getDeviceList(2)
        } else {
          homeStore.getDeviceList(1)
        }
        setTimeout(() => {
          router.push("/home")
        }, 5000)
      } else {
        showFailToast(res.msg)
      }
    })
  } else {
    get_device_share(code).then((res: any) => {
      if (res.code === 0) {
        showSuccessToast("分享成功")
        if (homeStore.state.deviceList.length) {
          homeStore.getDeviceList(2)
        } else {
          homeStore.getDeviceList(1)
        }
        setTimeout(() => {
          router.push("/home")
        }, 5000)
      } else {
        showFailToast(res.msg)
      }
    })
  }
}

const selectedConstraints = ref({ facingMode: "environment" })
const defaultConstraintOptions = [
  { label: "rear camera", constraints: { facingMode: "environment" } },
  { label: "front camera", constraints: { facingMode: "user" } },
]
const constraintOptions: any = ref(defaultConstraintOptions)

async function onCameraReady() {
  const devices = await navigator.mediaDevices.enumerateDevices()
  const videoDevices = devices.filter(({ kind }) => kind === "videoinput")

  constraintOptions.value = [
    ...defaultConstraintOptions,
    ...videoDevices.map(({ deviceId, label }) => ({
      label: `${label} (ID: ${deviceId})`,
      constraints: { deviceId },
    })),
  ]

  error.value = ""
}

/*** track functons ***/

function paintOutline(
  detectedCodes: any,
  ctx: {
    strokeStyle: string
    beginPath: () => void
    moveTo: (arg0: any, arg1: any) => void
    lineTo: (arg0: any, arg1: any) => void
    closePath: () => void
    stroke: () => void
  }
) {
  for (const detectedCode of detectedCodes) {
    const [firstPoint, ...otherPoints] = detectedCode.cornerPoints

    ctx.strokeStyle = "red"

    ctx.beginPath()
    ctx.moveTo(firstPoint.x, firstPoint.y)
    for (const { x, y } of otherPoints) {
      ctx.lineTo(x, y)
    }
    ctx.lineTo(firstPoint.x, firstPoint.y)
    ctx.closePath()
    ctx.stroke()
  }
}
function paintBoundingBox(
  detectedCodes: any,
  ctx: {
    lineWidth: number
    strokeStyle: string
    strokeRect: (arg0: any, arg1: any, arg2: any, arg3: any) => void
  }
) {
  for (const detectedCode of detectedCodes) {
    const {
      boundingBox: { x, y, width, height },
    } = detectedCode

    ctx.lineWidth = 2
    ctx.strokeStyle = "#007bff"
    ctx.strokeRect(x, y, width, height)
  }
}
function paintCenterText(
  detectedCodes: any,
  ctx: {
    canvas: { width: number }
    font: string
    textAlign: string
    lineWidth: number
    strokeStyle: string
    strokeText: (arg0: any, arg1: any, arg2: any) => void
    fillStyle: string
    fillText: (arg0: any, arg1: any, arg2: any) => void
  }
) {
  for (const detectedCode of detectedCodes) {
    const { boundingBox, rawValue } = detectedCode

    const centerX = boundingBox.x + boundingBox.width / 2
    const centerY = boundingBox.y + boundingBox.height / 2

    const fontSize = Math.max(12, (50 * boundingBox.width) / ctx.canvas.width)

    ctx.font = `bold ${fontSize}px sans-serif`
    ctx.textAlign = "center"

    ctx.lineWidth = 3
    ctx.strokeStyle = "#35495e"
    ctx.strokeText(detectedCode.rawValue, centerX, centerY)

    ctx.fillStyle = "#5cb984"
    ctx.fillText(rawValue, centerX, centerY)
  }
}
const trackFunctionOptions = [
  { text: "nothing (default)", value: undefined },
  { text: "outline", value: paintOutline },
  { text: "centered text", value: paintCenterText },
  { text: "bounding box", value: paintBoundingBox },
]
const trackFunctionSelected = ref(trackFunctionOptions[1])

/*** barcode formats ***/

// const barcodeFormats: any = ref({
//   aztec: false,
//   code_128: false,
//   code_39: false,
//   code_93: false,
//   codabar: false,
//   databar: false,
//   databar_expanded: false,
//   data_matrix: false,
//   dx_film_edge: false,
//   ean_13: false,
//   ean_8: false,
//   itf: false,
//   maxi_code: false,
//   micro_qr_code: false,
//   pdf417: false,
//   qr_code: true,
//   rm_qr_code: false,
//   upc_a: false,
//   upc_e: false,
//   linear_codes: false,
//   matrix_codes: false,
// });
// const selectedBarcodeFormats: any = computed(() => {
//   return Object.keys(barcodeFormats.value).filter(
//     (format) => barcodeFormats.value[format]
//   );
// });

/*** error handling ***/

const error = ref("")

function onError(err: { name: string; message: string }) {
  error.value = `[${err.name}]: `

  if (err.name === "NotAllowedError") {
    error.value += "you need to grant camera access permission"
  } else if (err.name === "NotFoundError") {
    error.value += "no camera on this device"
  } else if (err.name === "NotSupportedError") {
    error.value += "secure context required (HTTPS, localhost)"
  } else if (err.name === "NotReadableError") {
    error.value += "is the camera already in use?"
  } else if (err.name === "OverconstrainedError") {
    error.value += "installed cameras are not suitable"
  } else if (err.name === "StreamApiNotSupportedError") {
    error.value += "Stream API is not supported in this browser"
  } else if (err.name === "InsecureContextError") {
    error.value +=
      "Camera access is only permitted in secure context. Use HTTPS or localhost rather than HTTP."
  } else {
    error.value += err.message
  }
}
const back = () => router.go(-1)
</script>

<style scoped lang="less">
.error {
  font-weight: bold;
  color: red;
  text-align: center;
}
.barcode-format-checkbox {
  margin-right: 10px;
  white-space: nowrap;
  display: inline-block;
}

.fullscreen {
  position: fixed;
  z-index: 1000;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
}

.back-icon {
  width: 36px;
  height: 36px;
  position: absolute;
  top: 47px;
  left: 24px;
  z-index: 99;
}
// .mask {
//   position: absolute;
//   z-index: 98;
//   width: 100vw;
//   height: 100vh;
//   background-color: rgba(0, 0, 0, 0.5);
//   clip-path: polygon(
//     0px 0px,
//     0px 100%,
//     20% 100%,
//     20% 25%,
//     80% 25%,
//     80% 60%,
//     20% 60%,
//     20% 100%,
//     100% 100%,
//     100% 0px
//   );
// }
</style>
