<template>
  <div class="container">
    <div class="modal-box-invite" v-if="config.showBottom.value === 1">
      <div class="modal-invite-from-title">
        <h1>How to complete Your Review</h1>
        <p>Follow these simple steps to receive your free gift</p>
      </div>
      <div class="modal-invite-item one-item">
        <div class="modal-invite-item-left">1</div>
        <div class="modal-invite-item-right">
          <div class="modal-invite-item-right-title">Log in to your buying platform account</div>
          <div class="modal-invite-item-right-text">
            Visit buying platform and sign in to your account.
          </div>
        </div>
      </div>
      <div class="modal-invite-item">
        <div class="modal-invite-item-left">2</div>
        <div class="modal-invite-item-right">
          <div class="modal-invite-item-right-title">Find our device in your order history</div>
          <div class="modal-invite-item-right-text">
            Go to "Your Orders" and locate the product you purchased from us.
          </div>
        </div>
      </div>
      <div class="modal-invite-active">
        <div class="modal-invite-item">
          <div class="modal-invite-item-left">3</div>
          <div class="modal-invite-item-right">
            <div class="modal-invite-item-right-title">Click "Write a product review"</div>
            <div class="modal-invite-item-right-text">
              Rate our product and share your experience using it. Your honest feedback is greatly
              appreciated!
            </div>
          </div>
        </div>
        <!-- <img
          class="modal-invite-active-img"
          @click="showImage('https://cache.gdxp.com/acce/assets/gift1.png')"
          src="https://cache.gdxp.com/acce/assets/gift1.png"
          alt=""
        />
        <img
          class="modal-invite-active-img"
          @click="showImage('https://cache.gdxp.com/acce/assets/gift2.png')"
          src="https://cache.gdxp.com/acce/assets/gift2.png"
          alt=""
        /> -->
      </div>
      <div class="modal-invite-item">
        <div class="modal-invite-item-left">4</div>
        <div class="modal-invite-item-right">
          <div class="modal-invite-item-right-title">
            Take a screenshot of your published review
          </div>
          <div class="modal-invite-item-right-text">
            After submitting your review, take a screenshot showing your review has been published. The screenshot should include the star rating icons.
          </div>
        </div>
      </div>
      <div class="catek" v-if="useStore.platform === 'android'">
        <input ref="myPhoto" type="file" accept="image/*" @change="photoChage" />
      </div>
      <div class="modal-invite-box-img-data" v-if="config.albumImg.value" @click="clickAlbum">
        <img class="modal-invite-box-img" :src="config.albumImg.value" alt="" />
      </div>
      <div class="modal-invite-box" @click="clickAlbum" v-else>
        Upload your review screenshot
        <div class="modal-invite-box-data">Choose File</div>
      </div>
      <div class="invite-next">
        <div class="invite-next-btn" @click="nextStep">Next Step</div>
      </div>
    </div>
    <div class="modal-invite-data" v-if="config.showBottom.value === 2">
      <div class="modal-invite-from-title">
        <h1>Where Should We Send Your Gift?</h1>
        <p>Enter your shipping details to receive your free gift</p>
      </div>
      <form class="shipping-form" @submit.prevent="handleSubmit">
        <div class="floating-label-group">
          <input
            type="text"
            id="name"
            class="floating-label-input"
            placeholder=" "
            v-model="form.name"
            :class="{ error: errors.name }"
          />
          <label for="name" class="floating-label required">Full Name</label>
          <div class="error-message" v-if="errors.name">{{ errors.name }}</div>
        </div>

        <div class="floating-label-group">
          <input
            type="tel"
            id="phone"
            class="floating-label-input"
            placeholder=" "
            v-model="form.phone"
            :class="{ error: errors.phone }"
          />
          <label for="phone" class="floating-label required">Phone Number</label>
          <div class="error-message" v-if="errors.phone">{{ errors.phone }}</div>
        </div>
        <div class="floating-label-group">
          <input
            type="text"
            id="zipcode"
            class="floating-label-input"
            placeholder=" "
            v-model="form.zipcode"
            :class="{ error: errors.zipcode }"
          />
          <label for="zipcode" class="floating-label required">ZIP Code</label>
          <div class="error-message" v-if="errors.zipcode">{{ errors.zipcode }}</div>
        </div>
        <input type="hidden" id="country" value="US" />

        <div class="form-row">
          <div class="form-col">
            <div class="floating-label-group">
              <input
                type="text"
                id="state"
                class="floating-label-input"
                placeholder=" "
                v-model="form.state"
                :class="{ error: errors.state }"
              />
              <label for="state" class="floating-label required">State</label>
              <div class="error-message" v-if="errors.state">{{ errors.state }}</div>
            </div>
          </div>
          <div class="form-col">
            <div class="floating-label-group">
              <input
                type="text"
                id="city"
                class="floating-label-input"
                placeholder=" "
                v-model="form.city"
                :class="{ error: errors.city }"
              />
              <label for="city" class="floating-label required">City</label>
              <div class="error-message" v-if="errors.city">{{ errors.city }}</div>
            </div>
          </div>
        </div>

        <div class="floating-label-group">
          <input
            type="text"
            id="street"
            class="floating-label-input"
            placeholder=" "
            v-model="form.street"
            :class="{ error: errors.street }"
          />
          <label for="street" class="floating-label required">Street Address</label>
          <div class="error-message" v-if="errors.street">{{ errors.street }}</div>
        </div>
        <button type="submit" class="submit-button">Confirm Address</button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from "vue"
import config from "@/config"
import { showLoadingToast, showToast, showDialog, closeToast, showImagePreview } from "vant"
import { randomNum, base64ToFile, readImg, compressImg } from "@/utils"
import moment from "moment"
import { uploadToOSS } from "@/oss/index"
import { useHomeStore } from "@/stores/home"
import { createWebSocket, sendSock, closeSock } from "@/utils/socket"
import { get_auto_customer_star_info } from "@/api"
import router from "@/router"
import { md5 } from "js-md5"
import type{ formPranms } from "@/api/base"


const useStore = useHomeStore()
const myPhoto = ref<HTMLElement | any>(null)
const question_id = ref(0)
const form = reactive<formPranms>({
  name: "",
  phone: "",
  state: "",
  city: "",
  street: "",
  zipcode: "",
})
const errors = reactive<{
  name?: string
  phone?: string
  state?: string
  city?: string
  street?: string
  zipcode?: string
}>({})

// 打开相册选择照片后的回调
const photoChage = async () => {
  const file = myPhoto.value.files[0]
  changeImage(file)
}

// 转义图片格式跟大小
const changeImage = async (file: any) => {
  const img = await readImg(file) // 读取上传的照片并转为base64
  let image = await compressImg(img, file.type, 1000, 1000) //防止上传的照片过大，进行压缩处理
  if (image) {
    // 开始上传
    upload(image)
  }
}

// 上传图片
async function upload(userFile: object) {
  let file = base64ToFile(
    userFile,
    `${moment().format("YYYY/MM/DD")}/${new Date().getTime()}/${randomNum()}.png`
  )
  // 直接上传到阿里云oss
  const res = await uploadToOSS(
    `${moment().format("YYYY/MM/DD")}/${new Date().getTime()}/${randomNum()}.png`,
    file
  )
  if (res) {
    config.albumImg.value = res
    //判断websocket是否断开
    if (config.websockStatus === 0) {
      closeSock()
      setTimeout(() => {
        createWebSocket(global_callback)
      }, 100)
    }
  }
}

// 调用Ios相册
const clickAlbum = () => {
  if (useStore.platform === "android") {
    setTimeout(() => {
      myPhoto.value?.click()
    }, 50)
  } else {
    config.albumType = 2
    const windows: any = window
    windows.webkit.messageHandlers.click_album.postMessage("相册")
  }
}

//图片预览
// const showImage = (img: any) => {
//   showImagePreview([img])
// }

const nextStep = () => {
  if (!config.albumImg.value) return showToast("Please upload pictures first")
  if (config.websockStatus === 0) {
    closeSock()
    setTimeout(() => {
      createWebSocket(global_callback)
    }, 100)
  } else {
    showLoadingToast({
      message: "Loading...",
      //禁止背景点击
      forbidClick: true,
    })
    const params = JSON.stringify({
      cmd: "ai_img_handle",
      question_id: config.sessionId,
      img: config.albumImg.value,
      msg_id: config.msg_id,
      device_sn: config.device_sn,
    })
    sendSock(params)
  }
}

const validateForm = (): boolean => {
  let isValid = true
  if (!form.name.trim()) {
    errors.name = "Please enter your name"
    isValid = false
  } else {
    delete errors.name
  }
  if (!form.phone.trim()) {
    errors.phone = "Please enter a valid phone number"
    isValid = false
  } else {
    delete errors.phone
  }

  if (!form.state.trim()) {
    errors.state = "Required field"
    isValid = false
  } else {
    delete errors.state
  }

  if (!form.city.trim()) {
    errors.city = "Required field"
    isValid = false
  } else {
    delete errors.city
  }

  if (!form.street.trim()) {
    errors.street = "Please enter your street address"
    isValid = false
  } else {
    delete errors.street
  }

  if (!form.zipcode.trim()) {
    errors.zipcode = "Please enter a valid postal code"
    isValid = false
  } else {
    delete errors.zipcode
  }

  return isValid
}

// 提交评价
const handleSubmit = () => {
  if (validateForm()) {
    if (config.websockStatus === 0) {
      closeSock()
      setTimeout(() => {
        createWebSocket(global_callback)
      }, 100)
    } else {
      showLoadingToast({
        message: "Loading...",
        //禁止背景点击
        forbidClick: true,
      })
      const params = JSON.stringify(
        Object.assign(form, {
          cmd: "star_info_handle",
          img: config.albumImg.value,
          msg_id: config.msg_id,
          device_sn: config.device_sn,
        })
      )
      sendSock(params)
    }
  }
}

// 连接websocket模块
const global_callback = (msg: any) => {
  if (msg) {
    if (msg.cmd === "ai_img_handle") {
      if (msg.resultCode === 0) {
        question_id.value = msg.content.question_id
        closeToast()
        config.showBottom.value = 2
      } else {
        if (config.showBottom.value === 2) {
          config.showBottom.value = 1
        }
        showToast("Please upload screenshots of reviews with star rating")
      }
    } else if (msg.cmd === "star_info_handle") {
      if (msg.resultCode === 0) {
        closeToast()
        showDialog({
          message:
            "Dear customer, thanks a lot. We will send the free gift product soon and send you tracking number here. You should receive it in 2 to 3 weeks.",
        }).then(() => {
          inviteClose()
        })
      } else {
        closeToast()
        showToast("Please upload screenshots of reviews with star rating")
      }
    } else if (msg.resultCode == 0 && msg.cmd === "heartbeat") {
      //正在连接心跳
      config.websockStatus = 1
    }
  }
}

const inviteClose = () => {
  router.push("/inviteDetails")
}

const getAddress = () => {
  get_auto_customer_star_info(
    config.sessionId,
    config.device_sn,
    md5(`${config.sessionId}/${config.device_sn + "EKDB_ni&Hb&Zt&zz^7qn9"}`),
    '0'
  ).then((res: any) => {
    if (res.resultCode === 0) {
      if (res.content.sp_status === -1 || res.content.sp_status === 0) {
        config.showBottom.value = 2
      }
      if (res.content.sp_status === -2) {
        config.showBottom.value = 1
      }
      if (res.content.name) {
        form.name = String(res.content.name)
      }
      if (res.content.phone) {
        form.phone = String(res.content.phone)
      }
      if (res.content.state) {
        form.state = String(res.content.state)
      }
      if (res.content.city) {
        form.city = String(res.content.city)
      }
      if (res.content.street) {
        form.street = String(res.content.street)
      }
      if (res.content.zipcode) {
        form.zipcode = String(res.content.zipcode)
      }
    }
  })
}

onMounted(() => {
  showLoadingToast({
    message: "Loading...",
    //禁止背景点击
    forbidClick: true,
  })
  getAddress()
  setTimeout(() => {
    createWebSocket(global_callback)
  }, 800)
  setTimeout(() => {
    closeToast()
  }, 1500)
})

onUnmounted(() => {
  closeSock()
})
</script>

<style lang="less" scoped>
.container {
  background-color: #fff;
  min-height: 100vh;
}
.modal-box-invite {
  background-color: #fff;
  padding-bottom: 20px;
}
.modal-invite-title {
  font-size: 18px;
  color: #333;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  word-break: normal;
  overflow-wrap: break-word;
}
.modal-invite-item {
  display: flex;
  padding: 0 15px;
  margin-bottom: 15px;
  .modal-invite-item-left {
    width: 25px;
    height: 25px;
    background-color: #8a2be2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    margin-right: 10px;
  }
  .modal-invite-item-right {
    flex: 1;
    .modal-invite-item-right-title {
      font-size: 14px;
      color: #333;
      font-weight: bold;
      margin-bottom: 5px;
    }
    .modal-invite-item-right-text {
      font-size: 13px;
      color: #999;
      line-height: 18px;
    }
  }
}
.one-item {
  padding-top: 15px;
}
.invite-next {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  .invite-next-btn {
    width: 200px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #fff;
    font-weight: bold;
    border-radius: 20px;
    background-color: green;
  }
}
.modal-invite-box {
  margin: 0 15px 20px 15px;
  height: 120px;
  background-color: rgb(241, 248, 255);
  display: flex;
  border-radius: 10px;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-size: 16px;
  color: #1e6fe8;
  font-weight: bold;
  word-break: normal;
  overflow-wrap: break-word;
  .modal-invite-box-data {
    margin-top: 10px;
    width: 120px;
    height: 36px;
    background-color: #1e6fe8;
    font-size: 15px;
    color: #fff;
    font-weight: bold;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.modal-invite-box-img-data {
  width: 200px;
  height: 200px;
  margin: 0 auto;
}
.modal-invite-box-img {
  width: 200px;
  height: 200px;
  object-fit: contain;
}

.modal-invite-active {
  border-left: 3px solid #1e6fe8;
  font-size: 14px;
  color: #333;
  padding: 10px 0 5px 10px;
  line-height: 21px;
  background-color: #99ccff;
  margin-bottom: 10px;
  word-break: normal;
  overflow-wrap: break-word;
  margin-left: 15px;
  margin-right: 15px;
  .invite-span {
    color: #1e6fe8;
  }
}
.modal-invite-active-img {
  width: 96%;
}

.modal-invite-from-title {
  color: #fff;
  word-break: normal;
  overflow-wrap: break-word;
  background: linear-gradient(135deg, #8a2be2, #ff69b4);
  height: 55px;
  padding-top: 15px;
  text-align: center;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  h1 {
    font-size: 20px;
    margin-bottom: 6px;
  }
  p {
    font-size: 14px;
  }
}
.modal-invite-footer-btn {
  padding-top: 20px;
  width: 85%;
  margin: 0 auto;
  padding-bottom: 20px;
}
.catek {
  display: none;
}
.shipping-form {
  padding: 20px 15px;
  .floating-label-group {
    position: relative;
    margin-bottom: 16px;
  }

  .floating-label-input {
    width: 95%;
    height: 35px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s;
    padding-top: 15px;
    padding-left: 15px;
  }

  .floating-label {
    position: absolute;
    top: 17px;
    left: 16px;
    font-size: 16px;
    color: #999;
    pointer-events: none;
    transition: all 0.2s;
  }

  .floating-label-input:focus ~ .floating-label,
  .floating-label-input:not(:placeholder-shown) ~ .floating-label {
    top: 8px;
    left: 16px;
    font-size: 12px;
    color: #8a2be2;
  }

  .floating-label-input:focus {
    border-color: #8a2be2;
    box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.15);
  }
  .error-message {
    color: #d9534f;
    font-size: 12px;
    margin-top: 5px;
  }

  .required::after {
    content: " *";
    color: #d9534f;
  }
  .submit-button {
    display: block;
    background-color: #4caf50;
    color: white;
    font-size: 16px;
    font-weight: bold;
    padding: 14px 0;
    border-radius: 30px;
    text-decoration: none;
    margin: 25px auto 0;
    text-align: center;
    width: 100%;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
  }
  .form-row {
    display: flex;
    gap: 10px;
    .floating-label-input {
      width: 90% !important;
    }
  }
}
</style>
