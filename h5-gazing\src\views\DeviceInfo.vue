<template>
  <div class="pages">
    <Header :title="config.device.nickname" :left-type="1">
      <div class="title-edit" v-if="config.device.is_primary" @click="router.push('/editName')">
        编辑
      </div>
    </Header>
    <div class="box">
      <div class="facility">
        <div class="facility-left">
          <img class="facility-img" :src="config.device.product_img" alt="产品" />
          <div class="right-center-left">
            <img
              class="dc-img"
              v-if="config.device.attributes.battery_level > 100"
              src="../assets/device/cd.png"
              alt=""
            />
            <img
              class="dc-img"
              v-else-if="config.device.attributes.battery_level < 10"
              src="https://cache.gdxp.com/acce/assets/facility/seeing_0.png"
              alt=""
            />
            <img
              class="dc-img"
              v-else-if="config.device.attributes.battery_level > 9 && config.device.attributes.battery_level <= 20"
              src="https://cache.gdxp.com/acce/assets/facility/seeing_15.png"
              alt=""
            />
            <img
              class="dc-img"
              v-else-if="config.device.attributes.battery_level > 20 && config.device.attributes.battery_level <= 30"
              src="https://cache.gdxp.com/acce/assets/facility/seeing_30.png"
              alt=""
            />
            <img
              class="dc-img"
              v-else-if="config.device.attributes.battery_level > 30 && config.device.attributes.battery_level <= 50"
              src="https://cache.gdxp.com/acce/assets/facility/seeing_50.png"
              alt=""
            />
            <img
              class="dc-img"
              v-else-if="config.device.attributes.battery_level > 50 && config.device.attributes.battery_level <= 80"
              src="https://cache.gdxp.com/acce/assets/facility/seeing_80.png"
              alt=""
            />
            <img
              class="dc-img"
              v-else-if="config.device.attributes.battery_level > 80"
              src="https://cache.gdxp.com/acce/assets/facility/seeing_100.png"
              alt=""
            />
            <span class="dd-text">
              {{
                config.device.attributes.battery_level > 100
                  ? config.device.attributes.battery_level - 256
                  : config.device.attributes.battery_level
              }}
            </span>
            <span>%</span>
            <div class="seeing-yuan" v-if="refresh === 2">
              <van-loading color="#1989fa" size="24" />
            </div>
            <img
              v-else
              @click="refreshBattery"
              class="seeing-yuan"
              src="https://cache.gdxp.com/acce/assets/facility/seeing_yuan.png"
              alt=""
            />
          </div>
        </div>
        <div class="facility-right">
          <div class="facility-item" v-if="config.device_config_item.ring === 1">
            <div class="facility-item-title">门铃通知</div>
            <div class="facility-item-data">
              <van-switch
                active-color="#1E6FE8"
                inactive-color="#D4D7D5"
                v-model="doorbell"
                :loading="doorbellLoading"
                @change="doorbellChange"
              />
            </div>
          </div>
          <div class="facility-item" v-if="config.device_config_item.pir > 0">
            <div class="facility-item-title">移动通知</div>
            <div class="facility-item-data">
              <van-switch
                active-color="#1E6FE8"
                inactive-color="#D4D7D5"
                v-model="moveCheck"
                :loading="moveLoading"
                @change="moveCheckChange"
              />
            </div>
          </div>
          <div
            class="facility-item"
            v-if="config.device.is_primary && config.device_config_item.pir > 0"
          >
            <div class="facility-item-title">移动侦测</div>
            <div class="facility-item-data">
              <van-switch
                active-color="#1E6FE8"
                inactive-color="#D4D7D5"
                v-model="moveHuman"
                @change="fooParams"
              />
            </div>
          </div>
          <!-- <div class="facility-item" v-if="config.device_config_item.nn_label_enabled === 1">
            <div class="facility-item-title">人形标记</div>
            <div class="facility-item-data">
              <van-switch
                active-color="#1BA674"
                inactive-color="#D4D7D5"
                v-model="personMarker"
                @change="personMarkerChage"
              />
            </div>
          </div> -->
        </div>
      </div>
      <div class="tool-list">
        <router-link
          class="tool-item"
          :to="{ path: '/deviceSet', query: { id: 1 } }"
          draggable="false"
          @dragstart.prevent
        >
          <img class="tool-img" src="../assets/device/1.png" alt="" />
          <div class="tool-item-text">基本信息</div>
        </router-link>
        <div class="tool-item" @click="router.push('/shareDevice')" v-if="config.device.is_primary">
          <img class="tool-img" src="../assets/device/2.png" alt="" />
          <div class="tool-item-text">分享设备</div>
        </div>
        <div class="tool-item" @click="skipPages">
          <img class="tool-img" src="../assets/device/8.png" alt="" />
          <div class="tool-item-text">历史事件</div>
        </div>
        <router-link class="tool-item" to="/cloudStorage" draggable="false" @dragstart.prevent>
          <img class="tool-img" src="../assets/device/7.png" alt="" />
          <div class="tool-item-text">云存套餐</div>
        </router-link>
        <div class="tool-item" @click="detectAreaFn">
          <img class="tool-img" src="../assets/device/5.png" alt="" />
          <div class="tool-item-text">侦测区域</div>
        </div>
        <router-link class="tool-item" to="/volumeSetting" v-if="config.device.doorbell_diagram">
          <img class="tool-img" src="../assets/device/3.png" alt="" />
          <div class="tool-item-text">门铃音量</div>
        </router-link>
        <!-- <div class="tool-item" @click="humanDetectionFn" v-if="config.device_config_item.pir">
          <img class="tool-img" src="../assets/device/4.png" alt="" />
          <div class="tool-item-text">人形侦测</div>
        </div> -->
        <div
          class="tool-item"
          @click="navigatorLamplight"
          v-if="config.device_config_item.mobile_trigger_light_enabled"
        >
          <img class="tool-img" src="../assets/device/6.png" alt="" />
          <div class="tool-item-text">灯光感应</div>
        </div>
        <div class="tool-item" @click="verify(7)" v-if="config.device_config_item.is_4g_card">
          <img class="tool-img" src="../assets/device/7.png" alt="" />
          <div class="tool-item-text">充值流量</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import config from "@/config"
import Header from "@/components/Header.vue"
import { ref, onMounted } from "vue"
import { useRouter, useRoute } from "vue-router"
import { showToast } from "vant"
import { setProperty, getProperty } from "@/components/WebRTCClient"
import { set_device } from "@/api"
import { detectArea, detectAreaParams } from "@/utils"

const router = useRouter()
const route = useRoute()

const moveCheck = ref(true) //移动通知
const moveLoading = ref(false)
const doorbell = ref(true) //门铃通知
const doorbellLoading = ref(false)
const moveHuman = ref(true) //移动侦测
const personMarker = ref(false) //人形侦测
const refresh = ref<number>(1)
const status = ref<number>(0) // 0:离线 1:在线 2:待机

const refreshBattery = () => {
  refresh.value = 2
  getProperty([{ category: "battery", value: "" }])
  setTimeout(() => {
    refresh.value = 1
  }, 5000)
}

const checkDeviceOffline = (
  statusVal: number,
  toggleRef?: { value: boolean },
  callback?: () => void
) => {
  if (statusVal === 0) {
    if (toggleRef) {
      toggleRef.value = !toggleRef.value
    }
    showToast("设备已离线，请稍后再试")
    return true
  }
  if (callback) {
    callback()
  }
  return false
}

const doorbellChange = () => {
  checkDeviceOffline(status.value, doorbell, async () => {
    try {
      doorbellLoading.value = true
      await setDeviceStatus()
    } catch (error) {
      doorbell.value = !doorbell.value
      showToast("设置失败，请重试")
    } finally {
      doorbellLoading.value = false
    }
  })
}

const moveCheckChange = () => {
  checkDeviceOffline(status.value, moveCheck, async () => {
    try {
      moveLoading.value = true
      await setDeviceStatus()
    } catch (error) {
      moveCheck.value = !moveCheck.value
      showToast("设置失败，请重试")
    } finally {
      moveLoading.value = false
    }
  })
}

const setDeviceStatus = () => {
  set_device(config.device.device_sn, config.device.function_bitmap ^ (1 << 0)).then((res: any) => {
    if (res.code === 0) {
      config.device.function_bitmap = config.device.function_bitmap ^ (1 << 0)
      moveLoading.value = false
      doorbellLoading.value = false
    }
  })
}

const detectAreaFn = () => {
  checkDeviceOffline(status.value, undefined, () => {
    detectArea()
  })
}

// const humanDetectionFn = () => {
//   checkDeviceOffline(status.value, undefined, () => {
//     router.push("/humanDetection")
//   })
// }

const navigatorLamplight = () => {
  checkDeviceOffline(status.value, undefined, () => {
    getProperty([{ category: "light_leve", value: "" }])
    setTimeout(() => {
      router.push("/lamplight")
    }, 500)
  })
}

// 移动侦测
const fooParams = (value: boolean) => {
  checkDeviceOffline(status.value, moveHuman, () => {
    setProperty([
      { category: "PIR", value: value ? 2 : 0 },
      { category: "human_detection_state", value: value ? 1 : 0 },
    ])
  })
}

// 人形标记
// const personMarkerChage = (value: boolean) => {
//   checkDeviceOffline(status.value, personMarker, () => {
//     setProperty([{ category: "person_marker", value: value ? 1 : 0 }])
//   })
// }

const init = () => {
  //门铃通知开关 移动通知开关
  if (config.device.function_bitmap & (1 << 0)) {
    moveCheck.value = true
    doorbell.value = true
  } else {
    moveCheck.value = false
    doorbell.value = false
  }
  moveHuman.value = config.device.attributes.PIR ? true : false
  personMarker.value = config.device.attributes.person_marker ? true : false
}

const skipPages = () => {
  router.push("/playback")
}

const verify = (type: number) => {
  if (type === 7) return router.push("/applePay")
  if (type) {
    router.push({ path: "/deviceSet", query: { id: type } })
  } else {
    // click_wifi()
  }
}

onMounted(() => {
  init()
  window.detectAreaParams = detectAreaParams
  status.value = Number(route.query.device_status) || 0
})
</script>

<style lang="less">
.title-edit {
  font-size: 15px;
  color: #1e6fe8;
}
.box {
  padding: 16px;
  .facility {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    .facility-left {
      width: 40%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .facility-img {
        max-width: 100%;
        height: 151px;
        margin-bottom: 15px;
      }
      .right-center-left {
        font-size: 12px;
        color: #333;
        font-weight: 400;
        display: flex;
        align-items: center;
        .dd-text {
          font-size: 15px;
          font-weight: 700;
        }
        .dc-img {
          width: 31px;
          height: 14px;
          margin-right: 5px;
        }
        .seeing-yuan {
          width: 24px;
          height: 24px;
          margin-left: 5px;
        }
      }
    }
    .facility-right {
      width: 45%;
      display: flex;
      flex-wrap: wrap;
      flex-direction: column;
      justify-content: center;
      .facility-item {
        padding: 0 10px;
        height: 53px;
        border-radius: 8px;
        background-color: #fff;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        &:last-child {
          margin-bottom: 0;
        }
        .facility-item-title {
          font-size: 12px;
          color: #333;
        }
        .facility-item-data {
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }
  .tool-list {
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    flex-wrap: wrap;
    padding-top: 20px;
    .tool-item {
      width: 33%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin-bottom: 20px;
      .tool-img {
        width: 64px;
        height: 64px;
        margin-bottom: 8px;
      }
      .tool-item-text {
        font-size: 12px;
        color: #333;
      }
    }
  }
}
</style>
