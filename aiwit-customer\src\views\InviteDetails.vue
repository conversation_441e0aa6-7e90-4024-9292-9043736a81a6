<template>
  <div class="pages">
    <div class="order-card">
      <div class="header">
        <h1>Gift Order Tracking</h1>
      </div>
      <div class="content-box">
        <div class="thank-you">
          <h2>Thank You for Participating</h2>
        </div>

        <div class="progress-tracker">
          <div class="progress-steps">
            <div class="progress-line"></div>
            <div class="progress-line-active" :style="{ width: widthNum + '%' }"></div>
            <div class="step">
              <div class="step-circle active">
                <van-icon name="success" color="#fff" />
              </div>
              <div class="step-label active">Submitted</div>
            </div>

            <div class="step">
              <div class="step-circle" :class="state.mdl.order_status > 0 ? 'active' : ''">
                <van-icon name="success" color="#fff" />
              </div>
              <div class="step-label" :class="state.mdl.order_status > 0 ? 'active' : ''">
                Under Review
              </div>
            </div>

            <div class="step">
              <div class="step-circle" :class="state.mdl.order_status > 1 ? 'active' : ''">
                <van-icon name="success" color="#fff" v-if="state.mdl.order_status > 1" />
                <van-icon name="logistics" color="#fff" v-else />
              </div>
              <div class="step-label" :class="state.mdl.order_status > 1 ? 'active' : ''">
                Preparing
              </div>
            </div>

            <div class="step">
              <div class="step-circle" :class="state.mdl.order_status > 2 ? 'active' : ''">
                <van-icon name="success" color="#fff" v-if="state.mdl.order_status > 2" />
                <van-icon name="logistics" color="#fff" v-else />
              </div>
              <div class="step-label" :class="state.mdl.order_status > 2 ? 'active' : ''">
                Shipped
              </div>
            </div>

            <div class="step">
              <div class="step-circle" :class="state.mdl.order_status > 3 ? 'active' : ''">
                <van-icon name="success" color="#fff" v-if="state.mdl.order_status > 3" />
                <van-icon name="certificate" color="#fff" v-else />
              </div>
              <div class="step-label" :class="state.mdl.order_status > 3 ? 'active' : ''">
                Delivered
              </div>
            </div>
          </div>
        </div>

        <div class="order-status">
          <div class="status-title">
            <van-icon name="success" color="#000" v-if="state.mdl.order_status === 4" />
            <van-icon name="underway-o" color="#000" v-else />
            Current Status:
            {{
              state.mdl.order_status === 0
                ? "Submitted"
                : state.mdl.order_status === 1
                ? "Under Review"
                : state.mdl.order_status === 2
                ? "Preparing"
                : state.mdl.order_status === 3
                ? "Shipped"
                : state.mdl.order_status === 4
                ? "Delivered"
                : ""
            }}
          </div>
          <div class="status-time">Updated: {{ state.mdl.last_time }}</div>
        </div>

        <!-- 审核失败的情况（默认隐藏） -->
        <div class="failure-reason" v-if="state.mdl.sp_status < 0 && state.mdl.error_text">
          <div class="failure-title">
            <i class="icon icon-title">error_outline</i>
            Review Failure Reason
          </div>
          <div class="status-message">{{ state.mdl.error_text }}</div>
        </div>

        <!-- 已发货的情况（默认隐藏） -->
        <div class="shipping-info" v-if="state.mdl.order_status > 1">
          <div class="shipping-title">
            <i class="icon">local_shipping</i>
            Shipping Information
          </div>
          <div class="shipping-details">
            <p>
              <span class="label">Recipient:</span>
              Mr. {{ hideName(state.mdl.name) }}
            </p>
            <p>
              <span class="label">Phone:</span>
              {{ hidePhoneNumber(state.mdl.phone) }}
            </p>
            <p>
              <span class="label">Address:</span>
              {{ state.mdl.city }} {{ state.mdl.street }},{{ state.mdl.zipcode }}
            </p>
            <p>
              <span class="label" v-if="state.mdl.order_status > 2">Tracking:</span>
              {{ state.mdl.ship_no }}
              <!-- <div class="box-copy" @click="copyCode(state.mdl.ship_no)" v-if="state.mdl.ship_no">Copy</div> -->
            </p>
          </div>
        </div>
        <van-button
          class="home-btn"
          type="primary"
          round
          color="#ff7043"
          size="large"
          @click="clickHadte"
        >
          {{
            state.mdl.order_status === 0
              ? "Go to edit"
              : state.mdl.order_status === 1 ||
                state.mdl.order_status === 2 ||
                state.mdl.order_status === 3
              ? "Go home"
              : state.mdl.order_status === 4
              ? "completed"
              : ""
          }}
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue"
import { get_auto_customer_star_info } from "@/api"
import { md5 } from "js-md5"
import config from "@/config"
import type { formData } from "@/api/base"
import router from "@/router"
import { showLoading } from "@/utils"

const widthNum = ref(20)
const state = reactive({
  mdl: {} as formData,
})

const clickHadte = () => {
  if (state.mdl.sp_status === -1 || state.mdl.sp_status === 0) {
    config.showBottom.value = 2
    showLoading()
    router.push("/invite")
  } else if (state.mdl.sp_status === -2) {
    config.showBottom.value = 1
    showLoading()
    router.push("/invite")
  } else if (state.mdl.order_status > 0) {
    router.push("/")
  }
}

const hideName = (name: string) => {
  return name.slice(0, 8) + "****"
}

const hidePhoneNumber = (phone: string) => {
  return phone.slice(0, 3) + "****" + phone.slice(-4)
}

const getAddress = () => {
  get_auto_customer_star_info(
    config.sessionId,
    config.device_sn,
    md5(`${config.sessionId}/${config.device_sn + "EKDB_ni&Hb&Zt&zz^7qn9"}`),
    '0'
  ).then((res: any) => {
    if (res.resultCode === 0) {
      state.mdl = res.content
      if (state.mdl.order_status === 1) {
        widthNum.value = 40
      } else if (state.mdl.order_status === 2) {
        widthNum.value = 60
      } else if (state.mdl.order_status === 3 || state.mdl.order_status === 4) {
        widthNum.value = 80
      }
    }
  })
}

onMounted(() => {
  getAddress()
})
</script>

<style lang="less" scoped>
.pages {
  background-color: #f8f8f8;
  min-height: 100vh;
  .order-card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    .header {
      background: linear-gradient(135deg, #ff7043, #ffab91);
      color: white;
      display: flex;
      height: 60px;
      align-items: center;
      justify-content: center;
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;
      h1 {
        font-size: 22px;
        font-weight: bold;
      }
    }
    .content-box {
      padding: 20px 16px;
      .thank-you {
        text-align: center;
        margin-bottom: 24px;
        color: #333;
        h2 {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 5px;
        }
      }
      .progress-tracker {
        margin-bottom: 24px;
      }

      .progress-steps {
        display: flex;
        justify-content: space-between;
        position: relative;
        margin-bottom: 16px;
      }

      .progress-line {
        position: absolute;
        top: 12px;
        left: 10%;
        right: 10%;
        height: 2px;
        background-color: #ddd;
        z-index: 1;
      }

      .progress-line-active {
        position: absolute;
        top: 12px;
        left: 10%;
        width: 20%; /* Will be adjusted by JS based on current status */
        height: 2px;
        background-color: #ff7043;
        z-index: 2;
        transition: width 0.5s ease;
      }

      .step {
        width: 20%;
        position: relative;
        z-index: 3;
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .step-circle {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #ddd;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 8px;
        transition: background-color 0.3s;
      }

      .step-circle.active {
        background-color: #ff7043;
        box-shadow: 0 0 0 4px rgba(255, 112, 67, 0.2);
      }

      .step-circle i {
        color: white;
        font-size: 12px;
      }

      .step-label {
        font-size: 12px;
        text-align: center;
        color: #888;
        transition: color 0.3s;
      }

      .step-label.active {
        color: #333;
        font-weight: bold;
      }
      .order-status {
        background-color: #f5f7fa;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
      }

      .status-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
      }

      .status-title i {
        margin-right: 8px;
      }

      .status-time {
        font-size: 12px;
        color: #888;
      }

      .status-message {
        font-size: 14px;
        line-height: 1.5;
      }
      .failure-reason {
        background-color: #fff2f0;
        border-left: 4px solid #ff4d4f;
        padding: 12px 16px;
        margin-bottom: 20px;
        border-radius: 0 4px 4px 0;
      }

      .failure-title {
        font-size: 14px;
        font-weight: bold;
        color: #f5222d;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        .icon-title {
          margin-right: 5px;
        }
      }
      .shipping-info {
        background-color: #fff3e0;
        border-left: 4px solid #ff7043;
        padding: 12px 16px;
        margin-bottom: 16px;
        border-radius: 0 4px 4px 0;
      }

      .shipping-title {
        font-size: 14px;
        font-weight: bold;
        color: #ff7043;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
      }

      .shipping-title i {
        margin-right: 8px;
      }

      .shipping-details {
        font-size: 14px;
        line-height: 1.5;
      }

      .shipping-details p {
        margin-bottom: 8px;
        position: relative;
        .box-copy {
          position: absolute;
          right: 0;
          top: 0;
          background-color: #fff;
          width: 50px;
          height: 25px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          color: #000;
          border-radius: 28px;
        }
      }

      .shipping-details .label {
        color: #888;
        display: inline-block;
        width: 70px;
      }
    }
  }
  .home-btn {
    font-weight: bold;
  }
}
</style>
