<template>
  <div class="pages">
    <Header />
    <div class="conariner">
      <div class="countdown">
        <div class="time-box">{{ countdownTime }}</div>
      </div>
      <div class="code-data">
        <qrcode-vue v-if="qrData" :value="qrData" :size="305" level="L"></qrcode-vue>
        <!-- <img
          v-if="qrData"
          class="logo-image"
          src="../assets/logo.png"
          alt=""
        /> -->
      </div>
      <div class="code-tip">
        请将二维码正对摄像机镜头，并保持约4英寸（10厘米）距离，直至设备发出“二维码扫描成功”语音提示。
      </div>
      <div class="code-imgs">
        <img class="code-link" src="../assets/scan.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import { showConfirmDialog } from "vant"
import { ref, onMounted } from "vue"
import { useRouter, useRoute } from "vue-router"
import QrcodeVue from "qrcode.vue"
import { get_qr_key } from "@/api/index"

const router = useRouter()
const route = useRoute()

const showCenter = ref(false)
const countdownTime = ref(120)
const timer: any = ref(null)
const countdown = () => {
  timer.value = setInterval(() => {
    if (countdownTime.value === 0) {
      clearInterval(timer.value)
      if (route.name === "scanCode") {
        showCenter.value = true
        showConfirmDialog({
          title: "注册设备超时",
          confirmButtonText: "继续等待",
          cancelButtonText: "退出",
        })
          .then(() => {
            countdownTime.value = 120
            timer.value = null
            countdown()
          })
          .catch(() => {
            router.push("/")
          })
      }
    } else {
      countdownTime.value--
    }
  }, 1000)
}

const qrData = ref("") // 要编码为二维码的数据
const fetch = () => {
  get_qr_key().then((res: any) => {
    if (res.data) {
      qrData.value = `${import.meta.env.VITE_APP_BASE_API}\n${localStorage.getItem(
        "wifiName"
      )}\n${localStorage.getItem("wifiPass")}\n${res.data.qr_key}\n${"en"}\n${8}\n`
      countdown()
    }
  })
}

onMounted(() => {
  fetch()
})
</script>

<style lang="less" scoped>
.conariner {
  padding: 20px;
  background-color: #fff;
  .countdown {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    .time-box {
      font-size: 26px;
      color: #333333;
      font-weight: bold;
    }
  }
  .code-data {
    height: 305px;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    position: relative;
    .logo-image {
      position: absolute;
      left: 50%;
      top: 50%;
      width: 46px;
      height: 46px;
      transform: translate(-50%, -50%);
    }
  }
  .code-tip {
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    margin: 0 auto;
    width: 280px;
  }
  .code-imgs {
    padding-top: 10px;
    display: flex;
    justify-content: center;
    height: 138px;
    .code-link {
      width: 274px;
      height: 138px;
    }
  }
}
</style>
