import { createApp } from "vue"
import { createPinia } from "pinia"
import App from "./App.vue"
import router from "./router"
// import VConsole from "vconsole"

//样式初始化
import "reset-css"

import { i18n, vantLocales } from "./lang"
//对vant组件进行初始化语言设置
vantLocales(i18n.global.locale.value)

// 1. 引入你需要的组件
import {
  Button,
  Popup,
  Rate,
  CountDown,
  Icon,
  Form,
  Field,
  CellGroup,
  DropdownMenu,
  DropdownItem,
  Picker,
} from "vant"
// 2. 引入组件样式
import "vant/lib/index.css"

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Icon)
app.use(Button)
app.use(Popup)
app.use(Rate)
app.use(CountDown)
app.use(Form)
app.use(Field)
app.use(CellGroup)
app.use(DropdownMenu)
app.use(DropdownItem)
app.use(Picker)
app.use(i18n)

// new VConsole()
app.mount("#app")
