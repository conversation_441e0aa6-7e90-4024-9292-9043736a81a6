<template>
  <div class="container">
    <div class="modal-invite-data">
      <div class="modal-invite-from-title">
        <h1>Where Should We Send Your Replacement?</h1>
        <p>Enter your shipping details to receive your free device</p>
      </div>
      <form class="shipping-form" @submit.prevent="handleSubmit">
        <div class="floating-label-group">
          <input
            type="text"
            id="name"
            class="floating-label-input"
            placeholder=" "
            v-model="form.name"
            :class="{ error: errors.name }"
          />
          <label for="name" class="floating-label required">Full Name</label>
          <div class="error-message" v-if="errors.name">{{ errors.name }}</div>
        </div>

        <div class="floating-label-group">
          <input
            type="tel"
            id="phone"
            class="floating-label-input"
            placeholder=" "
            v-model="form.phone"
            :class="{ error: errors.phone }"
          />
          <label for="phone" class="floating-label required">Phone Number</label>
          <div class="error-message" v-if="errors.phone">{{ errors.phone }}</div>
        </div>
        <div class="floating-label-group">
          <input
            type="text"
            id="zipcode"
            class="floating-label-input"
            placeholder=" "
            v-model="form.zipcode"
            :class="{ error: errors.zipcode }"
          />
          <label for="zipcode" class="floating-label required">ZIP Code</label>
          <div class="error-message" v-if="errors.zipcode">{{ errors.zipcode }}</div>
        </div>
        <input type="hidden" id="country" value="US" />

        <div class="form-row">
          <div class="form-col">
            <div class="floating-label-group">
              <input
                type="text"
                id="state"
                class="floating-label-input"
                placeholder=" "
                v-model="form.state"
                :class="{ error: errors.state }"
              />
              <label for="state" class="floating-label required">State</label>
              <div class="error-message" v-if="errors.state">{{ errors.state }}</div>
            </div>
          </div>
          <div class="form-col">
            <div class="floating-label-group">
              <input
                type="text"
                id="city"
                class="floating-label-input"
                placeholder=" "
                v-model="form.city"
                :class="{ error: errors.city }"
              />
              <label for="city" class="floating-label required">City</label>
              <div class="error-message" v-if="errors.city">{{ errors.city }}</div>
            </div>
          </div>
        </div>

        <div class="floating-label-group">
          <input
            type="text"
            id="street"
            class="floating-label-input"
            placeholder=" "
            v-model="form.street"
            :class="{ error: errors.street }"
          />
          <label for="street" class="floating-label required">Street Address</label>
          <div class="error-message" v-if="errors.street">{{ errors.street }}</div>
        </div>
        <button type="submit" class="submit-button">Confirm Address</button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from "vue"
import config from "@/config"
import { showLoadingToast, showToast, showDialog, closeToast } from "vant"
import { createWebSocket, sendSock, closeSock } from "@/utils/socket"
import { get_auto_customer_star_info } from "@/api"
import router from "@/router"
import { md5 } from "js-md5"
import type { formPranms } from "@/api/base"

const question_id = ref(0)
const form = reactive<formPranms>({
  name: "",
  phone: "",
  state: "",
  city: "",
  street: "",
  zipcode: "",
})
const errors = reactive<{
  name?: string
  phone?: string
  state?: string
  city?: string
  street?: string
  zipcode?: string
}>({})

const validateForm = (): boolean => {
  let isValid = true
  if (!form.name.trim()) {
    errors.name = "Please enter your name"
    isValid = false
  } else {
    delete errors.name
  }
  if (!form.phone.trim()) {
    errors.phone = "Please enter a valid phone number"
    isValid = false
  } else {
    delete errors.phone
  }

  if (!form.state.trim()) {
    errors.state = "Required field"
    isValid = false
  } else {
    delete errors.state
  }

  if (!form.city.trim()) {
    errors.city = "Required field"
    isValid = false
  } else {
    delete errors.city
  }

  if (!form.street.trim()) {
    errors.street = "Please enter your street address"
    isValid = false
  } else {
    delete errors.street
  }

  if (!form.zipcode.trim()) {
    errors.zipcode = "Please enter a valid postal code"
    isValid = false
  } else {
    delete errors.zipcode
  }

  return isValid
}

// 提交评价
const handleSubmit = () => {
  if (validateForm()) {
    if (config.websockStatus === 0) {
      closeSock()
      setTimeout(() => {
        createWebSocket(global_callback)
      }, 100)
    } else {
      showLoadingToast({
        message: "Loading...",
        //禁止背景点击
        forbidClick: true,
      })
      const params = JSON.stringify(
        Object.assign(form, {
          cmd: "repair_info_handle",
          msg_id: config.msg_id,
          device_sn: config.device_sn,
        })
      )
      sendSock(params)
    }
  }
}

// 连接websocket模块
const global_callback = (msg: any) => {
  if (msg) {
    if (msg.cmd === "repair_info_handle") {
      if (msg.resultCode === 0) {
        closeToast()
        showDialog({
          message:
            "Thank you! Your address has been received. Your free replacement device will ship soon, and we'll send you tracking information when it's on the way. Expected delivery: 2-3 weeks.",
        }).then(() => {
          inviteClose()
        })
      } else {
        closeToast()
        showToast(msg.msg)
      }
    } else if (msg.resultCode == 0 && msg.cmd === "heartbeat") {
      //正在连接心跳
      config.websockStatus = 1
    }
  }
}

const inviteClose = () => {
  router.push("/changeDeviceDetails")
}

const getAddress = () => {
  get_auto_customer_star_info(
    config.sessionId,
    config.device_sn,
    md5(`${config.sessionId}/${config.device_sn + "EKDB_ni&Hb&Zt&zz^7qn9"}`),
    '1'
  ).then((res: any) => {
    if (res.resultCode === 0) {
      if (res.content.sp_status === -1 || res.content.sp_status === 0) {
        config.showBottom.value = 2
      }
      if (res.content.sp_status === -2) {
        config.showBottom.value = 1
      }
      if (res.content.name) {
        form.name = String(res.content.name)
      }
      if (res.content.phone) {
        form.phone = String(res.content.phone)
      }
      if (res.content.state) {
        form.state = String(res.content.state)
      }
      if (res.content.city) {
        form.city = String(res.content.city)
      }
      if (res.content.street) {
        form.street = String(res.content.street)
      }
      if (res.content.zipcode) {
        form.zipcode = String(res.content.zipcode)
      }
    }
  })
}

onMounted(() => {
  showLoadingToast({
    message: "Loading...",
    //禁止背景点击
    forbidClick: true,
  })
  getAddress()
  setTimeout(() => {
    createWebSocket(global_callback)
  }, 800)
  setTimeout(() => {
    closeToast()
  }, 1500)
})

onUnmounted(() => {
  closeSock()
})
</script>

<style lang="less" scoped>
.container {
  background-color: #fff;
  min-height: 100vh;
}
.modal-invite-from-title {
  color: #fff;
  word-break: normal;
  overflow-wrap: break-word;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  padding: 15px 0;
  text-align: center;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  h1 {
    font-size: 20px;
    margin-bottom: 6px;
  }
  p {
    font-size: 14px;
  }
}
.shipping-form {
  padding: 20px 15px;
  .floating-label-group {
    position: relative;
    margin-bottom: 16px;
  }

  .floating-label-input {
    width: 95%;
    height: 35px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s;
    padding-top: 15px;
    padding-left: 15px;
  }

  .floating-label {
    position: absolute;
    top: 17px;
    left: 16px;
    font-size: 16px;
    color: #999;
    pointer-events: none;
    transition: all 0.2s;
  }

  .floating-label-input:focus ~ .floating-label,
  .floating-label-input:not(:placeholder-shown) ~ .floating-label {
    top: 8px;
    left: 16px;
    font-size: 12px;
    color: #8a2be2;
  }

  .floating-label-input:focus {
    border-color: #8a2be2;
    box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.15);
  }
  .error-message {
    color: #d9534f;
    font-size: 12px;
    margin-top: 5px;
  }

  .required::after {
    content: " *";
    color: #d9534f;
  }
  .submit-button {
    display: block;
    background-color: #4caf50;
    color: white;
    font-size: 16px;
    font-weight: bold;
    padding: 14px 0;
    border-radius: 30px;
    text-decoration: none;
    margin: 25px auto 0;
    text-align: center;
    width: 100%;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
  }
  .form-row {
    display: flex;
    gap: 10px;
    .floating-label-input {
      width: 90% !important;
    }
  }
}
</style>
