<template>
  <div class="content">
    <div class="flip-container">
      <div class="flip-box" v-for="(unit, unitIndex) in timeArr" :key="unitIndex">
        <div v-if="unitIndex === 2 || unitIndex === 4" class="flip-data">:</div>
        <div class="flip-items">
          <div
            class="item"
            v-for="(item, index) in unit.max + 1"
            :key="index"
            :class="{
              current: unit.current === index,
              past: unit.current + 1 === index || (index === unit.max && unit.current === 0),
            }"
          >
            <div class="up">
              <div class="inner">{{ index }}</div>
              <div class="shadow"></div>
            </div>
            <div class="down">
              <div class="inner">{{ index }}</div>
              <div class="shadow"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue"

const porps = defineProps({
  deadline: {
    type: Number,
    default: 0,
  },
})

const countdown = ref<number>(porps.deadline)
const timeStr = ref<any>(twoTimeInterval(countdown.value))
const timeRunner = ref<any>(null)

const timeArr = computed(() => {
  return [...timeStr.value].map((unit, index) => {
    let max: any
    if (index && 1 === 1) {
      //时分秒的个位
      max = 9
    } else if (index == 0) {
      //天十位
      max = 9
    } else if (index == 2) {
      //时十位
      max = 5
    } else if (index == 4) {
      //分十位
      max = 5
    } else if (index == 6) {
      //秒十位
      max = 5
    }
    return {
      max,
      current: Number(unit),
    }
  })
})

const setTimeRunner = () => {
  timeRunner.value = setInterval(() => {
    if (countdown.value === 0) {
      clearInterval(timeRunner.value)
      return
    }
    countdown.value--
    timeStr.value = twoTimeInterval(countdown.value)
  }, 1000)
}

function twoTimeInterval(time: number) {
  // 计算出小时数
  let str = time % 3600
  let hours = Math.floor(time / 3600).toString()
  // 计算相差分钟数
  let minutes = Math.floor(str / 60).toString()
  // 计算相差秒数
  let seconds = Math.round(str % 60).toString()
  let returnVal =
    (Number(hours) < 10 ? "0" + hours : hours) +
    (Number(minutes) < 10 ? "0" + minutes : minutes) +
    (Number(seconds) < 10 ? "0" + seconds : seconds)
  if (isNaN(Number(returnVal))) {
    returnVal = "000000"
  } else {
    if (Number(returnVal) > 99235959) {
      returnVal = "992359"
    }
  }
  return returnVal
}

onMounted(() => {
  setTimeRunner()
})
</script>

<style lang="less" scoped>
@width: 23px;
@backgroundColor: #222;
@color: #fff;
@time: 1s;
@height: @width * 1.5;
@fontSize: @width * 1.3;
@lineWidth: (@width / 60);
@radius: 6px;
@perspective: @width * 5;
@gap: @width * 0.2;

.flip-container {
  display: flex;
  justify-content: center;
  position: relative;
  .flip-box{
    display: flex;
    position: relative;
  }
  .flip-data{
    width: 15px;
    height: @height;
    font-size: 35px;
  }
  .flip-items {
    position: relative;
    width: @width;
    height: @height;
    font-size: @fontSize;
    font-weight: bold;
    border-radius: @radius;
    margin-right: @gap;
    .item {
      z-index: 1;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      perspective: @perspective;
      &:before {
        content: "";
        position: absolute;
        top: ((@height - @lineWidth) / 2);
        left: 0;
        z-index: 9;
        width: 100%;
        height: @lineWidth;
        min-height: 2px;
        background-color: rgba(0, 0, 0, 0.6);
      }

      .up,
      .down {
        position: absolute;
        left: 0;
        right: 0;
        height: 50%;
        overflow: hidden;
      }
      .up {
        transform-origin: 50% 100%;
        top: 0;
      }
      .down {
        transform-origin: 50% 0%;
        bottom: 0;
      }
      .inner {
        position: absolute;
        left: 0;
        width: 100%;
        height: @height;
        line-height: @height;
        color: @color;
        text-shadow: 0 2px 4px #000;
        text-align: center;
        background-color: @backgroundColor;
        border-radius: @radius;
      }
      .up .inner {
        top: 0;
      }
      .down .inner {
        bottom: 0;
      }
      .up .shadow {
        border-top-left-radius: @radius;
        border-top-right-radius: @radius;
      }
      .down .shadow {
        border-bottom-left-radius: @radius;
        border-bottom-right-radius: @radius;
      }
    }
  }
  .flip-items .item {
    &.past {
      z-index: 3;
    }
    &.current {
      //反转到中间时候当前秒层级最大
      animation: highter-level (@time / 2) (@time / 2) linear forwards;
      z-index: 2;
    }
    &.current .up {
      z-index: 1;
    }
    &.past .up {
      animation: flip-past-up (@time / 2) linear both;
    }
    &.current .down {
      animation: flip-current-down (@time / 2) (@time / 2) linear both;
    }
    @keyframes flip-current-down {
      from {
        transform: rotateX(90deg);
      }
      to {
        transform: rotateX(0deg);
      }
    }
    @keyframes flip-past-up {
      from {
        transform: rotateX(0deg);
      }
      to {
        transform: rotateX(-90deg);
      }
    }
    @keyframes highter-level {
      from {
        z-index: 4;
      }
      to {
        z-index: 4;
      }
    }
  }

  // 控制阴影
  .flip-items .item {
    .shadow {
      position: absolute;
      width: 100%;
      height: 100%;
    }
    &.past .up .shadow {
      background: linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 1) 100%);
      animation: show (@time / 2) linear both;
    }
    &.past .down .shadow {
      background: linear-gradient(rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0.1) 100%);
      animation: show (@time / 2) linear both;
    }
    &.current .up .shadow {
      background: linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 1) 100%);
      animation: hide (@time / 2) 0.5s linear both;
    }
    &.current .down .shadow {
      background: linear-gradient(rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0.1) 100%);
      animation: hide (@time / 2) 0.5s linear both;
    }
  }
  @keyframes show {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  @keyframes hide {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
}
</style>
