import type {
  deviceData,
  webrtcData,
  webrtcItem,
  deviceConfig,
  deviceConfigItem,
  addDeviceItem,
  propertyData,
  deviceLogData
} from "@/api/base"
import { ref } from "vue"

export default {
  platform: "ios", //终端
  token: "",
  key: "0193000b-1fa1-7cb1-bb24-334c0b316e87;1730872549",
  device_sn: "",
  product_key: "",
  user_id: "",
  deviceList: [] as deviceData[],
  mqtt_status: 0, //区分登陆是否已经连接mtqq
  aesKey: "", //生成没有base64编码的key
  aesBase64Key: "", //编码过后的key
  x_key: "", //加密后的数据
  const_header: "9+/`Vj_T(Q", //异或头
  xor_key: "9nLkp\u003ei$=SIXjr]Qo2@YzlH6c[yB/,Mm", //异或解密key
  device: {} as deviceData, //设备详情数据
  webrtc_list: {} as webrtcData,
  webrtc_item: {} as webrtcItem,
  device_config: {} as deviceConfig,
  device_config_item: {} as deviceConfigItem,
  add_device_list: [] as addDeviceItem[],
  add_device_item: {} as addDeviceItem,
  set_property_config:[] as propertyData [],  //设备设置配置
  light_leve:ref<number>(0), //环境光强度
  device_log:ref({} as deviceLogData), //设备日志
  app_version: "1.1.0",
  app_name: "camtro",
  lang: "en",
  app_os: 1,
  app_id: "APP-DF3D207F-DCFC-4EF2-8AE4-BCCD47DFBB8D",
  app_informToken: "",
  app_test_flight: 0,
  app_is_dev: 0,
  timezone: "Asia/Hong_Kong",
  device_name: "",
  intentId: "", //支付ID
  clientSecret: "", //支付密钥
  wakeUp_type: 0, //唤起直播推流类型
  video_path: ref(""), //卡回放设备返回的地址
  video_state: 0, //卡回放状态
  device_status: ref<number>(0), //设备状态
  MQTTUsername: "5HwVtm19", //mqtt连接账号
  MQTTPassword: "ji>say!s{5a5", //mqtt连接密码
  old_device_sn: "", //pir触发时旧的设备ID
  old_product_key: "", //pir触发时旧的product_key
  mode: ref<string>(""), //RCT模式
  sub_id: ref<any>([]),
  mute_notify_to: ref(""), //静音通知时间戳
  username: ref<string | null>(""), //用户名
  toRFC1123:'', //请求时生成的RFC1123格式日期时间
  is_bluetooth:-1,
  iframeSrc:ref<string>(''),
  rememberAccount:ref<boolean>(false),  //是否记住账号
  isSilent:ref<boolean>(false), //首页消息是否静默
  isVoice:ref<boolean>(false), //直播是否开启语音
  showBottom: 1,
  scrollStatus: false,
  albumType: 1,
}
