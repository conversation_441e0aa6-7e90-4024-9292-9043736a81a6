<template>
  <div class="pages">
    <img class="guang" src="../assets/guang.png" alt="" />
    <div class="coupon-card">
      <img class="gift-left-hua" src="../assets/zuo.png" alt="">
      <img class="gift-right-hua" src="../assets/you.png" alt="">
      <div class="coupon-header">
        <h1 class="offer-text">Free Gift Waiting for You!</h1>
        <div class="countdown-container">
          <div class="countdown-timer">
            <div class="count-row">
              <flipcountdown
                v-if="Number(config.countdown)"
                :deadline="Number(config.countdown)"
              ></flipcountdown>
            </div>
            <div class="unit-row">
              <div class="hours">Hours</div>
              <div class="minutes">Minutes</div>
              <div class="seconds">Seconds</div>
            </div>
          </div>
        </div>
        <div class="data-bot">
          <div class="yuan">FREE</div>
          <div class="max-img">
            <van-image fit="contain" :src="decodeURIComponent(config.img)">
              <template v-slot:loading>
                <van-loading type="spinner" size="30" />
              </template>
            </van-image>
          </div>
          <div class="invite-text">Get a free device</div>
          <div class="invite-text">Click below to claim now!</div>
        </div>
      </div>
      <button class="btn shop-now-btn" @click="giftProduct">Claim Now</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import config from "@/config"
import { useRouter } from "vue-router"
import { showLoading } from "@/utils"
import flipcountdown from "@/components/Countdown.vue"

const router = useRouter()
const giftProduct = () => {
  config.showBottom.value = 1
  showLoading()
  router.push("invite")
}
</script>

<style lang="less" scoped>
.pages {
  background-color: #4774f5;
  min-height: 100vh;
  padding: 50px 16px 0 16px;
  position: relative;
  .guang {
    position: absolute;
    left: 0;
    top: -45px;
    width: 100%;
    object-fit:contain;
  }
  .coupon-card {
    background-image: url("../assets/cart.png");
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    padding: 20px 20px 40px 20px;
    min-height: 380px;
    position: relative;
    .gift-left-hua{
      position: absolute;
      left: 0;
      top: -10px;
      width: 156px;
      height: 147px;
    }
    .gift-right-hua{
      position: absolute;
      right: 0;
      top: 35px;
      width: 81px;
      height: 174px;
    }
  }
  .coupon-header {
    margin-bottom: 20px;
    .data-bot {
      padding: 15px 10px;
      background: linear-gradient(180deg, #f9f8ff 0%, #dde5fc 100%);
      border-radius: 10px;
      border: 1px solid #e9e3ff;
      position: relative;
      .invite-text {
        font-size: 13px;
        color: #525560;
        text-align: center;
        line-height: 20px;
      }
      .yuan {
        position: absolute;
        top: -25px;
        left: -30px;
        width: 89px;
        height: 89px;
        z-index: 9;
        background-image: url("../assets/yuan.png");
        background-position: center;
        background-size: 100% 100%;
        font-size: 22px;
        color: #0d503b;
        display: flex;
        align-items: center;
        justify-content: center;
        transform: rotate(-30deg);
      }
    }
  }
  .promo-text {
    font-size: 16px;
    font-weight: bold;
    color: #222;
    min-height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 21px;
    margin-bottom: 20px;
  }
  .offer-text {
    font-size: 38px;
    font-weight: bold;
    color: #183bee;
    text-align: center;
    margin-bottom: 20px;
    min-height: 76px;
  }

  .max-img {
    display: flex;
    justify-content: center;
    min-height: 145px;
    margin-bottom: 10px;
  }
  .btn {
    height: 50px;
    border: none;
    border-radius: 28px;
    cursor: pointer;
    font-size: 20px;
    transition: background-color 0.3s;
  }

  .shop-now-btn {
    width: 100%;
    background-color: #183bee;
    color: white;
    padding: 12px;
  }

  .countdown-timer{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-bottom: 20px;
  }
  .unit-row {
    padding-top: 5px;
    display: flex;
    gap: 34px;

    .hours,
    .minutes,
    .seconds {
      font-size: 10px;
      color: #999999;
      line-height: 16px;
    }
    .minutes {
      margin-left: 6px;
    }
  }
  .count-row {
    display: flex;
  }
}
</style>
