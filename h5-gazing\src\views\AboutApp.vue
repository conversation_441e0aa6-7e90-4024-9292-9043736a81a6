<template>
  <div class="pages-data">
    <Header title="关于APP" :left-type="1" />
    <div class="app-box">
      <img class="app-img" src="../assets/app.png" alt="">
    </div>
    <div class="footer">
      <div class="footer-item">
        <div class="item-left">APP版本号</div>
        <div class="item-right">v{{ config.app_version }}</div>
      </div>
      <div class="footer-item" @click="navigator(1)">
        <div class="item-left">隐私政策</div>
        <div class="item-right"><van-icon name="arrow" color="#C4C4C6"/></div>
      </div>
      <div class="footer-item" @click="navigator(2)">
        <div class="item-left">用户协议</div>
        <div class="item-right"><van-icon name="arrow" color="#C4C4C6"/></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import config from "@/config"
import { useRouter } from "vue-router"

const router = useRouter()

const navigator = (type:number)=>{
  if(type === 1){
    config.iframeSrc.value = 'https://www.camtro.cc/privacy_policy.html'
  }else {
    config.iframeSrc.value = 'https://www.camtro.cc/user_agreement.html'
  }
  router.push('iframe')
}
</script>

<style lang="less" scoped>
.app-box{
  padding-top: 100px;
  display: flex;
  justify-content: center;
  .app-img{
    width: 143px;
    height: 143px;
  }
}
.footer{
  position: absolute;
  bottom: 30px;
  left: 0;
  width: 100%;
  .footer-item{
    margin-left: 16px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 10px;
    .item-left{
      font-size: 16px;
      color: #090909;
    }
    .item-right{
      font-size: 14px;
      color: #686868;
    }
  }
}
</style>
