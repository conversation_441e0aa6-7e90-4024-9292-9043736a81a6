import config from '@/config'
let websock: any = null;
let global_callback: any = null;
let lockReconnect = false;//避免重复连接
const wsuri = "wss://service.ekenelectronics.com/wss";
// import { showNotify } from 'vant';
function createWebSocket(callback: any) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    if (websock == null || typeof websock !== WebSocket) {
        initWebSocket(callback);
    }
}

function initWebSocket(callback: any) {
    global_callback = callback;
    // 初始化websocket
    websock = new WebSocket(wsuri);

    websock.onmessage = (e: any)=> {
        websocketonmessage(e);
        ws_heartCheck.reset().start();
    };
    websock.onclose = (e: any)=> {
        websocketclose(e);
        if(global_callback){
            global_callback(0);
        }
        // showNotify({ type: 'warning', message: 'WebSocket连接断开' });
        config.websockStatus = 0;
        // 断线重连
        // ws_recontent();
    };
    websock.onopen = ()=> {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        websocketOpen();
        config.websockStatus = 1;
    };
    // 连接发生错误的回调方法
    websock.onerror = ()=> {
        // showNotify({ type: 'warning', message: 'WebSocket连接发生错误' });
        //createWebSocket();啊，发现这样写会创建多个连接，加延时也不行
        config.websockStatus = 0;
        if(global_callback){
            global_callback(0);
        }
        // 断线重连
        // ws_recontent();
    };
}

// 实际调用的方法
function sendSock(agentData: any) {
    if (websock.readyState === websock.OPEN) {
        // 若是ws开启状态
        websocketsend(agentData);
    } else if (websock.readyState === websock.CONNECTING) {
        // 若是 正在开启状态，则等待1s后重新调用
        setTimeout(()=> {
            sendSock(agentData);
        }, 1000);
    } else {
        // 若未开启 ，则等待1s后重新调用
        setTimeout(()=> {
            sendSock(agentData);
        }, 1000);
    }
}


function closeSock() {
    if(websock){
        websock.close();
    }
    websock = null;
}


// 数据接收
function websocketonmessage(msg: any) {
    // 收到信息为Blob类型时
    let result:any = null;
    if (msg.data instanceof Blob) {
        const reader = new FileReader();
        reader.readAsText(msg.data, "UTF-8");
        reader.onload = () => {
            if (typeof reader.result === "string") {
                result = JSON.parse(reader.result);
            }
            global_callback(result);
        };
    }else if(msg === 0) {
        // websocket连接断开
        global_callback(msg);
    } else {
        result = JSON.parse(msg.data);
        if(global_callback){
            global_callback(result);
        }
    }
}

// 数据发送
function websocketsend(agentData: any) {
    if(websock && websock.readyState && websock.readyState !== 3 && websock.readyState !== 4){
        websock.send(agentData);
    }
}

// 关闭
function websocketclose(e: any) {
    console.log("connection closed (" + e.code + ")");
    websock = null;
}


function websocketOpen() {
    console.log("连接打开");
    ws_heartCheck.reset().start();
    sendSock(JSON.stringify({cmd:"login",lang:localStorage.getItem('language'),token:config.sessionId}));
    sendSock(JSON.stringify({cmd:"screen_ready",device_sn:config.device_sn,appName:'Aiwit'}));
}


// 重新连接websocker(WebSocket连接地址)
function ws_recontent() {
    if(lockReconnect)return;
    lockReconnect = true;
	// 延迟避免请求过多
	setTimeout( ()=> {
        if(global_callback){
            createWebSocket(global_callback());
        }
	}, 5000);
}


// WebSocket心跳检测
const ws_heartCheck = {
    timeout: 10000,			// 20秒一次心跳
    timeoutObj: null,		// 执行心跳的定时器
    serverTimeoutObj: null,	// 服务器超时定时器
    reset:()=>{		// 重置方法
        const self:any = ws_heartCheck;
        clearTimeout(self.timeoutObj);
        clearTimeout(self.serverTimeoutObj);
        return self;
    },
    start:()=>{		// 启动方法
        const self:any = ws_heartCheck;
        self.timeoutObj = setTimeout(()=>{
            // 这里发送一个心跳信息，后端收到后，返回一个消息，在onmessage拿到返回的心跳（信息）就说明连接正常
            websocketsend(JSON.stringify({cmd:'heartbeat'}));
			// 如果超过一定时间还没重置，说明后端主动断开了
            self.serverTimeoutObj = setTimeout(()=>{
				// 如果onclose会执行reconnect，我们执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
                websocketclose({code:'手动关闭'});
            }, self.timeout);
        }, self.timeout);
    }
}


export { sendSock, createWebSocket, closeSock };