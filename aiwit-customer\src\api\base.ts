export interface userData {
  //聊天对应的接口
  question_id: number
  msg_id: number
  msg_type: number
  from_name: string
  from: string
  ctime: string
  role: number
  expiry_time: number
  uid: number
  show_button: number
  screen: number
  content: {
    txt: string
    cloud_storage_txt: any
    service_satisfaction_survey_txt: any
    url: any
    img?:string
    type?:number
  }
}

export interface sendChatData {
  //发送聊天
  session_id: string
  data: string
}

export interface videoData {
  //选择视频里的视频列表
  url: string
}

export interface pagingParams {
  //选择视频里的视频列表分页参数
  page: number
}

export interface codeData {
  //兑换码数据
  title: string
  product_name: string
  export_status: number
  img1: string
  img2: string
}

export interface formPranms {
  name: string
  phone: string
  state: string
  city: string
  street: string
  zipcode: string
}


export interface formData {
  name: string
  phone: string
  state: string
  city: string
  street: string
  zipcode: string
  order_status:number
  error_text:string
  ship_no:string
  sp_status:number
  last_time:string
}