import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: () => import('@/views/Home.vue'),
    },
    {
      path: '/responsibleDisclosure',
      name: 'responsibleDisclosure',
      component: () => import('@/views/ResponsibleDisclosure.vue'),
    },
    {
      path: '/updatePeriod',
      name: 'updatePeriod',
      component: () => import('@/views/UpdatePeriod.vue'),
    },
    {
      path: '/securityReporting',
      name: 'securityReporting',
      component: () => import('@/views/SecurityReporting.vue'),
    },
    {
      path: '/softwareUpdates',
      name: 'softwareUpdates',
      component: () => import('@/views/SoftwareUpdates.vue'),
    },
    {
      path: '/vulnerabilityReport',
      name: 'vulnerabilityReport',
      component: () => import('@/views/VulnerabilityReport.vue'),
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/LogIn.vue'),
    },
    {
      path: '/createAccount',
      name: 'createAccount',
      component: () => import('@/views/CreateAccount.vue'),
    },
    {
      path: '/forgotPassword',
      name: 'forgotPassword',
      component: () => import('@/views/ForgotPassword.vue'),
    },
    {
      path: '/index',
      name: 'index',
      component: () => import('@/views/Index.vue'),
    },
  ],
  scrollBehavior: () => ({ top: 0 }),
})

export default router
