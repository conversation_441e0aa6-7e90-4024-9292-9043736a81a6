<template>
  <div class="code-data">
    <div class="code-title">{{ mdl.title }}</div>
    <div class="code-text">{{ mdl.product_name }}</div>
    <div class="code-box">
      <img
        v-if="mdl.export_status == 2"
        class="code-imgs"
        :src="mdl.img2"
        alt="兑换"
      />
      <img v-else class="code-imgs" :src="mdl.img1" alt="兑换" />
    </div>
    <div class="code-footer">
      <div v-if="mdl.export_status == 2" class="code-add">
        <img
          class="suues-img"
          src="https://cache.gdxp.com/acce/assets/4.png"
          alt=""
        />
        <div>{{ $t('home.successful') }}</div>
      </div>
      <div v-else-if="mdl.export_status == 1" class="code-btn" @click="showBtn">
        {{ $t('home.exchange') }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, toRefs, onMounted } from 'vue'
import { getExchange, saveExchange } from '@/api/index'
import { type codeData } from '@/api/base'
import { showConfirmDialog } from 'vant'
import config from '@/config'
import { i18n } from '@/lang'
const { t } = i18n.global as any

const props = defineProps({
  code_sn: {
    type: String,
    default: '',
  },
})

const state = reactive({
  mdl: {
    title: '',
    product_name: '',
    img1: '',
    img2: '',
    export_status: 0,
  } as codeData,
})
const { mdl } = toRefs(state)
function fetch() {
  getExchange(props.code_sn).then((res: any) => {
    if (res.resultCode == 0) {
      state.mdl = res.content
    }
  })
}

const showBtn = () => {
  showConfirmDialog({
    title: t('home.hint'),
    message: t('home.exchangeText'),
    confirmButtonText: t('home.ok'),
    cancelButtonText: t('home.cancel'),
  }).then(() => {
    saveData()
  })
}

function saveData() {
  saveExchange(config.sessionId, config.device_sn, props.code_sn).then(
    (res: any) => {
      if (res.resultCode == 0) {
        state.mdl.export_status = 2
      }
    }
  )
}

onMounted(() => {
  fetch()
})
</script>

<style lang="less" scoped>
.code-data {
  width: 240px;
  min-height: 285px;
}
.code-title {
  font-size: 15px;
  color: #494854;
  font-weight: 700;
  margin-bottom: 5px;
  line-height: 18px;
}
.code-text {
  font-size: 14px;
  color: #494854;
  margin-bottom: 10px;
  line-height: 16px;
}
.code-box {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
  .code-imgs {
    display: block;
    width: 238px;
    height: 128px;
  }
}
.code-footer {
  display: flex;
  justify-content: center;
  padding-bottom: 10px;
  .code-btn {
    width: 214px;
    height: 36px;
    border-radius: 20px;
    background-color: #f6fdff;
    border: 2px solid #415cf6;
    font-size: 15px;
    font-weight: bold;
    color: #415cf6;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .code-add {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    color: #415cf6;
  }
  .suues-img {
    width: 20px;
    height: 20px;
    margin-right: 5px;
  }
}
</style>
