# aiwit-mall项目

## 项目简介

基于vue3+vant+ts+vite+pinia开发的一款移动端商城

## 项目文件

build - 打包更新后的index.html需要上传到Git给服务器更新
dist - 打包后的文件
node_modules - 第三方插件npm安装的包

    -  api - 调用服务器接口请求地址方法
    -  assets - 静态资源（图片）
    -  common - 公共资源（css样式）
    -  components - 封装的子组件
    -  config - APP传过来的参数（全局变量）
    -  directives - 封装的防抖节流函数
    -  hooks - 全局主题色配置

src - lang - 国际化多语言配置

    -  porps - 与服务器交互的类型检测
    -  router - 项目页面路由
    -  stores - pinia全局存储
    -  utils - 全局封装的公共方法
    -  views - 项目页面文件
    -  app.vue - 项目根页面
    -  mian.ts - 生成根页面引入第三方插件

.gitattributes - git合并
.gitignore - git忽略上传文件
.prettierrc - 代码格式配置文件
env.d - 全局ts声明
index.html - 项目根index.html
vite.config.ts - 项目打包启动配置文件

```bash
cd aiwt-mall
git remote add origin https://gitlab.kement.cn/h5-team/aiwt-mall
git branch -M main
git push -uf origin main
```

## 启动网站

要在本地启动网站，您需要安装 [Node.js](https://nodejs.org/) 和 [Yarn](https://yarnpkg.com/)。你可以运行以下命令检查是否安装了这两个软件：

## 启动指令

```bash
npm i
npm run dev
```

或者

```bash
pnpm i
pnpm run dev
```

启动后，可在 `http://localhost:8899/` 进行实时浏览。
