<template>
  <div class="page">
    <Header title="连接WiFi" :wifiType="1"/>
    <div class="new-title">此设备只支持2.4GHz Wi-Fi</div>
    <div class="conariner">
      <div class="con-item cons-one">
        <van-field
          v-model.trim="nameValue"
          label="WIFI名称"
          placeholder="请输入"
          label-align="top"
          :style="{ fontSize: nameValue.length ? '24px' : '14px' }"
        >
          <!-- <template #right-icon>
            <van-icon name="exchange" v-if="config.platform == 'android'" />
          </template> -->
        </van-field>
      </div>
      <div class="con-tip">请确保此Wi-Fi为2.4GHz Wi-Fi</div>
      <div class="con-item">
        <van-field
          v-model.trim="passValue"
          label="WIFI密码"
          placeholder="Please enter the WiFi network password"
          label-align="top"
          :type="inputType ? 'text' : 'password'"
        >
          <template #right-icon>
            <van-icon name="eye" v-if="inputType" @click="inputType = !inputType" />
            <van-icon name="closed-eye" v-else @click="inputType = !inputType" />
          </template>
        </van-field>
      </div>
      <div class="cons-text">WiFi名称和密码区分大小写，确保输入正确</div>
    </div>
    <div class="footer-btn">
      <van-button
        type="primary"
        round
        size="large"
        color="linear-gradient(90deg,#4774F5 3.73%,#4977F4 100%)"
        @click="stepData"
      >
        下一步
      </van-button>
    </div>

    <!-- 连接WIFI弹框 -->
    <van-popup
      v-model:show="showCenter"
      :close-on-click-overlay="false"
      closeable
      round
      :style="{ width: '85%' }"
    >
      <div class="modal">
        <div class="modal-title">确认网络</div>
        <div class="modal-tip">请再次确认你的Wi-Fi名称和密码是否正确（注意区分字母大小写）</div>
        <div class="modal-name">Wi-Fi名称</div>
        <div class="modal-wifi">
          <div>{{ nameValue }}</div>
          <div class="modal-wifi-text">请确保此Wi-Fi为2.4GHz Wi-Fi</div>
        </div>
        <div class="modal-name">Wi-Fi密码</div>
        <div class="modal-no">{{ passValue.length ? passValue : "该Wi-Fi没有密码?" }}</div>
        <div class="modal-footer">
          <van-button class="modal-btn cancel" round type="primary" color="#EEEEEE">
            取消
          </van-button>
          <van-button
            class="modal-btn"
            round
            type="primary"
            color="#4774F5"
            v-if="config.add_device_item.bluetooth === 1"
            @click="confirmWifi"
          >
            确认
          </van-button>
          <router-link to="./scanCode" v-else>
            <van-button class="modal-btn" round type="primary" color="#4774F5">确认</van-button>
          </router-link>
        </div>
      </div>
    </van-popup>

    <!-- 连接蓝牙设备弹框 -->
    <van-popup
      v-model:show="showBlue"
      :close-on-click-overlay="false"
      round
      :style="{ width: '85%' }"
    >
      <div class="modal-data">
        <img class="network-img" src="../assets/2.gif" alt="" />
        <div class="network-text">
          正在连接网络
          <span class="network-span">{{ countdownTime }}</span>
          <span class="network-spans">s</span>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import { ref, onUnmounted } from "vue"
import { showToast, showConfirmDialog } from "vant"
import config from "@/config"
import { send_wifi_params } from "@/utils"
import { useRouter } from "vue-router"
import { get_qr_key } from "@/api"

const router = useRouter()
const nameValue = ref<string | any>("")
const passValue = ref<string | any>("")
const inputType = ref(false)
const showCenter = ref(false)
const showBlue = ref(false)
const countdownTime = ref(59)
const timer = ref()

const countdown = () => {
  timer.value = setInterval(() => {
    if (countdownTime.value === 0) {
      showBlue.value = false
      clearInterval(timer.value)
      timer.value = null
      keepWaiting()
    } else {
      countdownTime.value--
    }
  }, 1000)
}

const keepWaiting = () => {
  showConfirmDialog({
    message: "注册设备超时",
    confirmButtonText: "继续等待",
    cancelButtonText: "退回",
    cancelButtonColor: "#ee0a24",
  })
    .then(() => {
      showBlue.value = true
      countdownTime.value = 59
      countdown()
    })
    .catch(() => {
      router.push("deviceReset")
    })
}

const stepData = () => {
  if (!nameValue.value) return showToast("请输入wifi名称")
  localStorage.setItem("wifiName", nameValue.value)
  if (passValue.value) {
    localStorage.setItem("wifiPass", passValue.value)
  }else {
    localStorage.setItem("wifiPass",'')
  }
  showCenter.value = !showCenter.value
}

const confirmWifi = () => {
  showCenter.value = false
  showBlue.value = true
  countdown()
  get_qr_key().then((res: any) => {
    if (res.data) {
      send_wifi_params(nameValue.value, passValue.value,`${import.meta.env.VITE_APP_BASE_API}\n${nameValue.value}\n${passValue.value}\n${res.data.qr_key}\n${"en"}\n${8}\n`)
    }
  })
}

onUnmounted(()=>{
  clearInterval(timer.value)
})

</script>

<style lang="less" scoped>
.conariner {
  padding: 0px 20px;
  .con-item {
    border-bottom: 1px solid #eeeeee;
    .con-name {
      height: 40px;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #222;
      font-weight: 700;
    }
  }
  .cons-one {
    :deep(.van-field__control) {
      color: #4774f5;
    }
  }
  .con-tip {
    padding-top: 10px;
    font-size: 13px;
    color: #fa2a0e;
    margin-bottom: 10px;
  }
  .cons-text {
    font-size: 13px;
    color: #999999;
    padding-top: 10px;
  }
}
.new-title {
  padding-left: 20px;
  height: 48px;
  background-color: #f3f6fc;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  color: #4774f5;
  font-size: 15px;
}
.van-cell {
  padding-left: 0px;
  padding-right: 0px;
}
:deep(.van-field__label) {
  font-size: 16px;
  font-weight: 700;
  color: #222;
}
.footer-btn {
  position: absolute;
  bottom: 50px;
  width: 89%;
  margin: 0 auto;
  padding: 0px 20px;
  left: 0px;
}
.modal {
  padding: 20px;
  .modal-title {
    font-size: 18px;
    color: #222;
    font-weight: 700;
    margin-bottom: 20px;
  }
  .modal-tip {
    font-size: 14px;
    color: #666666;
    margin-bottom: 15px;
    border-bottom: 1px solid #f3f3f3;
    padding-bottom: 15px;
  }
  .modal-name {
    font-size: 14px;
    color: #999999;
    margin-bottom: 10px;
  }
  .modal-wifi {
    font-size: 24px;
    color: #4774f5;
    margin-bottom: 20px;
  }
  .modal-wifi-text {
    padding-top: 10px;
    font-size: 13px;
    color: #fa2a0e;
  }
  .modal-no {
    padding-top: 5px;
    font-size: 16px;
    font-weight: 700;
    color: #999999;
  }
  .modal-footer {
    padding-top: 30px;
    display: flex;
    justify-content: space-between;
    .modal-btn {
      width: 120px;
    }
    .cancel {
      color: #666666 !important;
    }
  }
}

.modal-data {
  padding: 20px;
  text-align: center;
  .network-img {
    width: 205px;
    height: 205px;
    margin-bottom: 10px;
  }
  .network-text {
    font-size: 14px;
    color: #000;
    font-weight: bold;
    .network-span {
      font-size: 18px;
      color: #4774f5;
    }
    .network-spans {
      font-size: 13px;
      color: #4774f5;
    }
  }
}
</style>
