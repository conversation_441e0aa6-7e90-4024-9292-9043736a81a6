<template>
  <div class="share-data">
    <Header title="设备管理权转交" :left-type="1"/>
    <div class="share-main">
      <div class="share-box">
        <div class="from-data">
          <div class="from-title">将设备转让给</div>
          <input
            class="footer-input"
            v-model.trim="textValue"
            type="text"
            placeholder="Email address"
          />
          <van-button type="primary" color="#F65252" @click="sendClick('')" size="large" round>
            确定
          </van-button>
        </div>
        <div class="share-list">
          <div class="share-title">分享记录</div>
          <div v-if="state.shareList.length">
            <div class="share-item" v-for="(item, index) in state.shareList" :key="index">
              <div class="share-item-left">
                <div class="serial">{{ index > 9 ? index : `0${index + 1}` }}</div>
              </div>
              <div class="share-item-right">
                <div class="share-item-user">
                  <div class="share-item-text">{{ item.email }}</div>
                  <div class="share-item-time">{{ timestampToDate(item.create_at,config.timezone) }}</div>
                </div>
                <img class="transfer-img" @click="transfer(item)" src="../assets/device/zr.png" alt="" />
              </div>
            </div>
          </div>
          <van-empty v-if="state.shareList.length == 0" description="还未分享过" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import { ref, reactive, onMounted } from "vue"
import { type shareData } from "@/api/base"
import { device_share_list,device_transfer } from "@/api/index"
import config from "@/config"
import { showConfirmDialog } from "vant"
import { timestampToDate } from '@/utils'
import { showToast } from "vant"
import { useRouter } from "vue-router"

const router = useRouter()
const textValue = ref("")
const state = reactive({
  shareList: [] as shareData[],
})
const sendClick =  (user_id:string) => {
  device_transfer(
    user_id,
    config.product_key,
    config.device_sn,
    textValue.value
  ).then((res: any) => {
    if (res.code === 0) {
      showToast("设备转让成功")
      config.device.is_primary = false
      setTimeout(() => {
        router.push("/")
      }, 2000)
    }
  })
}

const fetch = () => {
  device_share_list(config.product_key, config.device_sn).then((res: any) => {
    if (res.code === 0) {
      state.shareList = res.data || []
    }
  })
}

const transfer = (item:shareData) => {
  textValue.value = item.email
  showConfirmDialog({
    message: `将设备转让给：${textValue.value}`,
    confirmButtonText: "转让设备",
  })
    .then(() => {
      sendClick(item.user_id)
    })
    .catch(() => {
      
    })
}

onMounted(()=>{
  fetch()
})
</script>

<style lang="less" scoped>
.share-data {
  min-height: 100vh;
  background: #f8f8f8;
  box-sizing: border-box;
  .share-main {
    padding: 20px 15px;
    .share-box {
      border-radius: 10px;
      background: #ffffff;
      overflow: hidden;
      .from-data {
        padding: 20px;
        .from-title {
          font-size: 16px;
          color: #222222;
          font-weight: 700;
          margin-bottom: 10px;
          margin-left: 10px;
        }
        .footer-input {
          width: 100%;
          height: 50px;
          border-radius: 25px;
          padding-left: 12px;
          border: 1px solid #fff;
          background-color: #f5f5f5;
          outline: none;
          font-size: 16px;
          color: #494854;
          font-weight: 700;
          -webkit-appearance: none;
          line-height: 50px;
          margin-bottom: 30px;
          box-sizing: border-box;
          &::placeholder {
            color: #848CA4;
            font-weight: 400;
          }
        }
      }
      .share-list {
        padding: 0 20px 0 20px;
        background-color: #fff;
        // border-radius: 8px;
        .share-title {
          font-size: 16px;
          color: #222222;
          font-weight: 700;
          margin-bottom: 20px;
        }
        .share-item {
          display: flex;
          height: 65px;
          padding: 0 10px;
          .share-item-left {
            width: 45px;
            padding-top: 12px;
            .serial {
              width: 35px;
              height: 35px;
              border-radius: 50%;
              background-color: rgb(211,224,243);
              border: 1px solid #eee;
              font-size: 16px;
              color: #666666;
              font-weight: 500;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
          .share-item-right {
            flex: 1;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: .5px solid #f3f3f3;
            .share-item-user {
              display: flex;
              flex-direction: column;
              justify-content: center;
              .share-item-text {
                font-size: 15px;
                color: #999999;
                margin-bottom: 5px;
              }
              .share-item-time {
                font-size: 15px;
                color: #888888;
                font-weight: 700;
              }
            }
            .transfer-img {
              width: 74px;
              height: 32px;
            }
          }
        }
      }
    }
  }
}
</style>
