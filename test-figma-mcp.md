# 测试 Figma MCP 连接

## 快速测试步骤

### 1. 确认 Figma 服务器运行
打开终端，运行以下命令检查服务器是否可访问：

```bash
curl http://127.0.0.1:3845/sse
```

如果返回连接信息，说明服务器正在运行。

### 2. 在 VS Code 中测试

1. 打开 VS Code
2. 按 `Ctrl+Shift+P` (Windows) 或 `Cmd+Shift+P` (Mac)
3. 输入 "Chat: Open Chat"
4. 在聊天界面中，查看是否有 MCP 服务器连接状态

### 3. 测试提示

在 VS Code Chat 中尝试以下提示：

```
请检查 Figma MCP 服务器连接状态
```

或者：

```
请列出可用的 Figma MCP 工具
```

## 常见问题排查

### 问题 1: 无法连接到 MCP 服务器
**解决方案：**
1. 确保 Figma 桌面应用正在运行
2. 确保已在 Figma 中启用 Dev Mode MCP Server
3. 重启 VS Code

### 问题 2: 没有看到 MCP 工具
**解决方案：**
1. 检查是否安装了 GitHub Copilot 扩展
2. 确保 Copilot 已登录并激活
3. 重启 VS Code 和 Figma

### 问题 3: 提示权限不足
**解决方案：**
1. 确保你的 Figma 账户有 Professional/Organization/Enterprise 计划
2. 确保你有 Dev 或 Full 席位

## 成功连接的标志

当 MCP 成功连接时，你应该能看到：
- VS Code Chat 中显示 Figma MCP 服务器已连接
- 可以使用 Figma 相关的工具和命令
- 能够从 Figma 链接或选择中获取设计信息

## 下一步

连接成功后，你就可以：
1. 在 Figma 中选择设计元素
2. 复制 Figma 链接
3. 在 VS Code 中使用 AI 助手根据设计生成 Vue 代码

记住使用我们配置的规则文件中的提示模板，这样生成的代码会更符合你的项目结构和编码规范。
