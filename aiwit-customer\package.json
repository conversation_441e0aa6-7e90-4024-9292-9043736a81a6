{"name": "demo", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "test:unit": "cypress run --component", "test:unit:dev": "cypress open --component", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.cypress-ct.json --composite false"}, "dependencies": {"ali-oss": "^6.18.1", "axios": "^1.5.1", "fastclick": "^1.0.6", "js-md5": "^0.8.3", "less": "^4.2.0", "pinia": "^2.1.6", "reset-css": "^5.0.2", "vant": "^4.7.2", "vconsole": "^3.15.1", "vue": "^3.3.4", "vue-i18n": "^9.5.0", "vue-router": "^4.2.4"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/ali-oss": "^6.16.10", "@types/node": "^18.18.6", "@vitejs/plugin-vue": "^4.3.4", "@vue/tsconfig": "^0.4.0", "cypress": "^13.2.0", "moment": "^2.29.4", "npm-run-all2": "^6.0.6", "postcss-px-to-viewport-8-plugin": "^1.2.3", "start-server-and-test": "^2.0.0", "typescript": "~5.2.0", "vite": "^4.4.9", "vue-tsc": "^1.8.11"}}