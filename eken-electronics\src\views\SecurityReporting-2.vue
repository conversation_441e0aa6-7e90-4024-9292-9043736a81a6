<template>
  <Nav />
  <div class="security-reporting">
    <h1>EKEN ELECTRONICS Vulnerability Management Solution</h1>
    <p class="">
      EKEN ELECTRONICS attaches great importance to the security of its products
      and services, and is committed to developing safe and reliable products to
      ensure user privacy protection. At the same time, the security researchers
      play an important role in protecting EKEN ELECTRONICS products and
      consumers. we have developed a vulnerability disclosure policy and
      established a complete vulnerability management process in accordance with
      standards such as ISO27001, ISO27701 to improve product security and
      ensure timely responses when vulnerabilities are discovered.
    </p>
    <h2>I. Vulnerability Qualitative Severity Ratings</h2>
    <p class="">
      EKEN ELECTRONICS uses the common industry standard for assessing the
      severity of suspected security vulnerabilities in products. Using the CVSS
      (Common Vulnerability Scoring System as an example, this system is
      composed of three metric groups: Base, Temporal, and Environmental. We
      also encourage users to assess the actual environmental score based on
      their network conditions. This score is used as the final vulnerability
      score in the specific environment to support decision-making on
      vulnerability mitigation deployment.
    </p>
    <p class="mt16">
      Different standards are adopted in different industries. EKEN ELECTRONICS
      uses the Security Severity Rating (SSR) as a simpler way to classify
      vulnerabilities. With SSR, we can classify vulnerabilities as critical,
      high, medium, low, and informational based on the overall severity score.
    </p>

    <h2>II. Reporting The Vulnerability Guidelines</h2>
    <p class="">
      If you have discovered an issue that you believe is an in-scope
      vulnerability, please
      <span
        class="pointer"
        style="color: #00ace7"
        @click="nav('/VulnerabilityReport')"
        >submit a vulnerability report.</span
      >
    </p>
    <p class="mt16">In your report, please include the following details:</p>

    <ul>
      <li class="mt16">
        · The model and version, website, IP or page of the observed
        vulnerability.
      </li>
      <li class="">
        · A brief description of the vulnerability type, such as: "XSS
        vulnerability."
      </li>
      <li class="">
        · Steps to reproduce. These steps should be benign, non-destructive, and
        proof of concept. This helps to ensure that the report can be and proof
        of concept. quickly and accurately. It also reduces the likelihood of
        duplicate reports, or malicious exploitation of some vulnerabilities,
        such as sub-domain takeovers.
      </li>
    </ul>
    <h2>III. Response Time</h2>
    <ol>
      <li class="">
        1.EKEN ELECTRONICS Security Emergency Response Center staff will confirm
        the received vulnerability report and follow up to start assessing the
        problem within 1 working day..
      </li>
      <li class="">
        2.Serious vulnerabilities will be followed up within 24 hours, and a
        preliminary conclusion and score will be given.
      </li>
      <li class="">
        3.High-risk vulnerabilities will be followed up within 3 working days,
        and preliminary conclusions and scores will be given.
      </li>
      <li class="">
        4.The remaining vulnerabilities will be followed up and scored within 7
        working days. If the reporter thinks it is an emergency, they can send
        an email to<a
          href="mailto:<EMAIL>"
          rel="noopener noreferrer"
          target="_blank"
        >
          <EMAIL> </a
        >. Expedited processing will be carried out after confirmation by the
        reviewer.
      </li>
    </ol>
    <h2>IV. Vulnerability Disclosure Instructions</h2>
    <p class="">
      Vulnerability management is managed based on the life cycle of
      product/software versions. EKEN ELECTRONICS will manage the
      vulnerabilities of all products before the end of service and support
      (EOS).
    </p>
    <p class="mt16">
      To protect our users, EKEN ELECTRONICS will not disclose, discuss, or
      confirm any security issues until a full investigation has been completed
      and an update is available. We kindly ask reporting parties to keep
      vulnerabilities confidential and not share unresolved vulnerabilities with
      third parties or make them public until EKEN ELECTRONICS provides the
      related patch solution.
    </p>
    <p class="mt16">
      In order to better support customers in patch deployment and risk
      assessments, EKEN ELECTRONICS will simultaneously publish vulnerability
      patching status in Software updates . It is recommended that you follow
      the update prompts to upgrade to a new product/software version or install
      the latest patches to reduce the risk of vulnerabilities.
    </p>

    <h1>Vulnerability Handling Process</h1>

    <p class="mt16">1. Vulnerability Submission</p>
    <p class="">
      - White-hat hackers can submit vulnerabilities through official channels
      provided by EKEN ELECTRONICS.
    </p>
    <p class="">
      - The vulnerability cannot be edited or modified once submitted. Please
      ensure the accuracy of the submitted information.
    </p>

    <p class="mt16">2. Vulnerability Review</p>
    <p class="">
      - EKEN ELECTRONICS Security Team will review the submitted vulnerabilities
      as soon as possible, typically within 1 working day.
    </p>
    <p class="">
      - During holidays or periods of high vulnerability submission, the review
      process may take longer but is generally completed within 5 working days.
    </p>

    <p class="mt16">3. Vulnerability Rewards</p>
    <p class="">
      - White-hat hackers who submit vulnerabilities approved by EKEN
      ELECTRONICS will receive rewards such as EKEN ELECTRONICS products,
      Points, or G coins.
    </p>

    <p class="">
      - For identical vulnerabilities submitted by different white-hat hackers,
      EKEN ELECTRONICS will not provide duplicate rewards:
    </p>

    <p class="">
      a. If the same vulnerability is submitted at the same time, the reward
      will be given to the white-hat hacker with the most comprehensive report.
    </p>

    <p class="">
      b. If the same vulnerability is submitted at different times, the reward
      will be given to the white-hat hacker who submitted it first.
    </p>
    <p class="mt16">4. Vulnerability Closure</p>
    <p class="">
      - EKEN ELECTRONICS will confirm, fix the vulnerability, and close its
      lifecycle.
    </p>

    <p class="mt16">Disclaimer:</p>
    <p class="">
      White-hat hackers should submit vulnerabilities in compliance with
      applicable laws and regulations. Without prior permission from EKEN
      ELECTRONICS, white-hat hackers must not disclose, attack, or abuse the
      discovered vulnerabilities to any third party outside of EKEN ELECTRONICS.
      Violators will be held legally responsible.
    </p>

    <div @click="nav('/VulnerabilityReport')" class="submit-button pointer">
      Submit Report
    </div>
  </div>
</template>

<script setup lang="ts">
import Nav from "@/components/Nav.vue";
import { useRouter } from "vue-router";
const router = useRouter();
const nav = (url: string) => {
  router.push(url);
};
</script>
<style lang="less" scoped>
.security-reporting {
  color: rgb(136, 136, 136);
  font-size: 18px;
  background-color: #fff;
  padding: 0 35px 30px;
  line-height: 23px;
  padding-bottom: 80px;

  h1 {
    text-align: center;
    color: #000000d9;
    font-size: 24px;
    font-weight: 600;
    line-height: 26px;
  }

  h2 {
    font-weight: 600;
    margin-top: 16px;
    margin-bottom: 16px;
    line-height: 25px;
    font-size: 20px;
    color: #000000d9;
  }

  .mt16 {
    margin-top: 16px;
  }

  a {
    color: #00ace7;
  }

  .submit-button {
    margin-top: 50px;
    width: 100%;
    height: 32px;
    line-height: 32px;
    background-color: #00ace7;
    border-radius: 4px;
    color: #fff;
    text-align: center;
    font-size: 18px;
    font-weight: 400;
    padding: 0 20px;
  }
}
.pointer {
  cursor: pointer;
}
</style>
