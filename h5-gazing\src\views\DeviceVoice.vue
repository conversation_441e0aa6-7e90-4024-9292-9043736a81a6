<template>
  <div class="voice-pages">
    <div class="voice-top">
      <img class="voice-img" src="../assets/ring_logo.png" alt="" />
    </div>
    <div class="voice-title">{{ config.device.nickname }}</div>
    <div class="voice-tip">{{ `There are visitors for ${config.device.nickname || ""}` }}</div>
    <div class="voice-down">{{ countNum }}s</div>
    <div class="voice-footer">
      <img class="voice-btn" @click="clickNo" src="../assets/no.png" alt="" />
      <img class="voice-btn" @click="clickAnswer" src="../assets/answer.png" alt="" />
    </div>
  </div>
</template>

<script setup lang="ts">
import config from "@/config"
import { ref, onActivated } from "vue"
import { useRouter } from "vue-router"
import { Standby } from "@/components/WebRTCClient"

const router = useRouter()
const countNum = ref<number>(10)
const intervalId = ref<any>(null)
const fetch = () => {
  intervalId.value = setInterval(() => {
    if (countNum.value > 0) {
      countNum.value--
    } else {
      Standby(true)
      router.go(-1);
      clearInterval(intervalId.value)
    }
  }, 1000)
}

const clickAnswer = () => {
  if(config.old_device_sn && config.old_device_sn !== config.device_sn){
    Standby(true)
  }
  config.wakeUp_type = 2
  clearInterval(intervalId.value)
  setTimeout(() => {
    router.push("/deviceDetails")
  }, 300)
}

const clickNo = () => {
  router.go(-1);
}

onActivated(() => {
  countNum.value = 10
  fetch()
})
</script>

<style lang="less" scoped>
.voice-pages {
  background-color: #201f24;
  height: 100vh;
  text-align: center;
  .voice-top {
    padding-top: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    .voice-img {
      width: 133px;
      height: 133px;
    }
  }
  .voice-title {
    font-size: 20px;
    color: #fff;
    font-weight: bold;
    text-align: center;
    margin-bottom: 10px;
  }
  .voice-tip {
    font-size: 15px;
    color: #fff;
    margin-bottom: 10px;
  }
  .voice-down {
    font-size: 15px;
    color: #fff;
  }
  .voice-footer {
    position: fixed;
    bottom: 15%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    .voice-btn {
      width: 72px;
      height: 72px;
    }
  }
}
</style>
