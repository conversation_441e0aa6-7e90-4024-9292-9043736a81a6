import { createRouter, createWebHashHistory } from "vue-router"
import config from "@/config"
import Cloud from "../views/Cloud.vue"

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "cloud",
      component: Cloud,
    },
    {
      path: "/giftItem",
      name: "giftItem",
      component: () => import("../views/GiftItem.vue"),
    },
    {
      path: "/guide",
      name: "guide",
      component: () => import("../views/Guide.vue"),
    },
    {
      path: "/invite",
      name: "invite",
      component: () => import("../views/Invite.vue"),
    },
    {
      path: "/inviteDetails",
      name: "inviteDetails",
      component: () => import("../views/InviteDetails.vue"),
    },
    {
      path: "/sendUsd",
      name: "sendUsd",
      component: () => import("../views/SendUsd.vue"),
    },
    {
      path: "/prizeDraw",
      name: "prizeDraw",
      component: () => import("../views/PrizeDraw.vue"),
    },
  ],
  scrollBehavior: () => ({ left: 0, top: 0 }),
})

// 路由拦截
router.beforeEach((to, from, next) => {
  if (config.show_type === '10' && config.state === '0' && to.path == '/') {
    next('/giftItem')
  } else if (config.show_type === '10' && config.state === '1' && to.path == '/') {
    next('/inviteDetails')
  }else {
    next()
  }
})

export default router
