<template>
  <div class="container" v-if="useStore.platform === 'android'">
    <div class="container-top">
      <div class="top-left">
        <img
          class="top-imgs"
          src="https://cache.gdxp.com/acce/assets/back.png"
          alt=""
          @click="goBack"
        />
      </div>
      <div class="top-right">{{ $t("home.title") }}</div>
    </div>
    <div class="box">
      <div class="android-gift-flex" @click="giftProduct" v-if="config.msg_id && isRepair === '0'">
        <img class="gift-img" src="../assets/4.png" alt="" />
      </div>
      <div class="tip">Service Hours：Monday to Friday，9:00 AM - 5:00 PM</div>
      <div v-if="useStore.tabbarStatus">
        <div
          class="new-data"
          @click="cardData"
          :class="
            toolStatus && screen == 0 && chatList.length > 1
              ? 'new-datas'
              : toolStatus && !evaluateShow
              ? 'new-datasaa'
              : (screen == 1 || screen == 2 || screen == 3) && toolStatus
              ? 'new-datasaa'
              : screen == 1 || screen == 2 || screen == 3 || !evaluateShow
              ? 'new-dataa'
              : ''
          "
        >
          <div class="box-conter" v-for="(item, index) in chatList" :key="index">
            <div class="box-time" v-if="item.role == 1">
              {{ item.from_name || item.from }} ({{ item.ctime }})
            </div>
            <div class="box-times" v-else>{{ item.ctime }}</div>
            <div class="box-left" :class="item.role == 0 ? 'box-right' : ''">
              <div
                class="box-p new-box-p"
                v-if="
                  item.uid === 3 &&
                  item.show_button &&
                  item.content.txt &&
                  (item.content.txt.indexOf('.png') !== -1 ||
                    item.content.txt.indexOf('.jpg') !== -1 ||
                    item.content.txt.indexOf('.jpeg') !== -1 ||
                    item.content.txt.indexOf('.svg') !== -1 ||
                    item.content.txt.indexOf('.bmp') !== -1 ||
                    item.content.txt.indexOf('.gif') !== -1)
                "
              >
                <InviteItem :img-url="item.content.txt" @clickGift="giftProduct" />
              </div>
              <div
                class="box-p"
                v-else-if="
                  item.content.txt &&
                  (item.content.txt.indexOf('.png') !== -1 ||
                    item.content.txt.indexOf('.jpg') !== -1 ||
                    item.content.txt.indexOf('.jpeg') !== -1 ||
                    item.content.txt.indexOf('.svg') !== -1 ||
                    item.content.txt.indexOf('.bmp') !== -1 ||
                    item.content.txt.indexOf('.gif') !== -1)
                "
              >
                <img
                  class="user-img"
                  :src="item.content.txt"
                  alt="加载失败"
                  @click="showImage(item.content.txt)"
                />
              </div>
              <div
                class="box-p"
                v-else-if="item.content.txt && item.content.txt.indexOf('.mp4') !== -1"
              >
                <video
                  class="banen-video"
                  controls
                  disablePictureInPicture
                  controlslist="nodownload nofullscreen noremoteplayback noplaybackrate"
                  :src="item.content.txt"
                ></video>
              </div>
              <div class="box-p" v-else-if="item.content.url">
                <a class="box-p-a" :href="item.content.url" rel="noopener noreferrer">
                  {{ item.content.url }}
                </a>
              </div>
              <div class="box-p" v-else-if="item.content.cloud_storage_txt">
                <Extend
                  :cloudStorage="item.content.cloud_storage_txt"
                  :expiryTime="item.expiry_time"
                />
              </div>
              <div class="box-p" v-else-if="item.content.service_satisfaction_survey_txt">
                <EvaluateItem
                  :survey="item.content.service_satisfaction_survey_txt"
                  :msgId="msgId"
                />
              </div>
              <div
                class="box-p"
                v-else-if="item.content.txt && item.content.txt.indexOf('Codesn:') !== -1"
              >
                <Exchange :code_sn="item.content.txt.replace('Codesn:', '')" />
              </div>
              <div class="box-p new-box-d" v-else-if="item.content.type && item.content.type === 2">
                <ChangeDeviceItem @device-address="deviceAddress" />
              </div>
              <div
                class="box-p"
                style="word-break: normal; overflow-wrap: break-word"
                v-else
                v-html="item.content.txt"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <div
          class="new-dataf"
          @click="cardData"
          :class="
            toolStatus && evaluateShow
              ? 'new-datafs'
              : toolStatus && !evaluateShow
              ? 'new-dataxa'
              : toolStatus && chatList.length == 0 && (screen == 1 || screen == 2 || screen == 3)
              ? 'new-datas'
              : screen == 1 || screen == 2 || screen == 3 || !evaluateShow
              ? 'new-datafa'
              : ''
          "
        >
          <div class="box-conter" v-for="(item, index) in chatList" :key="index">
            <div class="box-time" v-if="item.role == 1">
              {{ item.from_name || item.from }} ({{ item.ctime }})
            </div>
            <div class="box-times" v-else>{{ item.ctime }}</div>
            <div class="box-left" :class="item.role == 0 ? 'box-right' : ''">
              <div
                class="box-p new-box-p"
                v-if="
                  item.uid === 3 &&
                  item.show_button &&
                  item.content.txt &&
                  (item.content.txt.indexOf('.png') !== -1 ||
                    item.content.txt.indexOf('.jpg') !== -1 ||
                    item.content.txt.indexOf('.jpeg') !== -1 ||
                    item.content.txt.indexOf('.svg') !== -1 ||
                    item.content.txt.indexOf('.bmp') !== -1 ||
                    item.content.txt.indexOf('.gif') !== -1)
                "
              >
                <InviteItem :img-url="item.content.txt" @clickGift="giftProduct" />
              </div>
              <div
                class="box-p"
                v-else-if="
                  item.content.txt &&
                  (item.content.txt.indexOf('.png') !== -1 ||
                    item.content.txt.indexOf('.jpg') !== -1 ||
                    item.content.txt.indexOf('.jpeg') !== -1 ||
                    item.content.txt.indexOf('.svg') !== -1 ||
                    item.content.txt.indexOf('.bmp') !== -1 ||
                    item.content.txt.indexOf('.gif') !== -1)
                "
              >
                <img
                  class="user-img"
                  :src="item.content.txt"
                  alt="加载失败"
                  @click="showImage(item.content.txt)"
                />
              </div>
              <div
                class="box-p"
                v-else-if="item.content.txt && item.content.txt.indexOf('.mp4') !== -1"
              >
                <video
                  class="banen-video"
                  controls
                  disablePictureInPicture
                  controlslist="nodownload nofullscreen noremoteplayback noplaybackrate"
                  :src="item.content.txt"
                ></video>
              </div>
              <div class="box-p" v-else-if="item.content.url">
                <a class="box-p-a" :href="item.content.url" rel="noopener noreferrer">
                  {{ item.content.url }}
                </a>
              </div>
              <div class="box-p" v-else-if="item.content.cloud_storage_txt">
                <Extend
                  :cloudStorage="item.content.cloud_storage_txt"
                  :expiryTime="item.expiry_time"
                />
              </div>
              <div class="box-p" v-else-if="item.content.service_satisfaction_survey_txt">
                <EvaluateItem
                  :survey="item.content.service_satisfaction_survey_txt"
                  :msgId="msgId"
                />
              </div>
              <div
                class="box-p"
                v-else-if="item.content.txt && item.content.txt.indexOf('Codesn:') !== -1"
              >
                <Exchange :code_sn="item.content.txt.replace('Codesn:', '')" />
              </div>
              <div class="box-p new-box-d" v-else-if="item.content.type && item.content.type === 2">
                <ChangeDeviceItem @device-address="deviceAddress" />
              </div>
              <div
                class="box-p"
                style="word-break: normal; overflow-wrap: break-word"
                v-else
                v-html="item.content.txt"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer">
        <div class="evaluate" v-if="evaluateShow">
          <div class="evaluate-text" @click.stop="openView">
            <img class="evaluate-img" src="https://cache.gdxp.com/acce/assets/pj.png" alt="" />
            <span>{{ $t("home.evaluationService") }}</span>
          </div>
        </div>
        <div class="footer-title">
          <div class="album-img" id="album" @click.stop="openPhoto">
            <img class="album-img" src="https://cache.gdxp.com/acce/assets/album.png" alt="" />
          </div>
          <input
            class="footer-input"
            v-model.trim="textValue"
            @focus="inputFocus"
            type="text"
            ref="myInput"
            @keyup.enter.native="sendClick"
            placeholder="input message here..."
          />
          <div class="footer-rights">
            <img
              class="camera"
              @click.stop="openCamera"
              src="https://cache.gdxp.com/acce/assets/camera.png"
              alt=""
            />
            <img
              class="send"
              @click="sendClick"
              src="https://cache.gdxp.com/acce/assets/fs.png"
              alt=""
            />
          </div>

          <div class="catek">
            <input ref="myCamera" type="file" accept="camera/*" @change="cameraChage" />
          </div>
          <div class="catek">
            <input ref="myPhoto" type="file" accept="image/*" @change="photoChage" />
          </div>
        </div>
      </div>
      <!-- 底部tabbar -->
      <Tabbar @tabClick="receiveData" :chatList="chatList" v-if="useStore.tabbarStatus" />
    </div>
    <!-- 评价弹框 -->
    <Evaluate
      ref="evaluateHtml"
      :msgId="msgId"
      :service-uid="serviceUid"
      @send-msg="sendEvaluateMsg"
      v-if="evaluateShow"
    />
  </div>
  <!-- ios模块 -->
  <div class="container-ios" v-else @touchmove="touchStart" @touchend="touchendData">
    <div class="box ios-box">
      <div class="gift-flex" @click="giftProduct" v-if="config.msg_id && isRepair === '0'">
        <img class="gift-img" src="../assets/4.png" alt="" />
      </div>
      <div class="ios-tip">Service Hours：Monday to Friday，9:00 AM - 5:00 PM</div>
      <div v-if="useStore.tabbarStatus">
        <div
          class="newi-data"
          @click="cardData"
          :class="
            (chatList.length === 4 && sendStatus && isText) ||
            (chatList.length === 5 && sendStatus && isText)
              ? 'newi-fff'
              : chatList.length <= 3 && sendStatus && isText
              ? 'newi-ddd'
              : toolStatus && screen == 0 && chatList.length > 1
              ? 'newi-odd'
              : screen === 0 && chatList.length == 0 && toolStatus
              ? 'newi-dataa'
              : toolStatus && evaluateShow
              ? 'new-datax'
              : (screen == 1 || screen == 2 || screen == 3) && toolStatus
              ? 'newi-oaa'
              : screen == 1 || screen == 2 || screen == 3 || !evaluateShow
              ? 'new-dataa'
              : chatList.length > 1
              ? 'newi-off'
              : ''
          "
        >
          <div class="box-conter" v-for="(item, index) in chatList" :key="index">
            <div class="box-time" v-if="item.role == 1">
              {{ item.from_name || item.from }} ({{ item.ctime }})
            </div>
            <div class="box-times" v-else>{{ item.ctime }}</div>
            <div class="box-left" :class="item.role == 0 ? 'box-right' : ''">
              <div
                class="box-p new-box-p"
                v-if="
                  item.uid === 3 &&
                  item.show_button &&
                  item.content.txt &&
                  (item.content.txt.indexOf('.png') !== -1 ||
                    item.content.txt.indexOf('.jpg') !== -1 ||
                    item.content.txt.indexOf('.jpeg') !== -1 ||
                    item.content.txt.indexOf('.svg') !== -1 ||
                    item.content.txt.indexOf('.bmp') !== -1 ||
                    item.content.txt.indexOf('.gif') !== -1)
                "
              >
                <InviteItem :img-url="item.content.txt" @clickGift="giftProduct" />
              </div>
              <div
                class="box-p"
                v-else-if="
                  item.content.txt &&
                  (item.content.txt.indexOf('.png') !== -1 ||
                    item.content.txt.indexOf('.jpg') !== -1 ||
                    item.content.txt.indexOf('.jpeg') !== -1 ||
                    item.content.txt.indexOf('.svg') !== -1 ||
                    item.content.txt.indexOf('.bmp') !== -1 ||
                    item.content.txt.indexOf('.gif') !== -1)
                "
              >
                <img
                  class="user-img"
                  :src="item.content.txt"
                  alt="加载失败"
                  @click="showImage(item.content.txt)"
                />
              </div>
              <div
                class="box-p"
                v-else-if="item.content.txt && item.content.txt.indexOf('.mp4') !== -1"
              >
                <video
                  class="banen-video"
                  controls
                  disablePictureInPicture
                  controlslist="nodownload nofullscreen noremoteplayback noplaybackrate"
                  :src="item.content.txt"
                ></video>
              </div>
              <div class="box-p" v-else-if="item.content.url">
                <a class="box-p-a" :href="item.content.url" rel="noopener noreferrer">
                  {{ item.content.url }}
                </a>
              </div>
              <div class="box-p" v-else-if="item.content.cloud_storage_txt">
                <Extend
                  :cloudStorage="item.content.cloud_storage_txt"
                  :expiryTime="item.expiry_time"
                />
              </div>
              <div class="box-p" v-else-if="item.content.service_satisfaction_survey_txt">
                <EvaluateItem
                  :survey="item.content.service_satisfaction_survey_txt"
                  :msgId="msgId"
                />
              </div>
              <div
                class="box-p"
                v-else-if="item.content.txt && item.content.txt.indexOf('Codesn:') !== -1"
              >
                <Exchange :code_sn="item.content.txt.replace('Codesn:', '')" />
              </div>
              <div class="box-p new-box-d" v-else-if="item.content.type && item.content.type === 2">
                <ChangeDeviceItem @device-address="deviceAddress" />
              </div>
              <div
                class="box-p"
                style="word-break: normal; overflow-wrap: break-word"
                v-else
                v-html="item.content.txt"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <div
          class="newi-dataf"
          @click="cardData"
          :class="
            chatList.length > 3 && chatList.length < 6 && sendStatus && isText && evaluateShow
              ? 'newi-jjj'
              : chatList.length <= 3 && sendStatus && isText && evaluateShow
              ? 'newi-hhh'
              : chatList.length > 3 && chatList.length < 6 && sendStatus && isText
              ? 'newi-fff'
              : chatList.length <= 3 && sendStatus && isText
              ? 'newi-ddd'
              : toolStatus && evaluateShow
              ? 'newi-datafs'
              : toolStatus && !evaluateShow
              ? 'newi-datafss'
              : screen == 0 && chatList.length == 0 && toolStatus
              ? 'newi-datas'
              : (screen == 1 && chatList.length > 1 && toolStatus) ||
                (screen == 2 && chatList.length > 1 && toolStatus) ||
                (screen == 3 && chatList.length > 1 && toolStatus)
              ? 'newi-datas'
              : toolStatus && chatList.length == 0 && (screen == 1 || screen == 2 || screen == 3)
              ? 'newi-datas'
              : screen == 1 || screen == 2 || screen == 3 || !evaluateShow
              ? 'newi-datafa'
              : evaluateShow
              ? 'newi-footer'
              : ''
          "
        >
          <div class="box-conter" v-for="(item, index) in chatList" :key="index">
            <div class="box-time" v-if="item.role == 1">
              {{ item.from_name || item.from }} ({{ item.ctime }})
            </div>
            <div class="box-times" v-else>{{ item.ctime }}</div>
            <div class="box-left" :class="item.role == 0 ? 'box-right' : ''">
              <div
                class="box-p new-box-p"
                v-if="
                  item.uid === 3 &&
                  item.show_button &&
                  item.content.txt &&
                  (item.content.txt.indexOf('.png') !== -1 ||
                    item.content.txt.indexOf('.jpg') !== -1 ||
                    item.content.txt.indexOf('.jpeg') !== -1 ||
                    item.content.txt.indexOf('.svg') !== -1 ||
                    item.content.txt.indexOf('.bmp') !== -1 ||
                    item.content.txt.indexOf('.gif') !== -1)
                "
              >
                <InviteItem :img-url="item.content.txt" @clickGift="giftProduct" />
              </div>
              <div
                class="box-p"
                v-else-if="
                  item.content.txt &&
                  (item.content.txt.indexOf('.png') !== -1 ||
                    item.content.txt.indexOf('.jpg') !== -1 ||
                    item.content.txt.indexOf('.jpeg') !== -1 ||
                    item.content.txt.indexOf('.svg') !== -1 ||
                    item.content.txt.indexOf('.bmp') !== -1 ||
                    item.content.txt.indexOf('.gif') !== -1)
                "
              >
                <img
                  class="user-img"
                  :src="item.content.txt"
                  alt="加载失败"
                  @click="showImage(item.content.txt)"
                />
              </div>
              <div
                class="box-p"
                v-else-if="item.content.txt && item.content.txt.indexOf('.mp4') !== -1"
              >
                <video
                  class="banen-video"
                  controls
                  disablePictureInPicture
                  controlslist="nodownload nofullscreen noremoteplayback noplaybackrate"
                  :src="item.content.txt"
                ></video>
              </div>
              <div
                class="box-p"
                v-else-if="item.content.txt && item.content.txt.indexOf('Codesn:') !== -1"
              >
                <Exchange :code_sn="item.content.txt.replace('Codesn:', '')" />
              </div>
              <div class="box-p" v-else-if="item.content.url">
                <a class="box-p-a" :href="item.content.url" rel="noopener noreferrer">
                  {{ item.content.url }}
                </a>
              </div>
              <div class="box-p" v-else-if="item.content.cloud_storage_txt">
                <Extend
                  :cloudStorage="item.content.cloud_storage_txt"
                  :expiryTime="item.expiry_time"
                />
              </div>
              <div class="box-p" v-else-if="item.content.service_satisfaction_survey_txt">
                <EvaluateItem
                  :survey="item.content.service_satisfaction_survey_txt"
                  :msgId="msgId"
                />
              </div>
              <div class="box-p new-box-d" v-else-if="item.content.type && item.content.type === 2">
                <ChangeDeviceItem @device-address="deviceAddress" />
              </div>
              <div
                class="box-p"
                style="word-break: normal; overflow-wrap: break-word"
                v-else
                v-html="item.content.txt"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer" ref="iosFooter">
        <div class="evaluate" v-if="evaluateShow">
          <div class="evaluate-text" @click.stop="openView">
            <img class="evaluate-img" src="https://cache.gdxp.com/acce/assets/pj.png" alt="" />
            <span>{{ $t("home.evaluationService") }}</span>
          </div>
        </div>
        <div class="footer-title">
          <div class="album-img" id="album" @click.stop="clickAlbum" v-if="app_version == 1">
            <img class="album-img" src="https://cache.gdxp.com/acce/assets/album.png" alt="" />
          </div>
          <form
            :style="{ width: app_version == 1 ? '69%' : '73%' }"
            action="javascript:return true;"
          >
            <input
              class="footer-inputs"
              v-model.trim="textValue"
              @focus="saveFocusin"
              @blur="saveFocusout"
              type="search"
              ref="myInput"
              @keyup.enter.native="sendClick"
              placeholder="input message here..."
            />
          </form>
          <div class="footer-rights" v-if="app_version == 1">
            <div class="camera" id="camera" @click.stop="clickCamera">
              <img class="camera" src="https://cache.gdxp.com/acce/assets/camera.png" alt="" />
            </div>
            <div class="send-box" id="send" @click.stop="sendClick">
              <img class="send" src="https://cache.gdxp.com/acce/assets/send.png" alt="" />
            </div>
          </div>
          <div class="footer-right" v-else>
            <img
              class="camera"
              @click="toolStatus = !toolStatus"
              src="https://cache.gdxp.com/acce/assets/xj.png"
              alt=""
            />
            <div class="send" id="send" @click.stop="sendClick">
              <img class="send" src="https://cache.gdxp.com/acce/assets/fs.png" alt="" />
            </div>
          </div>
        </div>
        <div class="footer-box" v-show="toolStatus">
          <div class="footer-item" @click.stop="openCamera">
            <div class="item-top">
              <img class="item-top-img" src="https://cache.gdxp.com/acce/assets/1.png" alt="" />
            </div>
            <div class="item-text">{{ $t("home.camera") }}</div>
            <div class="catek">
              <input ref="myCamera" type="file" accept="image/*" @change="cameraChage" />
            </div>
          </div>
          <div class="footer-item" @click.stop="openPhoto">
            <div class="item-top">
              <img class="item-top-img" src="https://cache.gdxp.com/acce/assets/2.png" alt="" />
            </div>
            <div class="item-text">{{ $t("home.photoAlbum") }}</div>
            <div class="catek">
              <input ref="myPhoto" type="file" accept="image/*" @change="photoChage" />
            </div>
          </div>
        </div>
      </div>
      <!-- 底部tabbar -->
      <Tabbar @tabClick="receiveData" :chatList="chatList" v-if="useStore.tabbarStatus" />
    </div>
    <!-- 评价弹框 -->
    <Evaluate
      ref="evaluateHtml"
      :msgId="msgId"
      :service-uid="serviceUid"
      @send-msg="sendEvaluateMsg"
      v-if="evaluateShow"
      @html-close="eventClose"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, toRefs, reactive, onUnmounted, onUpdated } from "vue"
import {
  randomNum,
  base64ToFile,
  readImg,
  compressImg,
  compareVersions,
  getTypeFromUrl,
} from "@/utils"
import { showNotify, showToast, showImagePreview } from "vant"
import Evaluate from "@/components/Evaluate.vue"
import Extend from "@/components/Extend.vue"
import Tabbar from "@/components/Tabbar.vue"
import EvaluateItem from "@/components/EvaluateItem.vue"
import Exchange from "@/components/Exchange.vue"
import InviteItem from "@/components/InviteItem.vue"
import ChangeDeviceItem from "@/components/ChangeDeviceItem.vue"
import { getChat, saveChat, readMsg, get_auto_customer_star_info } from "@/api"
import type { userData, sendChatData } from "@/api/base"
import { uploadToOSS } from "@/oss/index"
import config from "@/config"
import { createWebSocket, sendSock, closeSock } from "@/utils/socket"
import moment from "moment"
import { i18n } from "@/lang"
import { useHomeStore } from "@/stores/home"
import FastClick from "fastclick"
import { useRouter } from "vue-router"
import { md5 } from "js-md5"

const useStore = useHomeStore()
const router = useRouter()
const { t } = i18n.global as any
// 动态接口数据
const state = reactive({
  chatList: [] as userData[], //因为后端传来的是List数组，而不是一条数据，这种就是定义成对象数组形式
  sendChat: {
    session_id: config.sessionId,
    data: "",
  } as sendChatData,
  pagesNum: 1,
  msgList: [] as any[], //未读消息数组
})
const { chatList } = toRefs(state)
const toolStatus = ref(false)
const textValue = ref("")
const msgId = ref(0) //聊天ID
const serviceUid = ref(0) //聊天uid
const screen = ref(useStore.screen) //消息通道 0客服消息  1测评消息  2推广消息
const evaluateStatus = ref(false) //是否已评价
const evaluateShow = ref(false) //是否出现评价
const myCamera = ref<HTMLElement | any>(null)
const myPhoto = ref<HTMLElement | any>(null)
const sendStatus = ref(false) //ios发送状态
const showBottom = ref(false) //是否弹出了评价
const iosFooter = ref<HTMLElement | any>(null) //ios底部键盘弹出特殊处理
const app_version = ref(compareVersions(config.app_version))
const isText = ref(true) //是否只有文本
const uesrname = ref("") //判断邀评用户是否填写过地址

// 打开相机
const openCamera = () => {
  if (useStore.platform === "android") {
    cardData()
    setTimeout(() => {
      myCamera.value?.click()
    }, 50)
  } else {
    myCamera.value?.click()
  }
}

// 拍照后的回调
const cameraChage = () => {
  const file = myCamera.value.files[0]
  changeImage(file)
}

// 打开相册
const openPhoto = () => {
  if (useStore.platform === "android") {
    cardData()
    setTimeout(() => {
      myPhoto.value?.click()
    }, 50)
  } else {
    myPhoto.value?.click()
  }
}

// 打开相册选择照片后的回调
const photoChage = async () => {
  const file = myPhoto.value.files[0]
  changeImage(file)
}

// 转义图片格式跟大小
const changeImage = async (file: any) => {
  const img = await readImg(file) // 读取上传的照片并转为base64
  let image = await compressImg(img, file.type, 1000, 1000) //防止上传的照片过大，进行压缩处理
  if (image) {
    // 开始上传
    upload(image)
  }
}

// 上传图片
async function upload(userFile: object) {
  let file = base64ToFile(
    userFile,
    `${moment().format("YYYY/MM/DD")}/${new Date().getTime()}/${randomNum()}.png`
  )
  // 直接上传到阿里云oss
  const res = await uploadToOSS(
    `${moment().format("YYYY/MM/DD")}/${new Date().getTime()}/${randomNum()}.png`,
    file
  )
  if (res) {
    sendImage(`img[${res}]`)
  }
}

//发送消息跟图片的参数
const sendParams = (text: any, type: any, data = "") => {
  const obj = {
    question_id: config.sessionId,
    role: 0,
    device_sn: config.device_sn,
    ctime: moment().format("YYYY-MM-DD HH:mm:ss"),
    cmd: "screen_say",
    content: type == 1 ? { service_satisfaction_survey_txt: text } : { txt: text },
    client: "h5",
    app_version: "1.0.0",
    from: config.from_name,
    screen: data ? data : screen.value,
  }
  state.sendChat.data = JSON.stringify(obj)
}

// 文本输入框聚焦
const myInput = ref<HTMLElement | any>(null)
// 输入框聚焦
const inputFocus = () => {
  toolStatus.value = false
  if (state.chatList.length > 3) {
    // textValue.value = 'input message here...'
    setTimeout(() => {
      if (config.scrollStatus) {
        window.scrollTo(0, document.body.scrollHeight)
      }
    }, 180)
  }
}

// ios端恢复界面空白
function changeBlur() {
  setTimeout(() => {
    const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0
    window.scrollTo(0, Math.max(scrollHeight - 1, 0))
  }, 88)
}

// 发送文本消息
const sendClick = () => {
  if (textValue.value === "") {
    showNotify({ type: "warning", message: t("home.tipMsg") })
  } else {
    myInput.value.focus()
    // 添加参数
    sendParams(textValue.value, 0)
    saveChatData()
    textValue.value = ""
  }
}

// 发送评价消息
function sendEvaluateMsg(data: any) {
  // 添加参数
  sendParams(data, 1)
  saveChatData()
}

// 发送图片消息
function sendImage(img: any) {
  // 添加参数
  sendParams(img, 0)
  saveChatData()
}

// websocket发送消息
function saveChatData() {
  config.scrollStatus = true
  //判断websocket是否断开
  if (config.websockStatus == 1) {
    const params = JSON.stringify(
      Object.assign(JSON.parse(state.sendChat.data), { cmd: "screen_say" })
    )
    sendSock(params)
  } else if (config.websockStatus == 0) {
    getHttpSaveChat()
  }
}

// 接口发送消息
function getHttpSaveChat() {
  saveChat(state.sendChat).then((res: any) => {
    if (res.resultCode == 0) {
      textValue.value = ""
      fetch()
    }
  })
}

// 连接websocket模块
const global_callback = (msg: any) => {
  if (msg) {
    if ((msg.resultCode === 0 && msg.cmd === "screen_say") || msg.cmd === "message") {
      config.websockStatus = 1
      if (msg.content.txt && msg.content.txt.indexOf("img[") !== -1) {
        isText.value = false
        msg.content.txt = msg.content.txt.match(/\[(.*?)\]/)[1]
        msg.content.img = msg.content.txt
      }
      if (
        msg.content.txt &&
        (msg.content.txt.indexOf("\r") !== -1 || msg.content.txt.indexOf("\n") !== -1)
      ) {
        msg.content.txt = msg.content.txt.replace(/\r\n/g, "<br/>")
        isText.value = false
      }
      if (msg.ctime) {
        msg.ctime = moment(msg.ctime).format("YYYY-MM-DD HH:mm:ss")
      }
      if (msg.content.service_satisfaction_survey_txt) {
        if (!localStorage.getItem("evaluateTime") && msg.role == 1) {
          localStorage.setItem("evaluateTime", moment().format("YYYY-MM-DD HH:mm:ss"))
          localStorage.setItem("msgId", msg.content.msg_id)
        }
        isText.value = false
        evaluateStatus.value = true
        msgId.value = msg.content.msg_id
      }
      if (msg.msg_id) {
        state.msgList = []
        state.msgList.push(msg.msg_id)
        webParams()
      }
      state.chatList.push(msg)
      // 针对ios聊天记录小于6的界面特殊处理
      if (useStore.platform === "ios") {
        if (state.chatList.length <= 6 && isText.value) {
          if (state.chatList.length == 4) {
            setTimeout(() => {
              window.scrollTo(0, 270)
            }, 150)
          } else if (state.chatList.length === 6) {
            setTimeout(() => {
              window.scrollTo(0, document.body.scrollHeight)
            }, 150)
          }
        }
      }
    } else if (msg == 0) {
      //断开连接走接口
      fetch()
    } else if (msg.resultCode == 0 && msg.cmd === "heartbeat") {
      //正在连接心跳
      config.websockStatus = 1
    }
  }
}

// 打开评价弹框
const evaluateHtml = ref<HTMLElement | any>(null)
const openView = () => {
  if (evaluateStatus.value) {
    showToast(t("home.already"))
    return
  }
  showBottom.value = true
  setTimeout(() => {
    evaluateHtml.value?.openEvaluate()
  }, 100)
}

// 领取邀评礼物   0 送美金  1 送设备
const giftType = ref("")
const giftTime = ref(0)
const giftProduct = () => {
  if (giftType.value === "0") {
    router.push("sendUsd")
  } else {
    if (uesrname.value) {
      config.showBottom.value = 2
      router.push("inviteDetails")
    } else {
      config.showBottom.value = 1
      router.push("invite")
    }
  }
}

// 返修流程
const isRepair = ref("0")
const deviceAddress = () => {
  if (isRepair.value === "1") {
    if (config.showBottom.value === 2) {
      router.push("changeDeviceDetails")
    } else {
      router.push("changeDeviceAddress")
    }
  }
}

// 分页是否还有数据
const noData = ref(true)
// 获取客服聊天记录
function fetch() {
  getChat(config.sessionId, config.device_sn, screen.value, state.pagesNum, config.src).then(
    (res: any) => {
      if (res.resultCode == 0) {
        if (
          res.content.service_satisfaction_survey_enable &&
          res.content.service_satisfaction_survey_enable === 1
        ) {
          evaluateShow.value = true
        }
        if (res.content.list.length) {
          let dataItem = { msg_id: 0, uid: 0 }
          res.content.list.forEach((item: any) => {
            if (item.role == 1 && state.pagesNum === 1 && item.uid !== 3) {
              dataItem = item
            }
            if (item.is_read == 0) {
              state.msgList.push(item.msg_id)
            }
            if (item.uid === 3 && item.show_button === 1) {
              config.msg_id = item.msg_id
            }
            if (item.content.type && item.content.type === 2) {
              config.msg_id = item.msg_id
              isRepair.value = "1"
            }
            if (item.content.service_satisfaction_survey_txt) {
              if (localStorage.getItem("msgId") && localStorage.getItem("msgId") != item.msg_id) {
                localStorage.setItem("evaluateTime", "")
                localStorage.setItem("msgId", "")
              }
              if (
                !localStorage.getItem("evaluateTime") &&
                localStorage.getItem("msgId") != "item.msg_id" &&
                item.role == 1
              ) {
                localStorage.setItem("evaluateTime", moment().format("YYYY-MM-DD HH:mm:ss"))
                localStorage.setItem("msgId", item.msg_id)
              }
              evaluateStatus.value = true
              msgId.value = item.msg_id
              serviceUid.value = item.service_uid
              isText.value = false
            }
            if (item.ctime) {
              item.ctime = moment(item.ctime).format("YYYY-MM-DD HH:mm:ss")
            }
            if (item.content.cloud_storage_txt && item.content.cloud_storage_txt.price) {
              isText.value = false
              item.content.cloud_storage_txt.price1 = item.content.cloud_storage_txt.price.replace(
                /\|(.*)/,
                ""
              )
              if (item.content.cloud_storage_txt.service_days === 365) {
                item.content.cloud_storage_txt.price2 = (
                  Number(item.content.cloud_storage_txt.price.replace(/.*\|/, "")) / 360
                ).toFixed(2)
              } else {
                item.content.cloud_storage_txt.price2 = (
                  Number(item.content.cloud_storage_txt.price.replace(/.*\|/, "")) /
                  item.content.cloud_storage_txt.service_days
                ).toFixed(2)
              }
            }
            if (item.content.txt && item.content.txt.indexOf("\n") !== -1) {
              isText.value = false
            }
            if (
              item.content.txt &&
              (item.content.txt.indexOf("\r") !== -1 || item.content.txt.indexOf("\n") !== -1)
            ) {
              item.content.txt = item.content.txt.replace(/\r\n/g, "<br/>")
              isText.value = false
            }
            if (item.content.txt && item.content.txt.indexOf("img[") !== -1) {
              item.content.txt = item.content.txt.match(/\[(.*?)\]/)[1]
              item.content.img = item.content.txt
              isText.value = false
              if (item.uid === 3 && item.show_button === 1) {
                giftType.value = getTypeFromUrl(item.content.img)
              }
            }
            if (item.content.txt && item.content.txt.indexOf("url[") !== -1) {
              item.content.url = item.content.txt.match(/\[(.*?)\]/)[1]
              isText.value = false
            }
            if (item.content.txt && item.content.txt.indexOf("embed[") !== -1) {
              item.content.txt = item.content.txt.match(/\[(.*?)\]/)[1]
              isText.value = false
            }
          })
          if (state.pagesNum === 1) {
            if (evaluateStatus.value === false) {
              msgId.value = dataItem.uid
            }
            serviceUid.value = dataItem.uid
          }
          if (state.pagesNum === 1) {
            state.chatList = res.content.list
          } else {
            state.chatList = [...res.content.list, ...state.chatList]
          }
          if (res.content.list.length < 30) {
            noData.value = false
          }
          // 未读消息传给后端
          if (state.msgList.length) {
            clearMsg()
          }
        } else {
          noData.value = false
        }
      } else {
        noData.value = false
      }
      getAddress()
    }
  )
}

//websocket 清除未读消息
function clearMsg() {
  // websocket是否断开
  setTimeout(() => {
    if (config.websockStatus == 1) {
      webParams()
    } else {
      // 接口清除
      readMsgData()
    }
  }, 1000)
}

//websocket清除已读参数
const webParams = () => {
  const params = JSON.stringify({
    cmd: "screen_read_msg",
    device_sn: config.device_sn,
    screen: screen.value,
    msg_id: state.msgList,
  })
  sendSock(params)
}

// 清除未读消息
function readMsgData() {
  const msgIdList: any = []
  state.chatList.forEach(item => {
    msgIdList.push(item.msg_id)
  })
  const params = {
    screen: screen.value,
    msg_id: msgIdList,
    cmd: "screen_read_msg",
    device_sn: config.device_sn,
  }
  readMsg({ session_id: config.sessionId, data: JSON.stringify(params) })
}

// 接受tabbar传值
const receiveData = (data: number) => {
  screen.value = data
  fetch()
}

// 取消聚焦
const cardData = () => {
  myInput.value.blur()
  if (useStore.platform === "ios") {
    changeBlur()
  }
}

// 滚动时收回键盘
function fn_isShowKeyboard() {
  // 加载下一页
  if ((document.documentElement.scrollTop || document.body.scrollTop) === 0 && noData.value) {
    state.pagesNum++
    config.scrollStatus = false
    fetch()
  }
}

// 滚动到底部
function scroolBody() {
  if (state.chatList.length < 6 && isText.value) return
  if (config.scrollStatus) {
    window.scrollTo(0, document.body.scrollHeight)
  }
}

//图片预览
const showImage = (img: any) => {
  showImagePreview([img])
}

// 调用安卓的方法返回
const goBack = () => {
  closeSock()
  document.removeEventListener("scroll", debounce(fn_isShowKeyboard, 20))
  const windows: any = window
  windows.android.finishActivity()
}

// 安卓原生返回事件
const clickBack = () => {
  closeSock()
  document.removeEventListener("scroll", debounce(fn_isShowKeyboard, 20))
}

// 键盘弹出
function saveFocusin() {
  sendStatus.value = true
  if (showBottom.value) return
  iosFooter.value.style = "padding-bottom:10px"
  //软键盘弹出的事件处理
  if (toolStatus.value) {
    toolStatus.value = false
  }
  if (state.chatList.length <= 5 && isText.value) {
    if (state.chatList.length <= 3) {
      setTimeout(() => {
        window.scrollTo(0, 40)
      }, 100)
    } else {
      setTimeout(() => {
        window.scrollTo(0, 270)
      }, 100)
    }
  }
}

// 键盘收下
function saveFocusout() {
  sendStatus.value = false
  iosFooter.value.style =
    "padding-bottom: constant(safe-area-inset-bottom);padding-bottom: env(safe-area-inset-bottom);"
}

// 节流函数
function debounce(fn: any, delay = 200) {
  let timer: any = null
  return function (...args: any[]) {
    if (timer != null) {
      clearTimeout(timer)
      timer = null
    }
    timer = setTimeout(() => {
      fn.call(fn, ...args)
    }, delay)
  }
}

const touchStart = () => {
  if (sendStatus.value) {
    myInput.value.blur()
    changeBlur()
    if (state.chatList.length > 7) {
      window.scrollTo(0, document.body.scrollHeight)
    }
  }
}

// 处理点击屏幕会滚动
let iLastTouchTime: any = null
const touchendData = (e: any) => {
  let iNowTime = new Date().getTime()
  iLastTouchTime = iLastTouchTime || iNowTime + 1
  let delta = iNowTime - iLastTouchTime
  if (delta < 500 && delta > 0) {
    e.preventDefault()
    return false
  }
  iLastTouchTime = iNowTime
}

// 评价弹框关闭
function eventClose() {
  showBottom.value = false
}

// 调用Ios原生拍照
const clickCamera = () => {
  const windows: any = window
  windows.webkit.messageHandlers.click_camera.postMessage("相机")
}

// 调用Ios相册
const clickAlbum = () => {
  config.albumType = 1
  const windows: any = window
  windows.webkit.messageHandlers.click_album.postMessage("相册")
}

// ios图片上传后返回
function cameraChages(file: any) {
  if (file) {
    if (config.albumType === 2) {
      config.albumImg.value = file.match(/\[(.*?)\]/)[1]
    } else {
      sendImage(file)
    }
  } else {
    showToast("图片地址是空的!")
  }
}

const getAddress = () => {
  get_auto_customer_star_info(
    config.sessionId,
    config.device_sn,
    md5(`${config.sessionId}/${config.device_sn + "EKDB_ni&Hb&Zt&zz^7qn9"}`),
    isRepair.value
  ).then((res: any) => {
    if (res.resultCode === 0) {
      if (res.content.sp_status === -1 || res.content.sp_status === 0) {
        config.showBottom.value = 2
      }
      if (res.content.sp_status === -2) {
        config.showBottom.value = 1
      }
      if (res.content.name) {
        uesrname.value = String(res.content.name)
      }
    }
  })
}

onMounted(() => {
  if (Number(localStorage.getItem("giftTime"))) {
    giftTime.value = Number(localStorage.getItem("giftTime"))
  } else {
    giftTime.value = 24 * 60 * 60 * 1000
  }

  //获取聊天记录
  fetch()
  createWebSocket(global_callback)

  // 监听滚动加载下一页
  document.addEventListener("scroll", debounce(fn_isShowKeyboard, 20))

  if (useStore.platform === "ios") {
    // 注册事件给ios调用
    window.cameraChages = cameraChages

    // ios点击延迟问题
    if (document.getElementById("album")) {
      FastClick(document.getElementById("album"))
    }
    if (document.getElementById("camera")) {
      FastClick(document.getElementById("camera"))
    }
    FastClick(document.getElementById("send"))
  }

  // 注册事件给app调用关闭
  window.clickBack = clickBack
})

onUpdated(() => {
  scroolBody()
})

onUnmounted(() => {
  closeSock()
})
</script>
<style lang="less" scoped>
.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.container-ios {
  background-color: #f8f8f8;
  min-height: 100vh;
  position: relative;
}

.container-top {
  background-color: #f5f5f5;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #333;
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  top: 0;
  z-index: 9;

  .top-left {
    position: absolute;
    left: 20px;
    top: 11px;

    .top-imgs {
      width: 44px;
      height: 40px;
    }
  }

  .top-right {
    font-weight: bold;
  }
}

.box {
  position: relative;
  height: 100%;

  .new-data {
    min-height: calc(100vh - 280px);
    padding: 0px 15px 0px 15px;
  }

  // .new-datas{
  //     height: calc(100vh - 370px);
  // }
  // .new-dataa{
  //     height: calc(100vh - 250px);
  // }
  // .new-top{
  //     height: calc(100vh - 250px);
  // }
  // .new-datasa{
  //     height: calc(100vh - 390px);
  // }
  // .new-datasaa{
  //     height: calc(100vh - 340px);
  // }
  // .new-datab{
  //     height: calc(100vh - 255px);
  // }
  // 安卓没有底部的样式
  .new-dataf {
    min-height: calc(100vh - 195px);
    padding: 0px 15px 0px 15px;
  }

  // .new-datafs{
  //     height: calc(100vh - 280px);
  // }

  // 安卓没有评价
  .new-datafa {
    min-height: calc(100vh - 165px);
  }

  // Ios没有底部的样式
  .newi-dataf {
    min-height: calc(100vh - 155px);
    padding: 0px 15px 0px 15px;
  }

  // .newi-datafs{
  //     min-height: calc(100vh - 245px);
  // }
  // .newi-datafss{
  //     min-height: calc(100vh - 215px);
  // }
  .newi-datafa {
    min-height: calc(100vh - 105px);
  }

  .newi-footer {
    min-height: calc(100vh - 135px);
  }

  // .newi-datas{
  //     min-height: calc(100vh - 280px);
  // }
  // ios有底部的样式
  .newi-data {
    min-height: calc(100vh - 195px);
    padding: 0px 15px 0px 15px;
  }

  .newi-dataa {
    min-height: calc(100vh - 315px);
  }

  // .newi-off{
  //     min-height: calc(100vh - 220px);
  // }
  // .newi-odd{
  //     min-height: calc(100vh - 310px);
  // }
  // .newi-oaa{
  //     min-height: calc(100vh - 315px);
  // }
  // .newi-occ{
  //     min-height: calc(100vh - 75px);
  // }
  // .new-datax{
  //     min-height: calc(100vh - 286px);
  // }
  // .new-dataxa{
  //     min-height: calc(100vh - 250px);
  // }
  // 小于一屏键盘弹起
  .newi-ddd {
    min-height: 38vh;
  }

  .newi-fff {
    min-height: 70vh;
  }

  // 有评价
  .newi-hhh {
    min-height: 30vh;
  }

  .newi-jjj {
    min-height: 66vh;
  }
  .gift-flex {
    position: fixed;
    bottom: 13%;
    left: 5%;
    z-index: 999;
    animation: pulse 2s infinite;
  }
  .android-gift-flex {
    position: fixed;
    bottom: 13%;
    left: 5%;
    z-index: 999;
    animation: pulse 2s infinite;
  }
  .gift-img {
    width: 36px;
    height: 36px;
  }
}

.tip {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  color: #fff;
  background-color: #555;
  font-weight: 400;
  position: sticky;
  position: -webkit-sticky;
  top: 60px;
  left: 0px;
  width: 100%;
  z-index: 9;
}

.ios-tip {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  color: #fff;
  background-color: #555;
  font-weight: 400;
  position: -webkit-sticky;
  position: sticky;
  top: 0px;
  left: 0px;
  width: 100%;
  z-index: 9;
}

.box-conter {
  .box-time {
    font-size: 14px;
    color: #999;
    height: 40px;
    display: flex;
    align-items: center;
  }

  .box-times {
    font-size: 14px;
    color: #999;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 40px;
  }

  .box-left {
    display: flex;

    .box-p {
      display: inline-block;
      font-size: 14px;
      color: #333;
      padding: 10px;
      background-color: #99ccff;
      border-radius: 10px;
      word-break: break-all;
      min-height: 14px;

      .box-p-a {
        display: block;
      }

      .show-btn {
        min-width: 150px;
        display: flex;
        justify-content: space-around;
        padding-top: 10px;
      }
    }
  }

  .box-right {
    display: flex;
    justify-content: flex-end;

    .box-p {
      display: inline-block;
      font-size: 14px;
      color: #333;
      padding: 10px;
      background-color: #fff;
      border-radius: 10px;
      min-height: 14px;
    }
  }

  .user-img {
    max-width: 100%;
    min-width: 150px;
    height: 200px;
    object-fit: contain;
  }

  .banen-video {
    width: 200px;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;

    .play-img {
      width: 64px;
      height: 64px;
    }
  }

  //音量按钮
  video::-webkit-media-controls-mute-button {
    display: none;
  }
}

.footer {
  padding: 15px 15px 0px 15px;
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  bottom: 0;
  width: 92%;
  background-color: #f8f8f8;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.evaluate {
  margin-bottom: 5px;

  .evaluate-text {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 15px;
    color: #333;
    font-size: 14px;
    background-color: #fff;
  }

  .evaluate-img {
    width: 15px;
    height: 15px;
    margin-right: 5px;
    vertical-align: text-top;
  }
}

.evaluates {
  min-height: 25px;
}

.footer-title {
  height: 50px;
  display: flex;
  align-items: center;

  .album-img {
    width: 24px;
    height: 22px;
    margin-right: 10px;
  }

  .footer-input {
    width: 233px;
    height: 40px;
    border-radius: 25px;
    padding-left: 12px;
    border: 1px solid #fff;
    outline: none;
    font-size: 14px;
    color: #333;
    -webkit-appearance: none;
    line-height: 40px;
  }

  .footer-inputs {
    background-color: #fff;
    width: 100%;
    height: 40px;
    border-radius: 25px;
    padding-left: 12px;
    border: 1px solid #fff;
    outline: none;
    font-size: 14px;
    color: #333;
    -webkit-appearance: none;
  }

  .footer-right {
    width: 25%;
    display: flex;
    margin-left: 2%;
    align-items: center;
    justify-content: space-around;

    .camera {
      width: 27px;
      height: 25px;
    }

    .send {
      width: 40px;
      height: 42px;
    }
  }

  .footer-rights {
    width: 20%;
    display: flex;
    margin-left: 2%;
    align-items: center;
    justify-content: space-between;

    .camera {
      width: 25.5px;
      height: 23px;
    }

    .send-box {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .send {
      width: 30px;
      height: 30px;
    }
  }
}

.footer-box {
  display: flex;
  height: 90px;
  align-items: center;
  justify-content: space-around;

  .footer-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .item-top {
    background-color: #fff;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;

    .item-top-img {
      width: 69px;
      height: 49px;
    }
  }

  .item-text {
    font-size: 14px;
    color: #333;
    font-weight: bold;
  }
}

.catek {
  display: none;
}

.new-box-p {
  padding: 20px 10px !important;
  position: relative;
  background: linear-gradient(135deg, #8a2be2, #ff69b4) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
.new-box-d {
  padding: 20px 10px !important;
  position: relative;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
</style>
