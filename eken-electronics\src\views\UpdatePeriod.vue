<template>
  <div class="update-period">
    <h2>Product Security Update Period</h2>
    <main>
      <div class="tab-container">
        <div v-for="item in [{ label: 'All Products' }, { label: 'Smartphone Products' }, { label: 'IoT Products' }]"
          :key="item.label" class="tab-item">{{ item.label }}</div>
      </div>
      <div class="text-container">
        <p class="text">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Deserunt repudiandae ea qui omnis et
          dolor dolores laudantium sunt delectus temporibus quisquam ad eius, repellat quam quia officia vel nam
          perferendis!
        </p>
        <p class="text">Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quos neque molestias fuga consequatur
          sint suscipit, ullam laborum voluptate ipsa illum distinctio aut harum enim! Consequuntur fugiat qui aliquid
          ducimus eos?</p>

      </div>

      <ul>
        <div class="header">
          <span>Product Name</span>
          <span>End Date of Period</span>
        </div>
        <li v-for="item in 5"><span>Enco Air4 Pro</span><span>19-Jun-2025</span></li>
      </ul>

      <h6>Disclaimer:</h6>
      <p class="disclaimer-text">
        1.Lorem ipsum dolor sit amet consectetur adipisicing elit. Sapiente ducimus accusamus eveniet similique. Aut,
        repellat id modi a veritatis non enim atque? Eos cumque culpa consequatur, est molestias ad. Itaque.
      </p>
      <p class="disclaimer-text">
        2.Lorem ipsum dolor sit amet consectetur adipisicing elit. Ipsam dolore optio animi nobis veniam consectetur nam
        obcaecati, quis exercitationem fugiat eveniet temporibus, inventore incidunt id, recusandae sequi expedita
        molestiae odio?
      </p>
      <p class="disclaimer-text">
        3.Lorem ipsum dolor sit amet, consectetur adipisicing elit. Unde odit laboriosam, labore libero saepe assumenda
        est consectetur fugit voluptas vitae perspiciatis numquam impedit, recusandae sed itaque? Eos voluptatibus at
        voluptatum.
      </p>
    </main>

  </div>
  <footer>
    <div class="svg-container">
      <svg t="1733728103688" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
        p-id="1459" width="30" height="30">
        <path
          d="M1024 512c0-282.763636-229.236364-512-512-512C229.236364 0 0 229.236364 0 512s229.236364 512 512 512C794.763636 1024 1024 794.763636 1024 512zM374.504727 512 374.504727 414.021818l60.043636 0L434.548364 354.769455c0-79.918545 23.877818-137.495273 111.383273-137.495273l104.075636 0 0 97.745455-73.262545 0c-36.724364 0-45.056 24.389818-45.056 49.943273l0 49.058909 112.919273 0L629.201455 512l-97.512727 0 0 295.517091L434.548364 807.517091 434.548364 512 374.504727 512z"
          p-id="1460" fill="#8a8a8a"></path>
      </svg>
      <svg t="1733728221247" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
        p-id="7035" width="30" height="30">
        <path d="M323.52 298.24l327.04 427.52h49.92L377.28 298.24h-53.76z" p-id="7036" fill="#8a8a8a"></path>
        <path
          d="M512 0a512 512 0 1 0 512 512A512 512 0 0 0 512 0z m113.6 779.84l-142.72-186.24L320 779.84H229.44l211.2-241.28-224-291.52h186.56l128 170.24 149.12-170.24h90.24L576 472.64l232.32 307.2z"
          p-id="7037" fill="#8a8a8a"></path>
      </svg>
      <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 20 20">
        <path fill="#8a8a8a"
          d="M11.603 9.833L9.357 8.785C9.161 8.694 9 8.796 9 9.013v1.974c0 .217.161.319.357.228l2.245-1.048c.197-.092.197-.242.001-.334M10 .4C4.698.4.4 4.698.4 10s4.298 9.6 9.6 9.6s9.6-4.298 9.6-9.6S15.302.4 10 .4m0 13.5c-4.914 0-5-.443-5-3.9s.086-3.9 5-3.9s5 .443 5 3.9s-.086 3.9-5 3.9" />
      </svg>
      <svg t="1733728601327" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
        p-id="18945" width="30" height="30">
        <path d="M412.8 512a99.2 99.2 0 1 0 198.4 0 99.2 99.2 0 1 0-198.4 0Z" fill="#8a8a8a" p-id="18946"></path>
        <path
          d="M627.2 412.8h70.4c6.4 0 12.8-6.4 12.8-12.8v-70.4c0-6.4-6.4-12.8-12.8-12.8h-70.4c-6.4 0-12.8 6.4-12.8 12.8v70.4c-3.2 6.4 3.2 12.8 12.8 12.8z"
          fill="#8a8a8a" p-id="18947"></path>
        <path
          d="M512 0C230.4 0 0 230.4 0 512s230.4 512 512 512 512-230.4 512-512S793.6 0 512 0z m256 707.2c0 32-28.8 60.8-60.8 60.8H316.8c-32 0-60.8-25.6-60.8-60.8V316.8C256 281.6 284.8 256 316.8 256h390.4c32 0 60.8 25.6 60.8 60.8v390.4z"
          fill="#8a8a8a" p-id="18948"></path>
        <path
          d="M668.8 512c0 86.4-70.4 156.8-156.8 156.8s-156.8-70.4-156.8-156.8c0-12.8 3.2-28.8 6.4-41.6H313.6v224c0 6.4 6.4 12.8 12.8 12.8h368c6.4 0 12.8-6.4 12.8-12.8v-224h-48c6.4 12.8 9.6 28.8 9.6 41.6z"
          fill="#8a8a8a" p-id="18949"></path>
      </svg>
    </div>
    <div class="links-container">
      <ul>
        <li>Privacy</li>
        <li>Privacy</li>
        <li>Privacy</li>
        <li>Privacy</li>
        <li>Privacy</li>
        <li>Privacy</li>
        <li>Privacy</li>
      </ul>
    </div>
    <div class="copyright">
      Copyright © 2004-2024 OPPO. All rights reserved.
    </div>
  </footer>
</template>

<style lang="less" scoped>
.update-period {
  background-color: rgb(248, 248, 248);
  overflow: hidden;
  color: #000;
  padding: 36px 10px 36px 10px;

  h2 {
    font-size: 14px;
    padding-bottom: 19px;
  }

  main {
    .tab-container {
      display: flex;
      gap: 4px;

      .tab-item {
        color:#666;
        border-bottom: solid 0.5px #ccc;
      }
    }

    .text-container {
      padding: 9px 0 24px;

      .text {
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
      }

    }

    ul {
      .header {
        display: flex;
        border-radius: 6px;
        background: #00c599;
        height: 36px;
        line-height: 36px;
        color: #fff;


        span {
          width: 50%;
          text-align: center;
        }
      }

      li {
        display: flex;
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid #d9d9d9;

        span {
          width: 50%;
          text-align: center;
          font-size: 14px;
        }
      }
    }

    h6 {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      letter-spacing: 0.02em;
      text-align: left;
      color: #00c599;
      padding-top: 20px;
      padding-bottom: 6px;
    }

    .disclaimer-text {
      color: #666666;
      font-size: 13px;
      line-height: 22px;
      font-weight: 400;
    }
  }

}

footer {
  background-color: #000;
  padding: 16px;

  .svg-container {
    display: flex;
    gap: 10px;
  }

  .links-container {
    padding-top: 26px;
    ul {
      display: flex;
      flex-wrap: wrap;
      li {
        margin-right: 20px;
        color: #ffffff8c;
        font-size: 12px;
        position: relative;


        &::after {
          content: "";
          position: absolute;
          top: 0;
          bottom: 0;
          margin: auto;
          right: -12px;
          width: 1px;
          height: 8px;
          background-color: #ffffff8c;
        }
      }
    }
  }

  .copyright {
    font-size: 12px;
    line-height: 20px;
    color: #ffffff8c;
  }
}
</style>
