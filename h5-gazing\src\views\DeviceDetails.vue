<template>
  <div class="pages">
    <van-nav-bar
      :title="config.device.nickname"
      :safe-area-inset-top="platform === 'ios'"
      @click-left="onClickLeft"
      :border="false"
    >
      <template #left>
        <div class="header-left">
          <van-icon name="arrow-left" color="#303A58" size="18" />
        </div>
      </template>
    </van-nav-bar>
    <div class="device-status-container">
      <div class="device-status-left">
        <div
          class="status-indicator"
          :class="config.device.status === 1 ? 'online' : config.device.status === 2 ? 'standby' : 'offline'"
        ></div>
        <div class="network-signal" v-if="config.device_config_item.is_4g_card">
          <img
            class="signal-icon"
            src="../assets/device/<EMAIL>"
            alt=""
            v-if="config.device.attributes.wifi_rssi > -85"
          />
          <img
            class="signal-icon"
            src="../assets/device/<EMAIL>"
            alt=""
            v-else-if="
              config.device.attributes.wifi_rssi <= -85 && config.device.attributes.wifi_rssi > -95
            "
          />
          <img
            class="signal-icon"
            src="../assets/device/<EMAIL>"
            alt=""
            v-else-if="
              config.device.attributes.wifi_rssi <= -95 && config.device.attributes.wifi_rssi > -105
            "
          />
          <img
            class="signal-icon"
            src="../assets/device/<EMAIL>"
            alt=""
            v-else-if="
              config.device.attributes.wifi_rssi <= -105 &&
              config.device.attributes.wifi_rssi > -115
            "
          />
          <img
            class="signal-icon"
            src="../assets/device/<EMAIL>"
            alt=""
            v-else-if="config.device.attributes.wifi_rssi < -115"
          />
          <img class="signal-icon" src="../assets/device/<EMAIL>" alt="" v-else />
          <span class="signal-text">{{ config.device.attributes.wifi_rssi || "-50" }}dBm</span>
        </div>
        <div class="network-signal" v-else-if="config.device_config_item.is_4g_card === 0">
          <img
            class="signal-icon"
            src="../assets/device/<EMAIL>"
            alt=""
            v-if="config.device.attributes.wifi_rssi > -55"
          />
          <img
            class="signal-icon"
            src="../assets/device/<EMAIL>"
            alt=""
            v-else-if="
              config.device.attributes.wifi_rssi <= -55 && config.device.attributes.wifi_rssi > -64
            "
          />
          <img
            class="signal-icon"
            src="../assets/device/<EMAIL>"
            alt=""
            v-else-if="
              config.device.attributes.wifi_rssi <= -64 && config.device.attributes.wifi_rssi > -73
            "
          />
          <img
            class="signal-icon"
            src="../assets/device/<EMAIL>"
            alt=""
            v-else-if="
              config.device.attributes.wifi_rssi <= -73 && config.device.attributes.wifi_rssi > -82
            "
          />
          <img
            class="signal-icon"
            src="../assets/device/<EMAIL>"
            alt=""
            v-else-if="config.device.attributes.wifi_rssi < -82"
          />
          <img class="signal-icon" src="../assets/device/<EMAIL>" alt="" v-else />
          <span class="signal-text">{{ config.device.attributes.wifi_rssi || -30 }}dBm</span>
        </div>
      </div>
      <div class="battery-status">
        <span class="views-num">{{ config.device_log.value.wakeup_count }}</span>
        <img
          class="battry-icon"
          v-if="config.device.attributes.battery_level > 100"
          src="../assets/device/cd.png"
          alt=""
        />
        <img
          class="battry-icon breathing-animation"
          v-else-if="config.device.attributes.battery_level < 10"
          src="../assets/device/1_9.png"
          alt=""
        />
        <img
          class="battry-icon breathing-animation"
          v-else-if="
            config.device.attributes.battery_level >= 10 &&
            config.device.attributes.battery_level <= 20
          "
          src="../assets/device/10_20.png"
          alt=""
        />
        <img
          class="battry-icon"
          v-else-if="
            config.device.attributes.battery_level <= 30 &&
            config.device.attributes.battery_level >= 20
          "
          src="../assets/device/20_30.png"
          alt=""
        />
        <img
          class="battry-icon"
          v-else-if="
            config.device.attributes.battery_level <= 50 &&
            config.device.attributes.battery_level >= 30
          "
          src="../assets/device/30_50.png"
          alt=""
        />
        <img
          class="battry-icon"
          v-else-if="
            config.device.attributes.battery_level <= 80 &&
            config.device.attributes.battery_level >= 50
          "
          src="../assets/device/50_80.png"
          alt=""
        />
        <img
          class="battry-icon"
          v-else-if="config.device.attributes.battery_level > 80"
          src="../assets/device/100.png"
          alt=""
        />
        <span
          class="battry-percent"
          :class="config.device.attributes.battery_level <= 20 ? 'red-color' : ''"
        >
          {{
            config.device.attributes.battery_level > 100
              ? config.device.attributes.battery_level - 256
              : config.device.attributes.battery_level
          }}%
        </span>
        <van-icon
          name="warning-o"
          color="#FF0000"
          v-if="config.device.attributes.battery_level < 21"
        />
      </div>
    </div>
    <div class="status-info">
      <div class="network-speed">
        <div class="speed-item">
          <span class="speed-text-up">
            {{
              config.device_log.value.living && config.device_log.value.living.upload_rate
                ? config.device_log.value.living.upload_rate
                : 0
            }}KB/s
          </span>
          <img class="arrow-icon-up" src="../assets/arrow-1.png" alt="" />
        </div>
        <div class="speed-item">
          <img class="arrow-icon-down" src="../assets/arrow-2.png" alt="" />
        </div>
        <div class="speed-item">
          <img class="arrow-icon-up" src="../assets/arrow-3.png" alt="" />
          <span class="speed-text-down">{{ config.device.attributes.speed_mbps }}KB/s</span>
        </div>
      </div>
      <div class="status-icons">
        <div class="video-icon" v-if="startRecording && config.device_log.value.recording === 0">
          <img class="video-icon-left" src="../assets/video-1.png" alt="" />
          <img class="video-icon-center breathing-animation" src="../assets/video-2.png" alt="" />
        </div>
        <img
          class="union-icon"
          v-if="config.device_config_item.resolution === 0"
          src="../assets/device/<EMAIL>"
          alt=""
        />
        <img
          class="union-icon"
          v-else-if="config.device_config_item.resolution === 1"
          src="../assets/device/<EMAIL>"
          alt=""
        />
        <img
          class="union-icon"
          v-else-if="config.device_config_item.resolution === 2"
          src="../assets/device/<EMAIL>"
          alt=""
        />
      </div>
    </div>
    <div class="record-tips" v-if="config.device_log.value.recording>0">
      {{ config.device_log.value.recording===2?"当前网络差，录制已停止。":"由于3分钟的录制限制，录制已停止。" }}
    </div>
    <div class="container-video" @click="toggleVisibility">
      <video
        ref="RemoteVideo"
        id="remote-video"
        @play="onPlay"
        autoplay
        playsinline
        webkit-playsinline
        muted
      ></video>
      <audio ref="RemoteAudio" id="remote-audio"></audio>
      <!-- 设备信息时间 -->
      <div v-if="isVisible" class="device-info">
        <!-- <div>模式：{{ config.mode }}</div> -->
        <div>视频编码: {{ config.device.attributes.video_decoder?.mimeType || '' }}</div>
        <div>帧率：{{ config.device.attributes.video_decoder?.clockRate || '' }}</div>
        <div>
          视频解/组/渲染包数:
          {{
            config.device.attributes.video_decoder?.packetsReceived -
            config.device.attributes.video_decoder?.packetsLost
          }}/{{ config.device.attributes.video_decoder?.packetsReceived }}/{{
            config.device.attributes.video_decoder?.packetsReceived -
            config.device.attributes.video_decoder?.packetsLost
          }}
        </div>
        <div>
          音频编码: {{ config.device.attributes.audio_decoder?.mimeType || '' }}/{{
            config.device.attributes.audio_decoder?.clockRate || ''
          }}
        </div>
        <div>
          音频解/收包数：{{
            config.device.attributes.audio_decoder?.packetsReceived -
            config.device.attributes.audio_decoder?.packetsLost
          }}/{{ config.device.attributes.audio_decoder?.packetsReceived }}
        </div>
        <div>设备唤醒：{{ deviceEvocationTime() }} ms</div>
        <div>总计：{{ totalTime }} ms</div>
        <div>唤醒次数:{{ 1 }} 唤醒失败:0</div>
        <div>
          其他访问用户:{{
            config.device_log.value.other_user === "null"
              ? "无"
              : config.device_log.value.other_user
          }}
        </div>
        <div>
          转发延时:{{ config.device_log.value?.transpond_delay || 0 }}ms，上传云存储延时:{{
            config.device_log.value?.upload_cloud_delay || 0
          }}ms
        </div>
        <div>推流次数:{{ config.device_log.value?.wakeup_count || 0 }}</div>
        <div>Servers：{{ config.webrtc_item?.turn_addr || '' }} ({{ config.device?.area || '' }})</div>
        <div>living：{{ config.device_log.value.living && config.device_log.value.living.debug_str ?config.device_log.value.living.debug_str:'' }}</div>
        <div>esi：{{ config.device_log.value?.esi || '' }}</div>
        <div>datetime：{{ config.device_log.value?.datetime || '' }}</div>
        <div>上一次唤醒异常:-</div>
        <div>重启/重连/PIR{{ config.device_log.value?.wakeup_stat || '' }}</div>
      </div>
    </div>
    <div class="tool">
      <div class="tool-list">
        <div class="tool-item" @click="screenshot('截屏成功')">
          <img class="tool-img" src="../assets/device/jt.png" alt="" />
          <p class="tool-text">截图</p>
        </div>
        <div class="tool-item" @click="toggleMute">
          <img v-if="soundShow" class="tool-img" src="../assets/device/ky.png" alt="" />
          <img v-else class="tool-img" src="../assets/device/jy.png" alt="" />
          <p class="tool-text">声音</p>
        </div>
        <div class="tool-item" @click="tools">
          <img v-if="toolShow" class="tool-img" src="../assets/device/tools.png" alt="" />
          <img v-else class="tool-img" src="../assets/device/tool.png" alt="" />
          <p class="tool-text">控制台</p>
        </div>
      </div>
      <!-- 控制台 -->
      <div class="tool-box" v-if="toolShow" id="tool-box">
        <div class="tool-top-left" @click.stop="resetDevice">
          <img class="tool-top-left-img" :style="imageStyle" src="../assets/device/zd.png" alt="" />
        </div>
        <div class="tool-top-right" @click.stop="toolShow = !toolShow">
          <img class="tool-top-right-img" src="../assets/device/close.png" alt="" />
        </div>
        <div
          class="tool-data"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
        >
          <div class="tool-data-imgs shang" v-if="directionType === 1"></div>
          <div class="tool-data-imgs xia" v-else-if="directionType === 2"></div>
          <div class="tool-data-imgs zuo" v-else-if="directionType === 3"></div>
          <div class="tool-data-imgs you" v-else-if="directionType === 4"></div>
          <img class="tool-data-img" src="../assets/device/def.png" alt="" />
        </div>
      </div>
      <!-- 语音 -->
      <div class="tool-box" v-else>
        <div class="tool-voice-top">
          <!-- <img
            v-if="openVoiceShow"
            class="tool-voice-top-img"
            src="../assets/device/d.png"
            alt=""
          />
          <img v-else class="tool-voice-top-img" src="../assets/device/x.png" alt="" /> -->
          <p class="timer-display" v-if="openVoiceShow">
            {{ formatTime(minutes) }} : {{ formatTime(seconds) }}
          </p>
        </div>
        <div class="tool-voice" :class="openVoiceShow ? 'active-voice' : ''" @click="openVoice">
          <div v-if="openVoiceShow" class="tool-voice-img open-voice"></div>
          <div v-else class="tool-voice-img end-voice"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  PeerConnInit,
  WakeUp,
  Standby,
  AudioOutputInit,
  accept,
  liveConsole,
  deviceEvocationTime,
  getSendWakeUpTime,
  // getMode,
  toggleAudio,
  closeMicrophone,
  stopSpeedMonitoring,
  sendVoice
} from "@/components/WebRTCClient"
import { useImageRotation } from "@/utils/useImageRotation"
import config from "@/config"
import { showConfirmDialog, showLoadingToast, closeToast } from "vant"
import { ref, onMounted, watch, onUnmounted } from "vue"
import { useRouter } from "vue-router"
import { screenshot } from "@/utils"

const RemoteVideo = ref<HTMLVideoElement | null>(null)
const RemoteAudio = ref<HTMLAudioElement | null>(null)
const platform = ref<string>(config.platform)
const status = ref<number>(0)
const router = useRouter()
const soundShow = ref<boolean>(false)
const toolShow = ref<boolean>(false)
const openVoiceShow = ref<boolean>(false)
const directionType = ref<number>(0)
const { isRotating, imageStyle, startRotation } = useImageRotation()
// 长按阈值（毫秒）
const LONG_PRESS_THRESHOLD = 500
// 方向更新频率（毫秒）
const DIRECTION_UPDATE_INTERVAL = 300
// 处理触摸开始事件
let touchStartTime: number | null = null
let isLongPressDetected = false
let directionUpdateTimer: NodeJS.Timeout | null = null
const isVisible = ref<boolean>(false)
const totalTime = ref<number>(0)
const lastClickTime = ref(0)
const DOUBLE_CLICK_THRESHOLD = 1000 // 双击时间间隔阈值（毫秒）
const minutes = ref(0)
const seconds = ref(0)
const intervalId = ref<number | null>(null)
const startRecording = ref<boolean>(false)

const onClickLeft = () => {
  Standby()
  closeMicrophone(RemoteVideo.value)
  pauseTimer()
  stopSpeedMonitoring()
  if(config.isVoice.value){
    sendVoice(0)
    config.isVoice.value = false
  }
  router.push("/")
}

// 格式化显示成 00:00
function formatTime(value: number): string {
  return value.toString().padStart(2, "0")
}

// 启动语音计时器
function startTimer() {
  intervalId.value = window.setInterval(() => {
    seconds.value++
    if (seconds.value >= 60) {
      seconds.value = 0
      minutes.value++
    }
  }, 1000)
}

// 清空语音计时器
const pauseTimer = () => {
  if (intervalId.value !== null) {
    clearInterval(intervalId.value)
    intervalId.value = null
  }
}

// 重置语言计时器
const resetTimer = () => {
  pauseTimer()
  minutes.value = 0
  seconds.value = 0
}

const toggleMute = () => {
  soundShow.value = !soundShow.value
  RemoteVideo.value!.muted = !RemoteVideo.value!.muted
}

const tools = () => {
  toolShow.value = !toolShow.value
}

const openVoice = () => {
  if(openVoiceShow.value){
    sendVoice(0)
  }else {
    sendVoice(1)
  }
}

const setVoice = ()=>{
  openVoiceShow.value = !openVoiceShow.value
  if (openVoiceShow.value) {
    toggleAudio(true)
    startTimer()
  } else {
    toggleAudio(false)
    resetTimer()
  }
  if (openVoiceShow.value && !soundShow.value) {
    soundShow.value = true
    RemoteVideo.value!.muted = false
  }
}

const resetDevice = () => {
  if (isRotating.value) return
  showConfirmDialog({
    title: "",
    message: "是否将摄像机归中?",
    confirmButtonText: "是",
    cancelButtonText: "否",
  })
    .then(() => {
      liveConsole("reset")
      startRotation()
    })
    .catch(() => {
      // on cancel
    })
}

// 获取圆心坐标
const getCircleCenter = (): { x: number; y: number } => {
  const el = document.getElementById("tool-box")
  if (!el) return { x: 0, y: 0 }

  const rect = el.getBoundingClientRect()
  return {
    x: rect.left + rect.width / 2,
    y: rect.top + rect.height / 2,
  }
}

// 判断触摸方向
const getTouchDirection = (x: number, y: number): string => {
  const { x: centerX, y: centerY } = getCircleCenter()
  const offsetX = x - centerX
  const offsetY = y - centerY

  // 计算角度
  const angle = Math.atan2(offsetY, offsetX) * (180 / Math.PI)

  if (angle >= -45 && angle < 45) return "right"
  if (angle >= 45 && angle < 135) return "bottom"
  if (angle >= 135 || angle < -135) return "left"
  if (angle >= -135 && angle < -45) return "top"

  return ""
}

// 节流函数
const throttle = (fn: Function, delay: number) => {
  let lastTime = 0
  return (...args: any[]) => {
    const now = new Date().getTime()
    if (now - lastTime >= delay) {
      fn(...args)
      lastTime = now
    }
  }
}

// 处理触摸开始事件
const handleTouchStart = (event: TouchEvent) => {
  touchStartTime = new Date().getTime()
  isLongPressDetected = false

  // 在触摸开始时，获取初始位置并更新方向类型
  const touch = event.changedTouches[0]
  const startX = touch.clientX
  const startY = touch.clientY

  const initialDirection = getTouchDirection(startX, startY)
  updateDirection(initialDirection)

  // 阻止默认行为
  event.preventDefault()

  // 设置一个定时器来检测长按
  setTimeout(() => {
    if (touchStartTime !== null && new Date().getTime() - touchStartTime >= LONG_PRESS_THRESHOLD) {
      isLongPressDetected = true
      onLongPress(event)
    }
  }, LONG_PRESS_THRESHOLD)
}

// 处理触摸移动事件
const handleTouchMoveImpl = (event: TouchEvent) => {
  if (isLongPressDetected) return // 如果已经检测到长按，不再更新方向
  const touch = event.changedTouches[0]
  const currentX = touch.clientX
  const currentY = touch.clientY

  // 更新方向类型
  const currentDirection = getTouchDirection(currentX, currentY)
  updateDirection(currentDirection)

  // 阻止默认行为
  event.preventDefault()
}

// 处理触摸移动事件
let throttledHandleTouchMove = throttle(handleTouchMoveImpl, 100)
const handleTouchMove = (event: TouchEvent) => {
  throttledHandleTouchMove(event)
}

// 更新方向类型
const updateDirection = (direction: string) => {
  if (direction === "top") {
    directionType.value = 1
    liveConsole("top")
  } else if (direction === "bottom") {
    directionType.value = 2
    liveConsole("bottom")
  } else if (direction === "left") {
    directionType.value = 3
    liveConsole("left")
  } else if (direction === "right") {
    directionType.value = 4
    liveConsole("right")
  }
}

// 长按处理函数
const onLongPress = (event: TouchEvent) => {
  // 启动定时器，每100毫秒更新一次方向
  if (directionUpdateTimer === null) {
    directionUpdateTimer = setInterval(() => {
      const touch = event.touches[0]
      if (touch) {
        const currentX = touch.clientX
        const currentY = touch.clientY

        // 更新方向类型
        const currentDirection = getTouchDirection(currentX, currentY)
        updateDirection(currentDirection)
      }
    }, DIRECTION_UPDATE_INTERVAL)
  }
}

// 处理触摸结束事件
const handleTouchEnd = () => {
  touchStartTime = null
  isLongPressDetected = false

  // 清除定时器
  if (directionUpdateTimer !== null) {
    clearInterval(directionUpdateTimer)
    directionUpdateTimer = null
  }

  // 重置方向类型
  directionType.value = 0
}

// 视频正在播放
const onPlay = () => {
  totalTime.value = new Date().getTime() - getSendWakeUpTime()
  startRecording.value = true
  closeToast()
}

const toggleVisibility = () => {
  const currentTime = new Date().getTime()
  if (currentTime - lastClickTime.value < DOUBLE_CLICK_THRESHOLD) {
    handleDblClick()
  }
  lastClickTime.value = currentTime
}

const handleDblClick = () => {
  // getMode()
  isVisible.value = !isVisible.value
}

watch(config.device_status, (newValue, oldValue) => {
  if (newValue) {
    status.value = newValue
    if (newValue === 3) {
      fetch()
    }
  }
})

watch(config.isVoice, () => {
  setVoice()
})

const fetch = () => {
  showLoadingToast({
    duration: 5000,
    message: "加载中...",
    forbidClick: true,
  })
  if (RemoteVideo.value && RemoteAudio.value) {
    PeerConnInit(RemoteVideo.value, RemoteAudio.value);
  } else {
    console.error("Video or Audio element not found");
  }
  if (config.wakeUp_type === 2) {
    accept()
  } else {
    WakeUp()
  }
  if (RemoteVideo.value && RemoteAudio.value) {
    AudioOutputInit()
  } else {
    console.error("Video or Audio element not found");
  }
  config.device_log.value.recording = 0
}

onMounted(() => {
  fetch()
})

onUnmounted(() => {
  closeMicrophone(RemoteVideo.value)
  pauseTimer()
  stopSpeedMonitoring()
  if(config.isVoice.value){
    config.isVoice.value = false
    sendVoice(0)
  }
})
</script>

<style lang="less" scoped>
.pages {
  background-color: #29292d;
  --van-nav-bar-title-font-size: 18px;
  --van-nav-bar-title-text-color: #fff;
  --van-nav-bar-background: #29292d;
}

img {
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
  -webkit-touch-callout: none;
}

.header-left {
  display: inline-block;
  width: 36px;
  height: 36px;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.device-status-container {
  height: 40px;
  display: flex;
  align-items: center;
  padding: 10px 16px 0;
  justify-content: space-between;
  .device-status-left {
    display: flex;
    align-items: center;

    .status-indicator {
      width: 15px;
      height: 15px;
      border-radius: 50%;
    }

    .network-signal {
      display: flex;
      align-items: center;

      .signal-icon {
        width: 20px;
        height: 14px;
        margin-left: 8px;
      }

      .signal-text {
        font-size: 12px;
        color: #fff;
        margin-left: 6px;
      }
    }
  }
  .offline {
    background-color: gray;
  }
  .standby {
    background-color: green;
  }
  .online {
    background-color: blue;
  }
  .battery-status {
    display: flex;
    align-items: center;

    .views-num {
      font-size: 12px;
      text-align: center;
      color: #fff;
      margin-right: 2px;
    }

    .battry-icon {
      width: 26px;
      height: 12px;
      margin: 0 5px;
    }

    .breathing-animation {
      animation: breathing 1s ease-in-out infinite;
    }

    .battry-percent {
      font-size: 12px;
      text-align: center;
      color: #fff;
    }

    .red-color {
      color: red;
    }
  }
}

@keyframes breathing {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  height: 20px;
  margin-bottom: 46px;

  .network-speed {
    padding: 0;
    margin: 0;
  }

  .status-icons {
    display: flex;
    align-items: center;

    .video-icon {
      display: inline-flex;
      align-items: center;
      position: relative;
      width: 22px;
      height: 14px;
      margin-right: 13px;

      .video-icon-left {
        width: 26px;
        height: 14px;
      }

      .video-icon-center {
        position: absolute;
        left: 7px;
        top: 4px;
        width: 6px;
        height: 6px;
        z-index: 1;
      }

      .breathing-animation {
        animation: breathing 1s ease-in-out infinite;
      }
    }

    .union-icon {
      width: 24px;
      height: 14px;
    }
  }
}

.network-speed {
  display: flex;
  margin-bottom: 6px;
  height: 20px;
  justify-content: flex-start;

  .speed-item {
    display: flex;
    align-items: center;

    .arrow-icon-down {
      width: 8px;
      height: 14px;
    }

    .arrow-icon-up {
      width: 8px;
      height: 8px;
      padding-bottom: 5px;
    }

    .speed-text-up {
      font-size: 10px;
      color: #3793ff;
      margin-right: 4px;
    }

    .speed-text-down {
      font-size: 10px;
      color: #60bf57;
      margin-left: 4px;
    }
  }
}

.record-tips {
  height: 32px;
  background-color: #3b3b3f;
  color: #fff;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container-video {
  position: relative;
  #remote-video {
    background-color: black;
    width: 100%;
    height: 200px;
    object-fit: cover;
    transform: translateZ(0);
  }
  .device-info {
    position: absolute;
    left: 16px;
    top: -100px;
    font-size: 13px;
    color: #00ff00;
    z-index: 999;
  }
}

.tool {
  padding: 20px 16px 0 16px;
  .tool-list {
    padding: 0 16px;
    display: flex;
    justify-content: space-around;
    margin-bottom: 40px;
    .tool-item {
      text-align: center;
      .tool-img {
        width: 60px;
        height: 60px;
        margin-bottom: 5px;
      }
      .tool-text {
        margin: 0;
        padding: 0;
        font-size: 12px;
        color: #fff;
        font-weight: bold;
      }
    }
  }
  .tool-box {
    position: relative;
    .tool-voice-top {
      display: flex;
      justify-content: center;
      margin-bottom: 8px;
      .tool-voice-top-img {
        width: 85px;
        height: 19px;
      }
      .timer-display {
        width: 60px;
        height: 20px;
        font-size: 12px;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f65252;
        border-radius: 5px;
      }
    }
    .tool-voice {
      margin: 0 auto;
      width: 114px;
      height: 114px;
      border-radius: 50%;
      background-color: #3b3b3f;
      display: flex;
      justify-content: center;
      align-items: center;
      .tool-voice-img {
        width: 100px;
        height: 100px;
        background-position: center;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        -webkit-touch-callout: none;
      }
      .open-voice {
        background-image: url("../assets/device/kyy.png");
      }
      .end-voice {
        background-image: url("../assets/device/gyy.png");
      }
    }
    .active-voice {
      background-color: #4774f5;
    }
    .tool-top-left {
      position: absolute;
      left: 16px;
      top: 10px;
      width: 41px;
      height: 41px;
      z-index: 9;
      .tool-top-left-img {
        width: 41px;
        height: 41px;
        transition: transform 0.016s linear;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        -webkit-touch-callout: none;
      }
    }
    .tool-top-right {
      position: absolute;
      right: 16px;
      top: 10px;
      width: 32px;
      height: 32px;
      z-index: 9;
      .tool-top-right-img {
        width: 32px;
        height: 32px;
      }
    }
    .tool-data {
      width: 205px;
      height: 205px;
      position: relative;
      .tool-data-img {
        width: 205px;
        height: 205px;
      }
      .tool-data-imgs {
        position: absolute;
        left: 0;
        top: 0;
        width: 205px;
        height: 205px;
        background-position: center;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        -webkit-touch-callout: none;
      }
      .shang {
        background-image: url("../assets/device/shang.png");
      }
      .xia {
        background-image: url("../assets/device/xia.png");
      }
      .zuo {
        background-image: url("../assets/device/zuo.png");
      }
      .you {
        background-image: url("../assets/device/you.png");
      }
    }
  }
  #tool-box {
    display: flex;
    justify-content: center;
  }
}
</style>
