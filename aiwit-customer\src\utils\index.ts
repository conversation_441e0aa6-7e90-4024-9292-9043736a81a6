import config from '@/config'
import { showLoadingToast } from "vant"

// 自动补0
function formatTime(time: number) {
  return time < 10 ? `0${time}` : time
}

// 获取当前年月日
export function updateTime() {
  const now = new Date()
  const year = now.getFullYear() //年
  const month = now.getMonth() + 1 //月
  const day = now.getDate() //日
  const hours = now.getHours() //小时数
  const minutes = now.getMinutes() //分钟数
  const seconds = now.getSeconds() //秒数
  // const week = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"][now.getDay()]  //星期
  //想展示什么  对应的展示即可
  return `${year}/${formatTime(month)}/${formatTime(day)} ${formatTime(
    hours
  )}:${formatTime(minutes)}:${formatTime(seconds)}`
}

// 4位随机数
export function randomNum() {
  return Math.floor(Math.random() * 4000 + 1000)
}

// 读取照片
export function readImg(file: any) {
  return new Promise((resolve, reject) => {
    let img: HTMLImageElement = new Image()
    const reader = new FileReader()
    reader.onload = function (e: any) {
      img.src = e.target.result
      base64ToFile(img.src, new Date().getTime() + '.png')
    }
    reader.onerror = (e) => {
      reject(e)
    }
    reader.readAsDataURL(file)
    img.onload = () => {
      resolve(img)
    }
    img.onerror = (e) => {
      reject(e)
    }
  })
}

export function compressImg(img: any, type: any, mx: any, mh: any) {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const context: any = canvas.getContext('2d')
    const { width: originWidth, height: originHeight } = img
    // 最大尺寸限制
    const maxWidth = mx
    const maxHeight = mh
    // 目标尺寸
    var targetWidth = originWidth
    var targetHeight = originHeight
    if (originWidth > maxWidth || originHeight > maxHeight) {
      if (originWidth / originHeight > 1) {
        // 宽图片
        targetWidth = maxWidth
        targetHeight = Math.round(maxWidth * (originHeight / originWidth))
      } else {
        // 高图片
        targetHeight = maxHeight
        targetWidth = Math.round(maxHeight * (originWidth / originHeight))
      }
    }
    canvas.width = targetWidth
    canvas.height = targetHeight
    context.clearRect(0, 0, targetWidth, targetHeight)
    // 图片绘制
    context.drawImage(img, 0, 0, targetWidth, targetHeight)
    resolve(canvas.toDataURL(type, 0.2))
  })
}

// 解码base64
export function base64ToFile(urlData: any, fileName: string) {
  let arr = urlData.split(',')
  let mine = arr[0].match(/:(.*?);/)[1]
  let bytes = atob(arr[1]) //解码base64
  let n = bytes.length
  let ia = new Uint8Array(n)
  while (n--) {
    ia[n] = bytes.charCodeAt(n)
  }
  return new File([ia], fileName, { type: mine })
}

// 防抖
// 不管怎么点击，只在500毫秒后触发事件，500毫秒内点击也要等到500毫秒后再触发事件
export const debounce = (fn: Function, delay = 500) => {
  let timer: number | null = null
  return (...args: any[]) => {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

// 版本号比较
export function compareVersions(version1: any) {
  // 将版本号拆分成数字数组
  var arr1 = version1.split('.')
  var arr2 = '2.8.1'.split('.')

  // 遍历数字数组进行逐段比较
  for (var i = 0; i < Math.max(arr1.length, arr2.length); i++) {
    var num1 = parseInt(arr1[i] || '0') // 如果数组长度不够，则将缺失部分补0
    var num2 = parseInt(arr2[i] || '0')

    if (num1 < num2 && (config.app_name == '' || config.app_name == 'aiwit')) {
      return -1 // 版本1小于版本2
    } else {
      return 1 // 版本1大于版本2
    }
  }
  return 0 // 版本1等于版本2
}

// 截取图片路径里的参数
export function getTypeFromUrl(url:string) {
  const match = url.match(/[?&]type=([^&#]*)/);
  return match ? match[1] : '';
}

//点击加loading状态
export function showLoading() {
  showLoadingToast({
    message: "Loading...",
    forbidClick: true,
  })
}