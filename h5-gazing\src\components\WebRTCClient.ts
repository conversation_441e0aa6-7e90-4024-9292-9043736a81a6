import config from "@/config"
import { showLoadingToast, showSuccessToast, closeToast, showToast, showDialog } from "vant"
import router from "@/router"
import { useHomeStore } from "@/stores/home"
import { MqttService } from "@/utils/mqtt"
import { decryptMessage, encrypt } from "@/utils/xor"
import type { syncData, otaItemData, propertyData } from "@/api/base"
import {
  clearLocalCache,
  executeNotification,
  initMediaDevicesPolyfill,
  get_microphone,
} from "@/utils"
import { useImageRotation } from "@/utils/useImageRotation"
import { ref } from "vue"

let mediaStream: any = null
let PeerConn: RTCPeerConnection | any = null
let DataChannel: RTCDataChannel | null = null // 新增：存储 DataChannel 实例
let MQTTClient: any = null
let hasSentAnswer = false
let toast: any = null //进度弹框
const isLoading = ref<boolean>(false) //加载中
let loadingTime: any = null
// let CandidateTimeout: any = null
let sendWakeUpTime = 0 //发送唤起指令时间
let receiveWakeUpTime = 0 //收到设备在线时间
const { resetRotation } = useImageRotation()
// 计算直播下行
let lastBytesReceived = 0
let lastTime = 0
let speedInterval: number | null = null
let audioSender: RTCRtpSender | null = null

// 唤醒设备
export function WakeUp() {
  sendWakeUpTime = 0
  hasSentAnswer = false
  if (MQTTClient) {
    let topic = `v1/${config.product_key}/${config.device_sn}/control`
    let msg = JSON.stringify({
      cmd: "wakeUp",
      sessionID: config.user_id,
      uuid: config.device_sn,
      email: config.username.value,
      date: Math.floor(new Date().getTime() / 1000),
      dev_signal: config.webrtc_item.dev_signal,
      turn_addr: config.webrtc_item.turn_addr.replace("turn:", ""),
      turn_pwd: config.webrtc_item.turn_pwd,
      turn_user: config.webrtc_item.turn_user,
    })
    MQTTClient.publish(topic, encrypt(msg))
    sendWakeUpTime = new Date().getTime()
  } else {
    console.error("MQTT未连接")
  }
}

// 发送pir事件唤起设备直播推流
export function accept() {
  hasSentAnswer = false
  if (MQTTClient) {
    let topic = `v1/${config.product_key}/${config.device_sn}/control`

    let msg = JSON.stringify({
      cmd: "accept",
      sessionID: config.user_id,
      uuid: config.device_sn,
      date: Math.floor(new Date().getTime() / 1000),
      dev_signal: config.webrtc_item.dev_signal,
      turn_addr: config.webrtc_item.turn_addr.replace("turn:", ""),
      turn_pwd: config.webrtc_item.turn_pwd,
      turn_user: config.webrtc_item.turn_user,
    })
    MQTTClient.publish(topic, encrypt(msg))
  } else {
    console.error("MQTT未连接")
  }
}

export function Standby(is_pir = false) {
  if (MQTTClient) {
    let topic = ""
    if (is_pir) {
      topic = `v1/${config.old_product_key}/${config.old_device_sn}/control`
    } else {
      topic = `v1/${config.product_key}/${config.device_sn}/control`
    }
    let msg = JSON.stringify({
      cmd: "standby",
      sessionID: config.user_id,
      uuid: config.device_sn,
      email: config.username.value,
    })
    MQTTClient.publish(topic, encrypt(msg))
  } else {
    console.error("MQTT未连接")
  }
}

/*  获取设备设置
  light_leve     环境光强度
  battery     电量
*/
export function getProperty(arr: Array<propertyData>) {
  if (MQTTClient) {
    let topic = `v1/${config.product_key}/${config.device_sn}/control`
    let msg = JSON.stringify({
      cmd: "getProperty",
      sessionID: config.user_id,
      uuid: config.device_sn,
      product_key: config.product_key,
      property: arr,
    })
    MQTTClient.publish(topic, encrypt(msg))
  } else {
    console.error("MQTT未连接")
  }
}

/*  设备设置
  setHumanDetection 人形侦测
  setDetectionArea  侦测区域
  setLamplight      灯光感应
  setPersonMarker   人形标记
  PIR               移动侦测
*/
export function setProperty(arr: Array<propertyData>) {
  if (MQTTClient) {
    showLoadingToast({
      duration: 0,
      message: "加载中...",
      forbidClick: true,
    })
    isLoading.value = true
    let topic = `v1/${config.product_key}/${config.device_sn}/control`
    let msg = JSON.stringify({
      cmd: "setProperty",
      sessionID: config.user_id,
      device_sn: config.device_sn,
      product_key: config.product_key,
      property: arr,
    })
    MQTTClient.publish(topic, encrypt(msg))
    checkLoading()
  } else {
    console.error("MQTT未连接")
  }
}

export function GetStatus(device_sn: string, product_key: string) {
  if (MQTTClient) {
    let topic = `v1/module/pull/message`
    let msg = JSON.stringify({
      cmd: "getState",
      sessionID: config.user_id,
      device_sn: device_sn,
      product_key: product_key,
    })
    MQTTClient.publish(topic, msg)
  } else {
    console.error("MQTT未连接")
  }
}

// 后台运行通知
export function appLeave() {
  if (MQTTClient) {
    let topic = `v1/module/pull/message`
    let msg = JSON.stringify({
      cmd: "appLeave",
      user_id: config.user_id,
    })
    MQTTClient.publish(topic, msg)
    if (router.currentRoute.value.name === "deviceDetails") {
      Standby()
      closeMicrophone({ srcObject: "" })
    }
  } else {
    console.error("MQTT未连接")
  }
  router.push("/home")
}

// 通知服务器设置设备信息
export function SetAppInfo() {
  if (MQTTClient) {
    let topic = `v1/module/pull/message`
    let msg = JSON.stringify({
      cmd: "setAppInfo",
      app_name: config.app_name,
      token: config.app_informToken,
      user_id: config.user_id,
      is_dev: config.app_is_dev,
      lang: config.lang,
      os: Number(config.app_os),
      is_testflight: config.app_test_flight,
    })
    MQTTClient.publish(topic, msg)
  } else {
    console.error("MQTT未连接")
  }
}

// 通知服务器记录语音是否开启关闭
export function sendVoice(state: number) {
  if (MQTTClient) {
    let topic = `v1/module/pull/message`
    let msg = JSON.stringify({
      cmd: "voice",
      user_id: config.user_id,
      device_sn: config.device.device_sn,
      product_key: config.product_key,
      status: state,
    })
    MQTTClient.publish(topic, msg)
  } else {
    console.error("MQTT未连接")
  }
}

// 通知设备ota升级
export function sendOtaUpdate(item: otaItemData, product_key: string) {
  if (MQTTClient) {
    let topic = `v1/${product_key}/${item.device_sn}/control`
    let msg = JSON.stringify({
      cmd: "otaUpgrade",
      sessionID: config.user_id,
      device_sn: item.device_sn,
      product_key,
      from: "app",
      content: {
        url: item.download_url,
        mcu_version: item.target_mcu_version,
        soc_version: item.target_soc_version,
      },
    })
    MQTTClient.publish(topic, encrypt(msg))
  } else {
    console.error("MQTT未连接")
  }
}

// 通知设备卡回放
export function sendPlayback(item: syncData) {
  if (MQTTClient) {
    config.video_state = 1
    toast = showLoadingToast({
      duration: 0,
      closeOnClick: true,
      message: "下载中 0%",
    })
    let topic = `v1/${config.product_key}/${config.device_sn}/control`
    let msg = JSON.stringify({
      cmd: "cardPlayBack",
      sessionID: config.user_id,
      device_sn: config.device_sn,
      path: encodeURIComponent(item.path),
      endpoint: item.endpoint,
      bucket: item.bucket,
      ossFromID: item.oss_from_id,
    })
    MQTTClient.publish(topic, encrypt(msg))
  } else {
    console.error("MQTT未连接")
  }
}

// 发送SDP给设备端
function sendAnswerSdp() {
  if (MQTTClient) {
    let topic = `v1/${config.product_key}/${config.device_sn}/control`
    let msg = JSON.stringify({
      cmd: "answer",
      data: PeerConn.localDescription?.sdp,
      sessionID: config.user_id,
    })
    MQTTClient.publish(topic, encrypt(msg))
  } else {
    console.error("MQTT未连接")
  }
}

// 发送candidate给设备
function sendCandidate(candidate: RTCIceCandidate) {
  if (MQTTClient) {
    let topic = `v1/${config.product_key}/${config.device_sn}/control`
    let msg = JSON.stringify({
      cmd: "candidate",
      sessionID: config.user_id,
      data: candidate.candidate,
    })
    MQTTClient.publish(topic, encrypt(msg))
  }
}

// 发送直播控制台指令
export function liveConsole(direction: string) {
  let msg = JSON.stringify({
    cmd: "liveConsole",
    sessionID: config.user_id,
    uuid: config.device_sn,
    rotateData: direction,
  })
  sendDataThroughDataChannel(msg)
}

// 发送调整门铃音量
export function setVolume(volume: number) {
  if (MQTTClient) {
    let topic = `v1/${config.product_key}/${config.device_sn}/control`
    let msg = JSON.stringify({
      cmd: "setVolume",
      sessionID: config.user_id,
      volume: volume,
    })
    MQTTClient.publish(topic, encrypt(msg))
  } else {
    console.error("MQTT未连接")
  }
}

// 更改网络
export function wifiChange(ssid: string, pwd: string) {
  if (MQTTClient) {
    let topic = `v1/${config.product_key}/${config.device_sn}/control`
    let msg = JSON.stringify({
      cmd: "wifiChange",
      sessionID: config.user_id,
      ssid,
      pwd,
    })
    MQTTClient.publish(topic, encrypt(msg))
  } else {
    console.error("MQTT未连接")
  }
}

// 判断是否包含IV6地址
function containsIPv6Address(str: string) {
  // 正则表达式匹配 IPv6 地址
  const ipv6Pattern =
    /(?:[A-Fa-f0-9]{1,4}:){7}[A-Fa-f0-9]{1,4}|::(?:[A-Fa-f0-9]{1,4}:)*(?:[A-Fa-f0-9]{1,4}){1,7}|(?:[A-Fa-f0-9]{1,4}:){1,7}::(?:[A-Fa-f0-9]{1,4}){1,7}|(?:[A-Fa-f0-9]{1,4}:){1,6}:(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/

  return ipv6Pattern.test(str)
}

async function SetRemoteSdp(sdp: string,time=500): Promise<RTCSessionDescriptionInit> {
  console.log("Received offer SDP:", sdp)
  try {
    // 设置远程描述 (offer)
    const offer = new RTCSessionDescription({ sdp, type: "offer" })
    await PeerConn.setRemoteDescription(offer)

    // 创建answer
    const answer = await PeerConn.createAnswer({
      offerToReceiveAudio: true,
      offerToReceiveVideo: true,
    })
    console.log("Answer SDP generated successfully:", answer.sdp)

    // 设置本地描述
    await PeerConn.setLocalDescription(answer)
    // 手动从SDP添加ICE候选项
    setTimeout(async () => {
      const candidateLines = sdp
        .split("\r\n")
        .filter((line: string) => line.startsWith("a=candidate:"))
      if (candidateLines.length > 0) {
        for (const candidate of candidateLines) {
          const candidateObj = new RTCIceCandidate({
            candidate: candidate.replace(/^a=/, ""),
            sdpMid: "0",
            sdpMLineIndex: 0,
          })
          try {
            await PeerConn.addIceCandidate(candidateObj)
          } catch (err) {
            console.error("Error adding ICE candidate:", err)
          }
        }
      } else {
        console.log("No ICE candidates found in SDP (relying on setRemoteDescription).")
      }
    }, time)

    return answer
  } catch (err) {
    console.error("Error in SetRemoteSdp:", err)
    throw err
  }
}

export function PeerConnInit(reVideo: any, reAudio: any) {
  PeerConn = null
  DataChannel = null

  const peerConn = new RTCPeerConnection({
    iceServers: [
      {
        urls: config.webrtc_item.turn_addr,
        username: config.webrtc_item.turn_user,
        credential: config.webrtc_item.turn_pwd,
      },
    ],
    iceTransportPolicy: "all", // 确保获取所有类型的地址，包括 IPv4
    // iceCandidatePoolSize: 10,
  })

  PeerConn = peerConn
  startSpeedMonitoring()

  // 创建 DataChannel
  DataChannel = peerConn.createDataChannel("myDataChannel")

  peerConn.ondatachannel = (event: RTCDataChannelEvent) => {
    // 获取远程传来的 dataChannel
    const dataChannels = event.channel

    // 监听 dataChannel 的 open 事件
    dataChannels.onopen = () => {
      console.log(
        `DataChannel is open User ${config.user_id} and ready to be used, label is: ` +
          dataChannels.label
      )
    }

    // 监听 dataChannel 的 message 事件，打印接收到的信息
    dataChannels.onmessage = (event: MessageEvent) => {
      // console.log("Received message:" + event.data)
      let data = JSON.parse(event.data)
      if (data.cmd === "log") {
        config.device_log.value = data
      } else if (data.cmd === "resetEnd") {
        resetRotation()
      }
    }

    // 监听 dataChannel 的 close 事件
    dataChannels.onclose = () => {
      console.log(`DataChannel for User ${config.user_id} closed`)
    }

    dataChannels.onerror = (error: any) => {
      console.error(`DataChannel User ${config.user_id} error:`, error)
    }
  }

  peerConn.onicecandidate = (event: RTCPeerConnectionIceEvent) => {
    if (event.candidate) {
      // 如果还没有发送过答案，则发送答案
      if (!hasSentAnswer && peerConn.localDescription) {
        sendAnswerSdp()
        hasSentAnswer = true
      } else {
        if (event.candidate.candidate.indexOf("tcp") !== -1) return
        if (containsIPv6Address(event.candidate.candidate)) return
        // 否则发送候选信息
        sendCandidate(event.candidate)
      }
    } else {
      // 当没有更多候选者时（即 ICE 收集完成），可以发送最终的候选信息或其他处理
      console.log("All ICE candidates have been gathered.")
    }
  }

  // PeerConn.onicecandidate = (event: { candidate: any }) => {
  //   clearTimeout(CandidateTimeout) // 每次收到候选者时，清除上一个定时器
  //   CandidateTimeout = null

  //   if (event.candidate) {
  //     // 每次接收到新的候选者后，重置定时器
  //     CandidateTimeout = setTimeout(() => {
  //       if (!hasSentAnswer && PeerConn.localDescription) {
  //         sendAnswerSdp()
  //         hasSentAnswer = true
  //       }
  //       clearTimeout(CandidateTimeout) // 所有候选者接收完毕，清除定时器
  //     }, 500) // 500ms
  //   } else {
  //     // 没更多候选了
  //     if (!hasSentAnswer && PeerConn.localDescription) {
  //       sendAnswerSdp()
  //       hasSentAnswer = true
  //     }
  //     clearTimeout(CandidateTimeout) // 所有候选者接收完毕，清除定时器
  //   }
  // }

  // 当接收到远程轨道时，触发 ontrack 事件
  peerConn.ontrack = (event: RTCTrackEvent) => {
    if (event.track.kind === "video" && reVideo.srcObject !== event.streams[0]) {
      reVideo.srcObject = event.streams[0]
    }
    if (event.track.kind === "audio" && reAudio.srcObject !== event.streams[0]) {
      reAudio.srcObject = event.streams[0]
    }
  }
}

// 新增dataChannel发送数据的方法
export function sendDataThroughDataChannel(message: string) {
  if (DataChannel && DataChannel.readyState === "open") {
    DataChannel.send(message)
    console.log("Message sent through DataChannel:", message)
  } else {
    console.error("DataChannel is not open or not initialized.")
  }
}

export function MQTTInit() {
  MQTTClient = null
  MQTTClient = new MqttService()
  MQTTClient.connect()
}

export function subData(device_sn: string, product_key: string) {
  if (MQTTClient) {
    MQTTClient.subscribe(
      `v1/${product_key}/${device_sn}/event`,
      (grant: any) => {
        GetStatus(device_sn, product_key)
      },
      (error: any) => {
        console.error("订阅失败", error)
      }
    )
  }
}

export function setupMessageHandler() {
  if (MQTTClient) {
    // 获取状态库的设备列表
    MQTTClient.setHandleMessage((message: any, payloadStrings: string) => {
      try {
        const res: any = decryptMessage(payloadStrings)
        const { topic } = message
        let data = JSON.parse(res)
        console.log(data)
        handleData(topic, data)
      } catch (error: any) {
        const { topic, payloadString } = message
        let data = JSON.parse(payloadString)
        console.log(data)
        handleData(topic, data)
      }
    })
  }
}

function handleData(topic: string, data: any) {
  const homeStore = useHomeStore()
  switch (topic) {
    case `v1/${data.product_key || config.product_key}/${
      data.device_sn || data.uuid || config.device_sn
    }/event`:
      // 解析buffer，将状态信息提取出来。
      if (data.cmd === "status") {
        // offline 0   standby 2  online 1
        if (data.status === 0) {
          setDeviceStatus(homeStore, data, "https://h5.seewds.com/offline.png", 0)
        } else if (data.status === 2) {
          setDeviceStatus(homeStore, data, "https://h5.seewds.com/standby.png", 2)
        } else if (data.status === 1) {
          setDeviceStatus(homeStore, data, "https://h5.seewds.com/online.png", 1)
        } else {
          console.log("状态信息有误")
        }
      } else if (data.cmd === "getProperty") {
        if (data.property && data.property.length && config.device.device_sn === data.device_sn) {
          data.property.forEach((item: propertyData) => {
            if (item.category === "battery") {
              config.device.attributes.battery_level = Number(item.value)
            } else if (item.category === "light_leve") {
              config.light_leve.value = Number(item.value)
            } else if (item.category === "wifi_rssi") {
              config.device.attributes.wifi_rssi = Number(item.value)
            }
          })
        }
      } else if (data.cmd === "pir") {
        if (
          homeStore.state.deviceList.length &&
          router.currentRoute.value.path !== "/deviceDetails" &&
          config.device_config_item.pir > 0 &&
          config.device.function_bitmap & (1 << 0) &&
          !config.isSilent.value &&
          config.device.attributes.battery_level > 19
        ) {
          config.old_device_sn = config.device_sn
          config.old_product_key = config.product_key
          homeStore.state.deviceList.some((item: any) => {
            if (data.device_sn === item.device_sn) {
              config.device = item
              config.device_sn = data.device_sn
              config.product_key = data.product_key
              return
            }
          })
          if (config.device.area === "US") {
            config.webrtc_item = config.webrtc_list.US
          } else if (config.device.area === "SG") {
            config.webrtc_item = config.webrtc_list.SG
          } else if (config.device.area === "DE") {
            config.webrtc_item = config.webrtc_list.DE
          } else if (config.device.area === "CN") {
            config.webrtc_item = config.webrtc_list.CN
          }
          showDialog({
            title: "Motion Detection",
            message: `There is a motion at ${config.device.nickname}`,
            confirmButtonText: "查看",
            showCancelButton: true,
            cancelButtonText: "忽略",
          })
            .then(() => {
              router.push("/deviceDetails")
            })
            .catch(() => {})
          // setTimeout(() => {
          //   router.push("/deviceVoice")
          // }, 300)
        }
      } else if (data.cmd === "cardPlayBackState") {
        if (data.state === 1) {
          toast.message = `下载中 ${data.progress}%`
        } else if (data.state === 9) {
          config.video_state = 2
          closeToast()
          config.video_path.value = data.path
        }
      } else if (data.cmd === "otaStatus") {
        if (homeStore.state.deviceList.length) {
          homeStore.state.deviceList.some((item: any) => {
            if (data.device_sn === item.device_sn) {
              if (data.content.state) {
                item.ota_state = data.content.state
              }
              if (data.content.progress) {
                item.ota_progress = data.content.progress
              }
              return
            }
          })
        }
      } else if (data.cmd === "otaUpgradeSuccess") {
        if (data.mcu_version) {
          config.device.attributes.mcu_version
        }
        if (data.soc_version) {
          config.device.attributes.soc_version
        }
      } else if (data.cmd === "voice") {
        if (
          config.user_id === data.user_id &&
          router.currentRoute.value.path === "/deviceDetails"
        ) {
          if (data.code === 1 && data.status === 1) {
            config.isVoice.value = true
          } else {
            config.isVoice.value = false
          }
        }
      } else if (data.cmd === "offer") {
        if (config.user_id === data.sessionID) {
          receiveWakeUpTime = 0
          receiveWakeUpTime = new Date().getTime()
          SetRemoteSdp(data.data,data.delay_ms??500)
        }
      } else if (data.cmd === "setProperty") {
        isLoading.value = false
        clearLoadingTime()
        closeToast()
        if (
          config.user_id === data.sessionID &&
          (router.currentRoute.value.path === "/deviceInfo" ||
            router.currentRoute.value.path === "/lamplight" ||
            router.currentRoute.value.path === "/humanDetection")
        ) {
          if (data.property && data.property.length && config.device.device_sn === data.device_sn) {
            data.property.forEach((item: propertyData) => {
              if (item.category === "human_detection") {
                config.device.attributes.human_detection = Number(item.value)
              } else if (item.category === "human_detection_state") {
                config.device.attributes.human_detection_state = Number(item.value)
              } else if (item.category === "detection_area") {
                config.device.attributes.detection_area = Number(item.value)
              } else if (item.category === "detection_area_list") {
                config.device.attributes.detection_area_list = String(item.value)
              } else if (item.category === "PIR") {
                config.device.attributes.PIR = Number(item.value)
              } else if (item.category === "person_marker") {
                config.device.attributes.person_marker = Number(item.value)
              } else if (item.category === "lamplight") {
                config.device.attributes.lamplight = Number(item.value)
              }
            })
            setTimeout(() => {
              showToast("设置成功")
            }, 300)
          } else {
            setTimeout(() => {
              showToast("设置失败")
            }, 300)
          }
        }
      } else {
        if (data.cmd === "login") {
          setDeviceStatus(homeStore, data, "https://h5.seewds.com/online.png", 1, "login")
        } else if (data.cmd === "standby" && router.currentRoute.value.path !== "/deviceDetails") {
          setDeviceStatus(homeStore, data, "https://h5.seewds.com/standby.png", 2)
          if (data.cmd === "standby" && router.currentRoute.value.path === "/deviceVoice") {
            router.go(-1)
          }
        }
      }
      break
    case `v1/${config.user_id}/message`:
      if (data.cmd === 20086) {
        showSuccessToast("设备注册成功")
        homeStore.getDeviceList(2)
        setTimeout(() => {
          router.push("/home")
        }, 5000)
      } else if (data.cmd === 20088) {
        showSuccessToast("设备分享成功")
        homeStore.getDeviceList(2)
        setTimeout(() => {
          router.push("/home")
        }, 5000)
      } else if (data.cmd === 20089) {
        config.const_header = data.data.header
        config.xor_key = data.data.key
      } else if (data.cmd === "AppKickOut") {
        if (data.current_app_id !== config.app_id || data.current_app_id !== config.user_id) {
          clearLocalCache()
        }
      } else if (data.cmd === "TransferDevice") {
        if(data.data.device_sn && homeStore.state.deviceList.length){
          homeStore.state.deviceList.some((item: any) => {
            if (data.data.device_sn === item.device_sn) {
              item.is_charging = true
              item.is_primary = true
              return
            }
          })
        }
      } else if (data.cmd === "NoNetworkConfigPermission") {
        showDialog({
          title: "",
          message: `${data.msg} ${data.data.owner_email}`,
        })
      }
      break
    case `v1/${data.product_key}/${data.device_sn}/data`:
      config.device.attributes.battery_level = data.battery_level
      break
    default:
      console.log("never subscribe this topic")
  }
}

function setDeviceStatus(homeStore: any, data: any, url: string, status: number, type = "no") {
  if (homeStore.state.deviceList.length) {
    homeStore.state.deviceList.some((item: any) => {
      if ((data.device_sn || data.uuid) === item.device_sn) {
        if (type === "login") {
          config.device_status.value = status
        }
        if (data.battery_level || data.battery_level === 0) {
          item.battery_level = data.battery_level
        }
        if (status >= 0) {
          item.status = status
        }
        if (url) {
          item.img_url = url
        }
        return
      }
    })
    if (
      localStorage.getItem("device_sn") &&
      localStorage.getItem("device_sn") === (data.device_sn || data.uuid)
    ) {
      if (status === 0) {
        localStorage.removeItem("device_sn")
        showToast("当前设备未在线")
      } else {
        executeNotification(localStorage.getItem("device_sn") ?? "")
      }
    }
  }
}

// export function SignalInit(reVideo: any, reAudio: any) {
//   RemoteVideo = null
//   RemoteAudio = null
//   RemoteVideo = reVideo
//   RemoteAudio = reAudio
// }

export async function AudioOutputInit() {
  try {
    if (config.platform === "android") {
      await initMediaDevicesPolyfill()
    }

    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true })

    console.log("获取到本地音频流, 已发送...")

    if (!PeerConn) {
      console.error("RTCPeerConnection 未初始化")
      return
    }

    // 获取音频轨道
    const audioTrack = mediaStream.getAudioTracks()[0]
    if (!audioTrack) return

    // 初始禁用音频轨道
    audioTrack.enabled = false

    audioSender = PeerConn.addTrack(audioTrack, mediaStream)
    audioSender!.replaceTrack(null).catch(console.error)

    // 启动音频包统计打印
    startAudioPacketLogging()
  } catch (err) {
    handleGetUserMediaError(err)
  }
}

export function toggleAudio(enabled: boolean) {
  if (!mediaStream || !PeerConn || !audioSender) return

  const audioTrack = mediaStream.getAudioTracks()[0]
  if (!audioTrack) return

  audioTrack.enabled = enabled

  if (enabled) {
    audioSender.replaceTrack(audioTrack).catch(console.error)
  } else {
    audioSender.replaceTrack(null).catch(console.error)
  }
}

// 打印发送的音频包统计
function startAudioPacketLogging() {
  if (!PeerConn) {
    console.error("无法打印音频包统计: PeerConn 未初始化")
    return
  }

  let lastPacketsSent = 0
  let lastTimestamp = 0

  // 每秒打印一次统计信息
  const intervalId = setInterval(async () => {
    try {
      if (!PeerConn) {
        clearInterval(intervalId)
        return
      }
      const stats = await PeerConn.getStats()
      let foundAudioStats = false

      stats.forEach(
        (report: {
          type: string
          kind: string
          packetsSent: number
          bytesSent: number
          timestamp: any
        }) => {
          if (report.type === "outbound-rtp" && report.kind === "audio") {
            foundAudioStats = true
            const packetsSent = report.packetsSent || 0 // 已发送的包数量
            const bytesSent = report.bytesSent || 0 // 已发送的字节数
            const timestamp = report.timestamp // 时间戳

            // 计算每秒发送的包数量
            if (lastTimestamp > 0) {
              const timeDiff = (timestamp - lastTimestamp) / 1000 // 时间差（秒）
              const packetsDiff = packetsSent - lastPacketsSent // 包数量差
              const packetsPerSecond = packetsDiff / timeDiff // 每秒包数

              console.log("发送的音频包统计:")
              console.log(`- 已发送总包数: ${packetsSent}`)
              console.log(`- 已发送字节数: ${bytesSent} bytes`)
              console.log(`- 每秒发送包数: ${packetsPerSecond.toFixed(2)} packets/s`)
              console.log(`- 时间戳: ${new Date(timestamp).toISOString()}`)
            }

            // 更新上一次的统计数据
            lastPacketsSent = packetsSent
            lastTimestamp = timestamp
          }
        }
      )

      // 如果 PeerConn 已关闭，停止打印
      if (PeerConn.connectionState === "closed") {
        clearInterval(intervalId)
        console.log("PeerConn 已关闭，停止音频包统计")
      } else if (!foundAudioStats) {
        console.log("未找到音频 outbound-rtp 统计，可能音频轨道未启用或未发送数据")
      }
    } catch (error) {
      console.error("获取音频包统计失败:", error)
      clearInterval(intervalId)
    }
  }, 1000)
}

function handleGetUserMediaError(err: any) {
  let errorMessage = ""

  switch (err.name) {
    case "NotFoundError":
    case "DevicesNotFoundError":
      errorMessage = "没有找到音频输入设备"
      console.error(errorMessage)
      showToast(errorMessage)
      break
    case "NotAllowedError":
    case "PermissionDeniedError":
      if (config.platform === "android") {
        get_microphone()
      } else {
        errorMessage = "需要麦克风权限，请授权后重试"
        console.error(errorMessage)
        showToast(errorMessage)
      }
      break
    case "NotReadableError":
    case "TrackStartError":
      if (config.platform === "android") {
        get_microphone()
      } else {
        errorMessage = "麦克风设备被占用或无法工作"
        console.error(errorMessage)
        showToast(errorMessage)
      }
      break
    default:
      errorMessage = `获取音频设备失败: ${err.message}`
      console.error(errorMessage, err)
      showToast(errorMessage)
  }

  return errorMessage
}

export function closeMicrophone(reVideo: any) {
  if (mediaStream) {
    // 停止所有的音轨
    const audioTracks = mediaStream.getAudioTracks()
    audioTracks.forEach((track: any) => {
      if (!track.stopped) {
        track.stop()
        console.log("麦克风音轨已停止")
        // 从 PeerConn 移除音轨
        const sender = PeerConn.getSenders().find((s: any) => s.track === track)
        if (sender) {
          PeerConn.removeTrack(sender)
        }
      }
    })
    if (PeerConn) {
      PeerConn.close()
      PeerConn = null
    }
    if (DataChannel) {
      DataChannel.close()
      DataChannel = null
    }
    if (reVideo.srcObject) {
      reVideo.srcObject.getTracks().forEach((track: { stop: () => any }) => track.stop())
      reVideo.srcObject = null
    }
    // 清理媒体流引用
    mediaStream = null
  } else {
    console.warn("没有活跃的麦克风音轨可以关闭")
  }
}

// 设备唤起共计时间
export function deviceEvocationTime() {
  return receiveWakeUpTime - sendWakeUpTime
}

// 发送设备唤起时间
export function getSendWakeUpTime() {
  return sendWakeUpTime
}

// 获取RTCSessionDescription连接模式
export function getMode() {
  PeerConn.getStats(null)
    .then((stats: RTCStatsReport) => {
      const reports = stats.values()
      let selectedCandidatePair: RTCIceCandidatePairStats | null = null

      // 遍历所有统计报告以找到选定的候选对
      for (const report of reports) {
        if (report.type === "candidate-pair") {
          // 假设具有非空 roundTripTime 的候选对是选定的
          if (report.currentRoundTripTime !== null && report.currentRoundTripTime !== undefined) {
            selectedCandidatePair = report as RTCIceCandidatePairStats
            break
          }
        }
      }

      if (!selectedCandidatePair) {
        console.warn("未能找到选定的候选对")
        return
      }

      // 获取本地和远端候选信息
      const localCandidate = stats.get(selectedCandidatePair.localCandidateId)
      const remoteCandidate = stats.get(selectedCandidatePair.remoteCandidateId)

      if (localCandidate && remoteCandidate) {
        // 判断是否使用了 TURN 中继
        if (localCandidate.candidateType === "relay" || remoteCandidate.candidateType === "relay") {
          config.mode.value = "RELAY"
        } else {
          config.mode.value = "P2P"
        }
      } else {
        console.warn("未能获取到完整的候选信息")
      }
    })
    .catch((err: any) => {
      console.error("获取统计信息失败:", err)
    })
}

async function measureDownloadSpeed() {
  const report = await PeerConn.getStats()

  let bytesReceived = 0
  let timestamp = 0

  report.forEach((stat: any) => {
    if (stat.type === "inbound-rtp" && stat.bytesReceived !== undefined) {
      bytesReceived = stat.bytesReceived
      timestamp = stat.timestamp // 时间戳单位为毫秒
    }
    if ((stat.type === "inbound-rtp" || stat.type === "outbound-rtp") && stat.kind === "audio") {
      report.forEach((statReport: any) => {
        if (statReport.type === "codec" && statReport.id === stat.codecId) {
          config.device.attributes.audio_decoder.clockRate = String(
            statReport.clockRate / 1000 + "kHz"
          )
          config.device.attributes.audio_decoder.mimeType = String(statReport.mimeType)
        }
      })
      if (stat.packetsReceived) {
        config.device.attributes.audio_decoder.packetsReceived = stat.packetsReceived //收到音频包总数
      }
      if (stat.packetsLost) {
        config.device.attributes.audio_decoder.packetsLost = stat.packetsLost //丢失音频包总数
      }
    }
    if ((stat.type === "inbound-rtp" || stat.type === "outbound-rtp") && stat.kind === "video") {
      if (stat.packetsReceived) {
        config.device.attributes.video_decoder.packetsReceived = stat.packetsReceived //收到视频包总数
      }
      if (stat.packetsLost) {
        config.device.attributes.video_decoder.packetsLost = stat.packetsLost //丢失视频包总数
      }
      // 获取帧率
      if (stat.frameRateMean) {
        config.device.attributes.video_decoder.clockRate = stat.frameRateMean
      } else if (stat.framesPerSecond) {
        config.device.attributes.video_decoder.clockRate = stat.framesPerSecond
      }

      if (stat.codecId) {
        report.forEach((statReport: any) => {
          if (statReport.type === "codec" && statReport.id === stat.codecId) {
            if (statReport.mimeType) {
              config.device.attributes.video_decoder.mimeType = statReport.mimeType
            }
          }
        })
      }
    }
  })

  if (lastBytesReceived && lastTime && timestamp && bytesReceived >= lastBytesReceived) {
    const deltaTime = (timestamp - lastTime) / 1000 // 秒
    const deltaBytes = bytesReceived - lastBytesReceived
    const speedMbps = Math.floor(((deltaBytes * 8) / deltaTime / (1024 * 1024)) * 100)

    config.device.attributes.speed_mbps = speedMbps
  }
  lastBytesReceived = bytesReceived
  lastTime = timestamp
}

function startSpeedMonitoring() {
  if (speedInterval !== null) return // 防止重复启动

  lastBytesReceived = 0
  lastTime = 0
  speedInterval = null
  config.device.attributes.audio_decoder = {
    clockRate: "",
    mimeType: "",
    packetsReceived: 0,
    packetsLost: 0,
  }
  config.device.attributes.video_decoder = {
    clockRate: "",
    mimeType: "",
    packetsReceived: 0,
    packetsLost: 0,
  }
  // 启动定时器，每秒测量一次
  const loop = () => {
    measureDownloadSpeed()
    speedInterval = window.setTimeout(loop, 3000)
  }

  loop()
}

export function stopSpeedMonitoring() {
  if (speedInterval !== null) {
    clearTimeout(speedInterval)
    speedInterval = null
    console.log("已停止下行速度监控")
  }
}

function clearLoadingTime() {
  clearTimeout(loadingTime)
  loadingTime = null
}

//断开mqtt
export function disconnectMqtt() {
  if(MQTTClient){
    MQTTClient.disconnect()
    MQTTClient = null
  }
}

function checkLoading() {
  clearLoadingTime()
  loadingTime = setTimeout(() => {
    if (isLoading.value) {
      closeToast()
      showToast("设置失败,请稍后再试")
    }
  }, 10000)
}
