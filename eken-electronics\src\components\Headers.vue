<template>
  <div class="container-top-pc">
    <div class="nav-box-pc">
      <div class="nav-left" @click="nav('/', 1)">
        <img src="../assets/logo.png" class="nav-icon-title" alt="" />
        <div class="nav-title">EKEN ELECTRONICS LIMITED</div>
      </div>
      <div ref="navCenterRef" class="nav-center">
        <!-- <div class="nav-item" @click="nav('/', 1)" :class="navActive === 1 ? 'active' : ''">
          Home
        </div> -->
        <div
          class="nav-item"
          :class="navActive === 2 ? 'active' : ''"
          @click="nav('/SecurityReporting', 2, $event.target)"
        >
          Vulnerability Disclosure
        </div>
        <div
          class="nav-item"
          :class="navActive === 3 ? 'active' : ''"
          @click="nav('/SoftwareUpdates', 3, $event.target)"
        >
          Software Updates
        </div>
        <div
          class="nav-item"
          :class="navActive === 4 ? 'active' : ''"
          @click="nav('/', 4, $event.target)"
        >
          <a
            class="a-item"
            :class="navActive === 4 ? 'active' : ''"
            href="mailto:<EMAIL>"
            rel="noopener noreferrer"
            target="_blank"
          >
            Contact Us
          </a>
        </div>
        <div v-if="navActive" class="indicator" :style="indicatorStyle"></div>
      </div>

      <div class="nav-right">
        <div class="nav-item" @click="nav('/login', 5)">Log In</div>
        <div v-if="loginStatus" class="nav-item" @click="logout">Log out</div>
      </div>
    </div>
  </div>
  <div class="container-top">
    <div class="nav-box">
      <div class="top-container">
        <div class="nav-left" @click="nav('/', 1)">
          <img src="../assets/logo.png" class="nav-icon-title" alt="" />
          <div class="nav-title">EKEN ELECTRONICS LIMITED</div>
        </div>
        <div class="svg-menu" @click="showPopup(true)">
          <img src="../assets/hamburger_menu.svg" class="nav-icon" alt="" />
        </div>
      </div>
    </div>

    <van-popup
      v-model:show="show"
      position="right"
      class="popup-container"
      :style="{ width: '75%', height: '100%', background: '#fff' }"
    >
      <div class="close-icon-container">
        <img
          src="../assets/close_black.png"
          class="close-icon"
          @click="showPopup(false)"
          alt=""
        />
      </div>
      <div class="popup-list">
        <div class="nav-item" @click="nav('/', 1)">
          <span :class="navActive === 1 ? 'active' : ''">Home</span>
        </div>
        <div class="nav-item" @click="nav('/SecurityReporting', 2)">
          <span :class="navActive === 2 ? 'active' : ''"
            >Vulnerability Disclosure</span
          >
        </div>
        <div class="nav-item" @click="nav('/SoftwareUpdates', 3)">
          <span :class="navActive === 3 ? 'active' : ''">Software Updates</span>
        </div>
        <div class="nav-item" @click="nav('/', 4)">
          <span :class="navActive === 4 ? 'active' : ''">Contact Us</span>
        </div>
        <div class="nav-item" @click="nav('/login', 5)">
          <span :class="navActive === 5 ? 'active' : ''">Log In</span>
        </div>
        <div v-if="loginStatus" class="nav-item" @click="logout">Log out</div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";

const porops = defineProps({
  index: {
    type: Number,
    default: 1,
  },
});

const navCenterRef = ref();

const navActive = ref<number>(porops.index);
const router = useRouter();

const indicatorWidth = ref<string | number>(0);
const indicatorLeft = ref<string | number>(0);

const nav = (url: string, index: number, target?: any) => {
  if (navActive.value === index) return;
  navActive.value = index;
  if (target) {
    const rect = target.getBoundingClientRect();
    if ([2, 3, 4].includes(index)) {
      const { left } = navCenterRef.value.getBoundingClientRect();
      indicatorWidth.value = `${rect.width + 30}px`;
      indicatorLeft.value = `${rect.left - left - 15}px`;
    }
  } else {
    indicatorWidth.value = `${0}px`;
  }
  showPopup(false);
  router.push(url);
  localStorage.setItem("active", String(index));
  if (index === 4) {
    setTimeout(() => {
      window.scrollTo({
        top: 9999999,
        behavior: "smooth",
      });
    }, 200);
  }
};

const indicatorStyle: any = computed(() => {
  return {
    width: indicatorWidth.value,
    left: indicatorLeft.value,
    background: "linear-gradient(20deg, #411980, #e91c24)",
    borderRadius: "30px",
    height: "34px",
    position: "absolute",
    top: "0",
    bottom: "0",
    margin: "auto",
    transition: "all 0.3s ease-in-out",
  };
});
const loginStatus = ref<boolean>();
loginStatus.value = localStorage.getItem("isLogin") === "true";
const logout = () => {
  localStorage.clear();
  loginStatus.value = localStorage.getItem("isLogin") === "true";
  router.push("/login");
};

const show = ref(false);
const showPopup = (bol: boolean) => {
  show.value = bol;
};

onMounted(() => {
  if (
    localStorage.getItem("active") &&
    localStorage.getItem("active") === "4"
  ) {
    navActive.value = 4;
  }
});
</script>

<style lang="less" scoped>
.container-top-pc {
  width: 100%;
  background-color: #fff;
  padding: 58px 0;
  display: flex;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  .nav-box-pc {
    width: 80%;
    margin: 0 auto;
    margin-left: 300px;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    .nav-left {
      cursor: pointer;
      margin-right: -100px;
      .nav-title {
          display: inline-block;
          font-size: 22px;
          font-weight: bold;
          color: #2F3541;
          cursor: pointer;
        }
      img {
        width: 32px;
        height: 32px;
        margin-right: 10px;
        vertical-align: text-bottom;
      }
    }
  }
  .nav-center {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 60px;
    position: relative;
    padding: 10px 23px;
    border-radius: 30px;
    .nav-item {
      font-size: 18px;
      color: #2F3541;
      cursor: pointer;
      flex-shrink: 0;
      text-align: left;
      z-index: 1;
      transition: all ease-in-out 0.3s;
      .a-item {
        all: unset;
        font: inherit;
      }
    }
    .active {
      color: rgba(255, 255, 255, 0.8);
    }
  }
  .nav-right {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;

    .nav-item {
      font-size: 18px;
      color: #2F3541;
      cursor: pointer;
      flex-shrink: 0;
      text-align: left;
    }
  }
}

.container-top {
  width: 100%;
  background-color: #fff;
  padding: 38px 0;
  display: flex;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  .svg-menu {
    display: flex;
    align-items: center;
    .nav-icon {
      width: 32px;
      height: 32px;
    }
  }

  .nav-box {
    width: 93%;
    margin: 0 auto;
    margin-left: 16px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;

    .top-container {
      width: 100vw;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .nav-left {
        .nav-title {
          display: inline-block;
          font-size: 20px;
          font-weight: bold;
          color: #2F3541;
          cursor: pointer;
        }
        img {
          width: 32px;
          height: 32px;
          margin-right: 10px;
          vertical-align: text-bottom;
        }
      }
      .nav-right {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 10px;
        margin-left: auto;

        .nav-item {
          font-size: 16px;
          color: #2F3541;
          cursor: pointer;
          flex-shrink: 0;
          text-align: left;
        }
      }
    }
  }
  .popup-container {
    padding: 30px;
    overflow: hidden;
    .close-icon-container {
      display: flex;
      .close-icon {
        width: 32px;
        height: 32px;
        margin-left: auto;
      }
    }

    .popup-list {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 20px;

      .nav-item {
        font-size: 22px;
        color: #2F3541;
        cursor: pointer;
        flex-shrink: 0;
        text-align: left;
        font-weight: 600;
        .a-item {
          color: #2F3541;
        }
      }
      .active {
        color: #2F3541;
        // border-bottom: 3px solid #3d7aff;
        border-bottom: 3px solid #2F3541;
        padding-bottom: 5px;
      }
    }
  }
}

@media screen and (max-width: 1025px) {
  .container-top {
    display: block;
  }
  .container-top-pc {
    display: none;
  }
}
@media (min-width: 1025px) {
  .container-top {
    display: none;
  }
  .container-top-pc {
    display: block;
  }
}

@media (min-width: 100px) and (max-width: 500px) {
  .nav-left {
    font-size: 18px !important;
  }
}
</style>
