import OSS from "ali-oss"
import { getOssKey } from "@/api/index"

let ossSTS: any
let ossSTSChat: any

// 从后端服务获取 STS 临时访问凭证，这个让后端照着阿里云的文档写，拿到一些keyid和临时秘钥啥的
async function getOSSSTS(endpoint: string) {
  const res: any = await getOssKey(endpoint)
  if (res.code === 0) {
    // 设置保存时间为一小时
    const expires: any = Date.now() + 3000
    localStorage.setItem("timeKey", expires)
    localStorage.setItem("ossSTS", JSON.stringify(res.data))
    return res.data
  }
}
async function getOSSSTSChat() {
  const res:any = await getOssKey('https://oss-ap-southeast-1.aliyuncs.com')
  if (res.code === 0) {
    // 设置保存时间为一小时
    const expires:any = Date.now() + 1000 * 60 * 60;
    localStorage.setItem('timeKeyChat',expires);
    localStorage.setItem('ossSTSChat',JSON.stringify(res.data));
    return res.data;
  }
}

export async function get_oss_info() {
  if (!localStorage.getItem("timeKeyChat")) {
    ossSTSChat = await getOSSSTSChat()
  }
  // 读取并检查是否过期
  const time: any = Number(localStorage.getItem("timeKeyChat"))
  if (time < Date.now()) {
    localStorage.removeItem("timeKeyChat")
    localStorage.removeItem("ossSTSChat")
    ossSTSChat = await getOSSSTSChat()
  }
  if (ossSTSChat == undefined) {
    const day: any = localStorage.getItem("ossSTSChat")
    ossSTSChat = JSON.parse(day)
  }
  return ossSTSChat
}

export async function authTemps(bucket: string, endpoint: string) {
  if (!localStorage.getItem("timeKey")) {
    ossSTS = await getOSSSTS(endpoint)
  }
  // 读取并检查是否过期
  const time: any = Number(localStorage.getItem("timeKey"))
  if (time < Date.now()) {
    localStorage.removeItem("timeKey")
    localStorage.removeItem("ossSTS")
    ossSTS = await getOSSSTS(endpoint)
  }
  if (ossSTS == undefined) {
    const day: any = localStorage.getItem("ossSTS")
    ossSTS = JSON.parse(day)
  }
  let str: any = getValueAfterDoubleSlash(endpoint)
  return new OSS({
    accessKeyId: ossSTS.AccessKeyId,
    accessKeySecret: ossSTS.AccessKeySecret,
    bucket: bucket,
    stsToken: ossSTS.SecurityToken,
    region: str,
  })
}

// 生成 M3U8 文件及其分段 TS 文件的签名 URL
async function getSignedUrls(client: any, fileName: string) {
  try {
    const m3u8Url = await client.signatureUrl(fileName, { expires: 3600 }) // 设置有效期为1小时
    const response = await fetch(m3u8Url)
    const m3u8Content = await response.text()

    const urls = []
    const lines = m3u8Content.split("\n")
    for (let line of lines) {
      line = line.trim()
      if (line.endsWith(".ts")) {
        const tsUrl = await client.signatureUrl(line, { expires: 3600 }) // 设置有效期为1小时
        urls.push(tsUrl)
      }
    }

    return { m3u8Url, tsUrls: urls }
  } catch (error) {
    console.error("Error generating signed URLs:", error)
    return null
  }
}

// 修改 M3U8 文件中的 TS 文件链接为签名 URL
async function modifyM3U8Content(client: string, fileName: string) {
  const { m3u8Url, tsUrls }:any = await getSignedUrls(client, fileName)
  const response = await fetch(m3u8Url)
  const m3u8Content = await response.text()

  let modifiedContent = ""
  const lines = m3u8Content.split("\n")
  let index = 0
  for (let line of lines) {
    line = line.trim()
    if (line.endsWith(".ts")) {
      modifiedContent += tsUrls[index] + "\n"
      index++
    } else {
      modifiedContent += line + "\n"
    }
  }

  return modifiedContent
}

// 过滤提取下载视频需要endpoint
function getValueAfterDoubleSlash(str: string) {
  const regex = /\/\/(.*?)\./
  const match = str.match(regex)
  return match ? match[1].trim() : null
}

// 上传文件到阿里云 OSS
export async function downloadFile(
  bucket: string,
  endpoint: string,
  fileName: string
) {
  const client = await authTemps(bucket, endpoint)
  return modifyM3U8Content(client, fileName)
}