import { ref } from "vue"

export default {
  // APP跳转到h5的必要参数
  sessionId: "",
  device_sn: "",
  from_name: "",
  app_version: "",
  app_name: "",
  src: 0,

  // websocket连接是否断开参数
  websockStatus: 0,

  scrollNum: 0, //滚动次数

  albumType: 1, //区分哪里点了相册
  albumImg: ref(""),
  msg_id: 0,
  showBottom: ref(-1),

  // 控制是否要滚动到底部
  scrollStatus: true,

  // 打包变量
  // HOST_URL: "/api", //本地
  HOST_URL: '/', //线上环境
}
