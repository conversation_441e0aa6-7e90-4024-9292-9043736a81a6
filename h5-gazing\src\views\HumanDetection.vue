<template>
  <div class="pages">
    <Header :title="config.device.nickname" :left-type="1">
      <div class="title-edit" v-if="config.device.is_primary" @click="saveVolume">保存</div>
    </Header>
    <div class="lamplight">
      <div class="lamplight-title">
        <div class="lamplight-title-left">人形侦测</div>
        <van-switch
          active-color="#6B30B7"
          v-model="detect"
          :disabled="!config.device.is_primary"
        />
      </div>
      <div class="lamplight-tip">人形侦测检测到人形即触发报警通知，并录影。</div>
      <div class="lamplight-data">
        <div class="proen-box">
          <img class="proen-img" src="https://cache.gdxp.com/acce/assets/facility/1.png" alt="" />
          <img class="proen-img" src="https://cache.gdxp.com/acce/assets/facility/2.png" alt="" />
          <img class="proen-img" src="https://cache.gdxp.com/acce/assets/facility/3.png" alt="" />
        </div>
        <van-slider
          vertical
          reverse
          v-model="lamplightValue"
          :disabled="!config.device.is_primary"
          bar-height="10px"
          @update:model-value="moveValue"
          :active-color="colors"
          inactive-color="#E7E7E7"
        />
        <div class="proen-item">
          <div class="proen-text">高</div>
          <div class="proen-text">中</div>
          <div class="proen-text">低</div>
        </div>
      </div>
      <div class="proen-data">
        <p>{{ lamplightText }}</p>
      </div>
    </div>
    <div class="footer-tip" v-if="!config.device.is_primary">
      <van-icon name="info" color="#FFCC33"/>
      <div class="footer-tip-text">只有管理者才有权限修改此设置</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import config from "@/config"
import Header from "@/components/Header.vue"
import { ref,onMounted } from "vue"
import { setProperty } from "@/components/WebRTCClient";

const detect = ref<boolean>(false) //人形侦测
const lamplightValue = ref<number>(0)
const lamplightText = ref<string>('低精度 - 设备将更频繁地录制视频。电池寿命最短。')
const colors = ref("#FF7E06")

//人形侦测
const moveValue = (value: number) => {
  setColor(value)
  if(value <= 33){
    lamplightText.value = '低精度 - 设备将更频繁地录制视频。电池寿命最短。'
  }else if(value > 33 && value < 64){
    lamplightText.value = '标准准确度 - 中等电池寿命。'
  }else {
    lamplightText.value = '高精度 - 设备录制视频的频率会降低。最大电池寿命。'
  }
}

const saveVolume = () => {
  setProperty([{category:'human_detection',value:lamplightValue.value},{category:'human_detection_state',value:detect.value?1:0}])
}

const setColor = (value:number)=>{
  if (value > 55 && value < 70) {
    colors.value = "linear-gradient(10deg, #FF7E06 0%,  #FF7E06 50%,#41CB99 90%)"
  } else if (value > 70) {
    colors.value = "linear-gradient(10deg, #FF7E06 0%,  #FF7E06 30%,#41CB99 70%)"
  } else {
    colors.value = "linear-gradient(10deg, #FF7E06 100%, #FF7E06 100%, #41CB99 0%)"
  }
}

onMounted(()=>{
  lamplightValue.value = Number(config.device.attributes.human_detection??0)
  setColor(lamplightValue.value)
  if(config.device.attributes.human_detection_state === 0){
    detect.value = false
  }else if(config.device.attributes.human_detection_state === 1) {
    detect.value = true
  }
})
</script>

<style lang="less" scoped>
.title-edit {
  font-size: 15px;
  color: #fff;
  background-color: #1ba674;
  padding: 3px 15px;
  border-radius: 20px;
}
.lamplight {
  background-color: #fff;
  border-radius: 12px;
  margin: 0 16px 30px 16px;
  padding: 10px;
  .lamplight-title {
    font-size: 24px;
    color: #222222;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    .lamplight-title-left {
      display: flex;
      align-items: center;
    }
  }
  .lamplight-tip {
    font-size: 15px;
    color: #999;
    padding-top: 5px;
    margin-bottom: 5vh;
  }
  .time-period {
    display: flex;
    height: 50px;
    justify-content: space-between;
    .period-left {
      width: 60%;
    }
    .period-img {
      width: 33px;
      height: 33px;
    }
    .period-right {
      display: flex;
      justify-content: space-between;
      width: 40%;
    }
  }
  .sliding {
    position: relative;
    .sliding-left {
      font-size: 12px;
      color: #fff;
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translate(0, -50%);
      z-index: 9;
    }
    .sliding-right {
      font-size: 12px;
      color: #666666;
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translate(0, -50%);
      z-index: 9;
    }
    .hua-box {
      position: relative;
      width: 34px;
      height: 34px;
      .hua-top {
        position: absolute;
        left: 50%;
        top: -15px;
        transform: translate(-50%);
        font-size: 12px;
        color: #4774f5;
      }
      .hua-img {
        width: 34px;
        height: 34px;
      }
    }
  }
  .intensity {
    height: 60px;
    display: flex;
    align-items: center;
    .intensity-item {
      display: flex;
      align-items: center;
      .intensity-text {
        font-size: 12px;
        color: #222222;
        margin-right: 5px;
      }
      .intensity-data {
        width: 23.5px;
        height: 24.8px;
        background-image: url("https://cache.gdxp.com/acce/assets/facility/qing.png");
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        color: #4774f5;
        position: relative;
        border-radius: 50%;
        .dian-mm {
          width: 8px;
          height: 18px;
          position: absolute;
          left: 50%;
          top: -20px;
          transform: translate(-50%);
        }
      }
    }
  }
  .lamplight-data {
    height: 35vh;
    display: flex;
    justify-content: center;
    position: relative;
    .proen-box {
      position: absolute;
      left: 37%;
      top: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
      .proen-img {
        width: 16px;
        height: 23.5px;
      }
    }
    .proen-item {
      position: absolute;
      right: 27%;
      top: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
      .proen-text {
        font-size: 13px;
        color: #666;
        font-weight: 400;
      }
    }
  }
  .proen-data {
    padding-top: 5vh;
    font-size: 13px;
    color: #666;
    line-height: 18px;
    text-align: center;
    p {
      margin-bottom: 5px;
    }
  }
}
.footer-tip{
  margin: 0 16px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #fff;
  background-color: #0D2759;
  border-radius: 20px;
  height: 24px;
  padding-left: 10px;
  .footer-tip-text{
    margin-left: 5px;
  }
}
</style>
