# eken-device-scanning项目

## 项目简介

基于vue3+vant+ts+vite适用于APP消息通知点击跳转的h5

## 项目文件

build - 打包更新后的index.html需要上传到Git给服务器更新
dist - 打包后的文件
node_modules - 第三方插件npm安装的包

    -  api - 调用服务器接口请求地址方法
    -  assets - 静态资源（图片）
    -  components - 封装的子组件
    -  config - APP传过来的参数（全局变量）

src - lang - 国际化多语言配置

    -  router - 项目页面路由
    -  utils - 全局封装的公共方法
    -  views - 项目页面文件
    -  app.vue - 项目根页面
    -  mian.ts - 生成根页面引入第三方插件

.gitattributes - git合并
.gitignore - git忽略上传文件
.prettierrc - 代码格式配置文件
env.d - 全局ts声明
index.html - 项目根index.html
vite.config.ts - 项目打包启动配置文件

## 启动网站

要在本地启动网站，您需要安装 [Node.js](https://nodejs.org/) 和 [Yarn](https://yarnpkg.com/)。你可以运行以下命令检查是否安装了这两个软件：

## 启动指令

```bash
npm i
npm run dev
```

或者

```bash
pnpm i
pnpm run dev
```

启动前提需要在APP.vue界面替换本地token、相关参数。
启动后，可在 `http://localhost:8899/` 进行实时浏览。
