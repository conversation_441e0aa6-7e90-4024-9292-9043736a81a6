<template>
  <div class="form-container">
    <van-form class="van-form-class" @submit="onSubmitSignIn">
      <h1 class="h1">Sign in</h1>
      <div>
        <van-field
          v-model="formData.email"
          label=""
          placeholder="Email"
          type="email"
          :rules="[{ required: true, message: 'Email is required!' }]"
        />
      </div>

      <van-field
        v-model="formData.password"
        label=""
        placeholder="password"
        type="password"
        :eye-see="true"
        :rules="[{ required: true, message: 'Password is required!' }]"
      />

      <van-button
        class="submit-button"
        type="primary"
        block
        :disabled="!formData.email || !formData.password"
        native-type="submit"
      >
        Sign in
      </van-button>
      <div class="forgot-create-container">
        <span class="text-button" @click="nav('/forgotPassword')"
          >Forgot password</span
        >
        <span class="text-button create-text" @click="nav('/createAccount')"
          >Create account</span
        >
      </div>
    </van-form>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import Headers from "@/components/Headers.vue";
import { Encrypt } from "@/utils/secret";
import { showToast } from "vant";
import { useRoute, useRouter } from "vue-router";
const router = useRouter();
const route = useRoute();
interface FormData {
  country: { name: string; code: string };
  email: string;
  emailCode: string;
  password: string;
  repeatPassword: string;
}

/**
 *  signType 0 注册 1 忘记密码 2 登录
 **/

const formData = ref<FormData>({
  country: { name: "", code: "" },
  email: "",
  emailCode: "",
  password: "",
  repeatPassword: "",
});

const nav = (url: string) => router.push(url);
// Submit
const onSubmitSignIn = async () => {
  const { email, password } = formData.value;
  const encryptedPassword = Encrypt(password);
  // Login
  const response = await fetch("https://amznew.ekengroup.com/api/login", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ email: email, pass_word: encryptedPassword }),
  });
  const { code, msg } = (await response.json()) as any;
  if (code == 0) {
    localStorage.setItem("isLogin", "true");
    showToast("Login successful");
    setTimeout(() => {
      router.replace("/");
    }, 1500);
  } else if (code == 88) {
    showToast("Login successful");
    setTimeout(() => {
      router.replace("/");
      window.open(msg, "_blank");
    }, 1500);
  } else {
    showToast(msg);
  }
};
</script>

<style lang="less" scoped>
.form-container {
  background: url(../assets/sign/signbg.png) no-repeat 100% / cover;
  position: relative;
  height: calc(100vh);
  background-color: rgb(246, 247, 250);
  overflow: hidden;
  padding-top: 130px;

  .van-form-class {
    h1 {
      color: black;
      text-align: center;
      padding: 0px 0 10px;
    }
    background-color: #fff;
    width: 400px;
    margin: 100px auto 0;
    padding: 30px 20px 30px;
    border-radius: 10px;
    overflow: hidden;
    .get-code-button {
      width: 90px;
    }
    .submit-button {
      margin-top: 30px;
      border-radius: 6px;
    }
  }

  .forgot-create-container {
    display: flex;
    justify-content: space-between;
    padding: 5px 10px;
    .text-button {
      color: #00000099;
      font-size: 13px;
      cursor: pointer;
    }
    .create-text {
      color: #1989fa;
    }
  }
  @media screen and (max-width: 550px) {
    .van-form-class {
      width: 90vw;
    }
  }
}
</style>
