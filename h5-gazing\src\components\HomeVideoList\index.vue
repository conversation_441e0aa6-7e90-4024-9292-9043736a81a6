<template>
  <div class="list-container">
    <VueDraggable
      v-model="deviceList"
      :animation="150"
      :scroll="true"
      :scroll-sensitivity="100"
      :scroll-speed="10"
      :touch-start-threshold="5"
      :force-fallback="false"
      :delay="300"
      :delay-on-touch-only="true"
      @end="onDragEnd"
    >
      <div class="wrapper" v-for="element in deviceList" :key="element.device_sn">
        <header>
          <div class="left-container">
            <div class="status-setting" @click.stop="deviceMsg(element)">
              <img class="status" v-if="element.img_url" :src="element.img_url" alt="" />
              <img class="status" v-else src="../../assets/device/offline.png" alt="" />
              <img class="setting" src="../../assets/home/<USER>/02.png" alt="" />
            </div>
            <span class="equipment-name">{{ element.nickname }}</span>
            <img
              class="battry-icon"
              v-if="element.attributes.battery_level > 100"
              src="../../assets/device/cd.png"
              alt=""
            />
            <img
              class="battry-icon breathing-animation"
              v-else-if="element.attributes.battery_level < 10"
              src="../../assets/device/1_9.png"
              alt=""
            />
            <img
              class="battry-icon breathing-animation"
              v-else-if="
                element.attributes.battery_level >= 10 && element.attributes.battery_level <= 20
              "
              src="../../assets/device/10_20.png"
              alt=""
            />
            <img
              class="battry-icon"
              v-else-if="
                element.attributes.battery_level <= 30 && element.attributes.battery_level >= 20
              "
              src="../../assets/device/20_30.png"
              alt=""
            />
            <img
              class="battry-icon"
              v-else-if="
                element.attributes.battery_level <= 50 && element.attributes.battery_level >= 30
              "
              src="../../assets/device/30_50.png"
              alt=""
            />
            <img
              class="battry-icon"
              v-else-if="
                element.attributes.battery_level <= 80 && element.attributes.battery_level >= 50
              "
              src="../../assets/device/50_80.png"
              alt=""
            />
            <img
              class="battry-icon"
              v-else-if="element.attributes.battery_level > 80"
              src="../../assets/device/100.png"
              alt=""
            />
            <span
              class="battry-percent"
              :class="element.attributes.battery_level <= 20 ? 'red-color' : ''"
            >
              {{
                element.attributes.battery_level > 100
                  ? element.attributes.battery_level - 256
                  : element.attributes.battery_level
              }}%
            </span>
            <van-icon
              name="warning-o"
              color="#FF0000"
              v-if="element.attributes.battery_level < 21"
              @click="openBatteryTip"
            />
          </div>
          <div class="right-container">
            <van-icon
              name="chat-o"
              :badge="element.unreadCount || ''"
              color="#34C88A"
              size="30"
              @click="customer(element)"
            />
            <img
              @click.stop="playback(element)"
              class="record-icon"
              src="../../assets/home/<USER>/05.png"
              alt=""
            />
          </div>
        </header>
        <main @click="details(element)">
          <img
            v-if="element.last_img_url"
            class="video-imgs"
            :src="decodeURIComponent(element.last_img_url)"
            alt=""
          />
          <img v-else class="video-imgs" src="../../assets/home/<USER>/video.jpg" alt="" />
          <div
            class="ota-box"
            v-if="element.ota_state && element.ota_state !== 16 && element.ota_state !== 18"
          >
            <div class="ota-text">
              {{ element.ota_state < 3 ? state.ota_status[element.ota_state] : "固件升级中" }}
              <div v-if="element.ota_progress >= 0">{{ element.ota_progress }}%</div>
            </div>
          </div>
          <img v-else class="center-button" src="../../assets/home/<USER>/06.png" alt="" />
          <img
            class="right-bottom-button"
            v-if="
              element.ota_item &&
              element.ota_item.download_url &&
              element.ota_state !== 16 &&
              element.ota_state !== 18 &&
              element.ota_progress <= 0
            "
            @click.stop="sendOtaUpdate(element.ota_item, element.product_key)"
            src="../../assets/home/<USER>/ota.png"
            alt=""
          />
        </main>
        <main
          class="cloud"
          v-if="element.need_subscribe === 1"
          @click="verify(element)"
        >
          <div class="cloud-left">
            <div class="cloud-title">Traffic package</div>
            <div class="cloud-text">
              <span class="dian"></span>
              monthly traffic package
            </div>
            <div class="cloud-text">
              <span class="dian"></span>
              Annual Traffic Package
            </div>
            <div class="cloud-text">
              <span class="dian"></span>
              VIP traffic package
            </div>
          </div>
          <div class="cloud-right">
            <div class="cloud-btn">Subscribe</div>
          </div>
        </main>
      </div>
    </VueDraggable>
  </div>
  <van-popup v-model:show="showBattery" round closeable :style="{ width: '70%' }">
    <div class="model-battery">
      <img class="battry-icon" src="../../assets/device/10_20.png" alt="" />
      <div class="model-battery-title">电量低</div>
      <div class="model-battery-text">电量低于10% pir自动关闭.</div>
      <div class="model-battery-text">电量大于20% 恢复开启.</div>
    </div>
  </van-popup>
</template>
<script setup lang="ts">
import { ref, computed, reactive, watch } from "vue"
import { showToast } from "vant"
import type { deviceData } from "@/api/base"
import { useRouter } from "vue-router"
import config from "@/config"
import { useHomeStore } from "@/stores/home"
import { sendOtaUpdate } from "@/components/WebRTCClient"
import { VueDraggable } from "vue-draggable-plus"
import { sort_device } from "@/api"

const homeStore = useHomeStore()
const router = useRouter()
const showBattery = ref<boolean>(false)
interface resultCodeType {
  [key: number]: string
}
const state = reactive({
  ota_status: {
    1: "更新准备",
    2: "更新准备失败",
    3: "下载更新包",
    4: "更新包下载完成",
    5: "更新包下载失败",
    6: "初始化分区信息",
    7: "初始化分区信息失败",
    8: "解压安装包",
    9: "解压安装包完成",
    10: "解压安装包失败",
    11: "固件校验失败",
    12: "安装固件中",
    13: "固件安装完成",
    14: "固件安装失败",
    15: "设置系统分区启动标志(A,B系统启动标志)",
    16: "linux更新完成",
    18: "mcu更新完成",
    19: "电量低失败",
  } as resultCodeType,
})

const details = (item: deviceData) => {
  if (item.ota_state && item.ota_state !== 16 && item.ota_state !== 18) {
    return
  }
  if (item.status === 0) {
    showToast("设备已离线，请稍后再试")
    return
  }

  config.product_key = item.product_key
  config.device_sn = item.device_sn
  config.device = item
  if (config.device.area === "US") {
    config.webrtc_item = config.webrtc_list.US
  } else if (config.device.area === "SG") {
    config.webrtc_item = config.webrtc_list.SG
  } else if (config.device.area === "DE") {
    config.webrtc_item = config.webrtc_list.DE
  } else if (config.device.area === "CN") {
    config.webrtc_item = config.webrtc_list.CN
  }
  config.wakeUp_type = 1
  config.device_config_item = config.device_config[item.project_id]
  router.push("/deviceDetails")
}

const verify = (item: deviceData) => {
  config.product_key = item.product_key
  config.device_sn = item.device_sn
  config.device = item
  config.device_config_item = config.device_config[item.project_id]
  if (config.device_config_item.is_4g_card === 1) {
    router.push("/applePay")
  } else {
    router.push("/cloudStorageBuy")
  }
}

const deviceMsg = (item: deviceData) => {
  config.product_key = item.product_key
  config.device_sn = item.device_sn
  config.device = item
  config.light_leve.value = config.device.attributes.light_leve
  config.device_config_item = config.device_config[item.project_id]
  router.push({
    path: "/deviceInfo",
    query: {
      device_status: item.status,
    },
  })
}

const playback = (item: deviceData) => {
  config.product_key = item.product_key
  config.device_sn = item.device_sn
  config.device = item
  config.device_config_item = config.device_config[item.project_id]
  router.push("/playback")
}

const customer = (item: deviceData) => {
  config.product_key = item.product_key
  config.device_sn = item.device_sn
  config.device = item
  config.light_leve.value = config.device.attributes.light_leve
  config.device_config_item = config.device_config[item.project_id]
  router.push({
    path: "/customer",
    query: {
      device_status: item.status,
    },
  })
}

const openBatteryTip = () => {
  showBattery.value = true
}

const onDragEnd = (event: any) => {
  const { oldIndex, newIndex } = event
  if (oldIndex !== undefined && newIndex !== undefined) {
    const updatedList = deviceList.value.map((item, index) => ({
      ...item,
      sort: index + 1,
    }))
    deviceList.value = updatedList
    sortDevice()
  }
}

const sortDevice = () => {
  sort_device(deviceList.value)
}

const deviceList = computed({
  get: () => homeStore.state.deviceList,
  set: newValue => {
    homeStore.setDeviceList(newValue)
  },
})
</script>
<style lang="less" scoped>
@keyframes breathing {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.list-container {
  padding-bottom: 100px;
  .wrapper {
    padding: 0 16px;
    border-radius: 20px;
    overflow: hidden;
    background-color: #f2f1f3;
    margin-bottom: 12px;
    header {
      height: 54px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 14px;
      background-color: #fff;
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;
      .left-container {
        display: flex;
        align-items: center;
        vertical-align: middle;
        .status-setting {
          position: relative;
          .status {
            width: 38px;
            height: 38px;
            border: 0.5px solid #e3e3e3;
            border-radius: 50%;
            padding: 3px;
            box-sizing: border-box;
          }
          .setting {
            width: 18px;
            height: 18px;
            position: absolute;
            top: 0;
            left: 25px;
          }
        }
        .equipment-name {
          margin: 0 5px;
          font-size: 15px;
          font-weight: 400;
          line-height: 21px;
          text-align: center;
        }
        .battry-icon {
          width: 26px;
          height: 12px;
          margin: 0 5px;
        }
        .breathing-animation {
          animation: breathing 1s ease-in-out infinite;
        }
        .battry-percent {
          font-size: 13px;
          text-align: center;
          margin-right: 5px;
        }
        .red-color {
          color: red;
        }
      }
      .right-container {
        display: flex;
        align-items: center;
        .record-icon {
          width: 30px;
          height: 30px;
          margin-left: 15px;
        }
      }
    }
    main {
      background-color: dimgrey;
      height: 206px;
      position: relative;
      .video-imgs {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
      }
      .center-button {
        width: 50px;
        height: 50px;
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        margin: auto;
        z-index: 9;
      }
      .right-bottom-button {
        width: 41px;
        height: 41px;
        position: absolute;
        bottom: 27px;
        right: 15px;
      }
      .ota-box {
        padding: 0 15px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 9;
        font-size: 16px;
        line-height: 16px;
        color: #fff;
        background-color: rgba(000, 000, 000, 0.6);
        border-radius: 20px;
        height: 50px;
        display: flex;
        align-items: center;
      }
    }
    .cloud {
      height: 116px;
      background: linear-gradient(90deg, #235e49 0%, #3abd80 100%);
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom-left-radius: 12px;
      border-bottom-right-radius: 12px;
    }
    .cloud-left {
      padding-left: 16px;
    }
    .cloud-right {
      display: flex;
      align-items: center;
      padding-right: 16px;
    }
    .cloud-title {
      font-size: 16px;
      color: #fff;
      margin-bottom: 5px;
    }
    .cloud-text {
      font-size: 12px;
      color: #fff;
      line-height: 20px;
    }
    .dian {
      display: inline-block;
      width: 8px;
      height: 8px;
      background-color: rgba(255, 255, 255, 0.5);
      border-radius: 50%;
      margin-right: 5px;
    }
    .cloud-btn {
      width: 95px;
      height: 32px;
      background: linear-gradient(180deg, #ff9900 0%, #ff7b00 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #fff;
      font-weight: 600;
      border-radius: 19px;
    }
  }
}
.model-battery {
  padding: 15px;
  .battry-icon {
    width: 26px;
    height: 12px;
    margin-bottom: 10px;
  }
  .model-battery-title {
    font-size: 16px;
    color: 000;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .model-battery-text {
    font-size: 13px;
    color: #999;
    line-height: 18px;
  }
}
</style>
