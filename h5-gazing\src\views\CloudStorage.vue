<template>
  <div class="pages">
    <Header title="云存储" :left-type="1" />
    <div class="cloud">
      <div class="cloud-box">
        <img class="cloud-img" src="../assets/device/no-fam.png" alt="套餐" />
        <div class="cloud-title">Cloud Storage Basic</div>
        <div class="cloud-text">
          <img class="cloud-time-img" src="../assets/device/muqian.png" alt="" />
          <span class="cloud-num">{{ 0 }}</span>
          <span class="div">days of playback records</span>
        </div>
      </div>
      <div class="cloud-list">
        <div class="cloud-title">
          {{ is_family === 1 ? "PLUS特权" : "BASIC特权" }}
        </div>
        <div class="cloud-item">
          <img
            v-if="is_family === 1"
            class="cloud-item-img"
            src="../assets/device/13.png"
            alt=""
          />
          <img v-else class="cloud-item-img" src="../assets/device/9.png" alt="" />
          Video Review
        </div>
        <div class="cloud-item">
          <img
            v-if="is_family === 1"
            class="cloud-item-img"
            src="../assets/device/14.png"
            alt=""
          />
          <img v-else class="cloud-item-img" src="../assets/device/10.png" alt="" />
          Loop Recording
        </div>
        <div class="cloud-item">
          <img
            v-if="is_family === 1"
            class="cloud-item-img"
            src="../assets/device/15.png"
            alt=""
          />
          <img v-else class="cloud-item-img" src="../assets/device/11.png" alt="" />
          Video Download
        </div>
        <div class="cloud-item">
          <img
            v-if="is_family === 1"
            class="cloud-item-img"
            src="../assets/device/16.png"
            alt=""
          />
          <img v-else class="cloud-item-img" src="../assets/device/12.png" alt="" />
          Unlimited storage
        </div>
        <div class="cloud-item" v-if="is_family === 1">
          <img class="cloud-item-img" src="../assets/device/17.png" alt="" />
          <div>
            Available for multiple devices
            <div class="div">All features for up to 9 of your doorbells and security cameras.</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import config from "@/config"
import Header from "@/components/Header.vue"

const is_family = ref<number>(0)
</script>

<style lang="less" scoped>
.cloud {
  background-color: #fff;
  border-radius: 8px;
  margin: 0 16px;
  .cloud-box {
    min-height: 167px;
    position: relative;
    padding: 10px;
    .cloud-img {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
    }
    .cloud-title {
      position: relative;
      z-index: 9;
      font-size: 23px;
      color: #072e6a;
      margin-bottom: 10px;
      padding-top: 10px;
    }
    .cloud-text {
      position: absolute;
      left: 25px;
      bottom: 27px;
      display: flex;
      align-items: flex-end;
      color: #fff;
      .cloud-num {
        font-size: 28px;
        line-height: 23px;
      }
      .div {
        font-size: 16px;
        margin-left: 5px;
      }
      .cloud-time-img {
        width: 26px;
        height: 26px;
        margin-right: 5px;
      }
    }
  }
  .cloud-list {
    padding: 16px;
    .cloud-title {
      font-size: 20px;
      color: #0b316f;
      margin-bottom: 10px;
    }
    .cloud-item {
      height: 45px;
      display: flex;
      align-items: center;
      font-size: 16px;
      .cloud-item-img {
        width: 32px;
        height: 32px;
        margin-right: 5px;
      }
      .div {
        padding-top: 5px;
        font-size: 12px;
        color: #8aa6d2;
      }
    }
  }
}
</style>
