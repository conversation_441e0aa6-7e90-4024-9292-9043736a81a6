import { fileURLToPath, URL } from "node:url"
import { defineConfig, loadEnv } from "vite"
import vue from "@vitejs/plugin-vue"

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "")
  return {
    plugins: [vue()],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },
    base: env.BASE_ENV,
    server: {
      host: "0.0.0.0",
      port: 5178,
      proxy: {
        [env.VITE_APP_BASE_API]: {
          target: env.VITE_SERVER,
          changeOrigin: true,
          rewrite: path => path.replace(new RegExp("^" + (env.VITE_APP_BASE_API as string)), ""),
        },
      },
    },
    build: {
      assetsDir: "assets_1.0.4",
      target: "es2015",
      chunkSizeWarningLimit: 1500,
    },
  }
})
