<template>
  <div class="pages">
    <Header title="二维码分享" :left-type="1" />
  <div class="share">
    <div class="share-code">
      <div class="share-code-title">
        <img class="share-device" src="../assets/device/share-device.png" alt=""/>
        <span class="share-address">{{ config.device.nickname }}</span>
      </div>
      <div class="share-code-img-box">
        <div ref="shareCodeBorder" class="share-code-border">
          <div class="share-code-img" :style="{width: qrcodeSize + 'px', height: qrcodeSize + 'px'}">
            <qrcode-vue v-if="qrData" :value="qrData" :size="qrcodeSize"></qrcode-vue>
            <!-- <img
              v-if="qrData"
              class="logo-image"
              src="https://cache.gdxp.com/acce/assets/facility/logo.png"
              alt=""
            /> -->
          </div>
        </div>
      </div>
      <div class="share-text">
        在新用户的手机上打开Camtro应用程序。在主页上,点击右上角,然后扫描上面的二维码。
      </div>
      <div class="save-btn" @click="screenshot('保存成功')">
        <div class="connect-save">
          <img class="save-img" src="../assets/device/connect-save.png" alt=""/>
        </div>
        <div class="save-text">保存</div>
      </div>
    </div>
  </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref } from "vue"
import { type shareData } from "@/api/base"
import config from "@/config"
import Header from "@/components/Header.vue"
import QrcodeVue from "qrcode.vue"
import { get_share_qrkey, device_share_list } from "@/api/index"
import { screenshot } from "@/utils"

const qrData = ref<string>("") // 要编码为二维码的数据
const state = reactive({
  shareList: [] as shareData[],
})
const shareCodeBorder = ref<HTMLDivElement | null>(null)
const qrcodeSize = ref(0)

const fetch = () => {
  device_share_list(config.product_key, config.device_sn).then((res: any) => {
    if (res.code === 0) {
      state.shareList = res.data || []
    }
  })
}

const shareCode = () => {
  get_share_qrkey(config.product_key, config.device_sn).then((res: any) => {
    if (res.data) {
      qrData.value = res.data.qr_key
    }
    fetch()
  })
}

onMounted(() => {
  shareCode()

  // 设置二维码宽高度
  if (shareCodeBorder.value) {
    let rectInfo = shareCodeBorder.value.getBoundingClientRect()
    qrcodeSize.value = rectInfo.width * 1 - 20
  } else {
    qrcodeSize.value = 205
  }
})
</script>

<style lang="less" scoped>
.share {
  padding: 20px 18px;
  .share-code {
    padding: 20px 0;
    border-radius: 8px;
    margin-bottom: 10px;
    .share-code-title {
      margin-bottom: 35px;
      padding: 0 30px;
      display: flex;
      align-items: center;
      .share-device {
        width: 40px;
        height: 40px;
        margin-right: 15px;
      }
      .share-address {
        font-size: 26px;
        font-weight: 700;
        color: #333333;
      }
    }
    .share-code-img-box {
      padding: 0 30px;
      .share-code-border {
        width: 100%;
        min-height: 230px;
        padding: 10px;
        border-radius: 20px;
        box-sizing: border-box;
        background-color: #fff;
        .share-code-img {
          position: relative;
          min-height: 205px;
          display:flex;
          justify-content:center;
          .logo-image {
            position: absolute;
            left: 50%;
            top: 50%;
            width: 46px;
            height: 46px;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
    .share-text {
      padding-top: 80px;
      font-size: 14px;
      color: #000;
      line-height: 18px;
    }
    @save-btn-width: 90px;
    .save-btn {
      width: @save-btn-width;
      margin: 50px auto 0 auto;
      .connect-save {
        width: @save-btn-width;
        height: @save-btn-width;
        background: #4774F5;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        .save-img {
          width: 30%;
          height: 30%;
        }
      }
      .save-text {
        margin-top: 10px;
        text-align: center;
      }
    }
  }
}
</style>
