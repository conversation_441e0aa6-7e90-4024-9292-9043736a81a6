<template>
  <div class="pages">
    <div class="ios-top"></div>
    <div class="pages-title">
      <div class="title-text">Chat</div>
      <van-icon name="cross" size="25" color="#222" @click="click_dismiss" />
    </div>
    <div class="tip-box">
      <div class="tip-text">Learn more about birds.Ask and uncover their</div>
      <div class="tip-text">secrets!</div>
    </div>
    <div class="chat-list" ref="chatData">
      <div class="item-left">
        <div class="item-box">
          <img class="item-left-img" src="../assets/left.png" alt="" />
          <div class="item-data">
            <img
              class="item-data-img"
              v-for="item in state.imageList"
              :key="item"
              :src="item"
              alt=""
            />
          </div>
        </div>
        <div class="item-data-title">
          <div class="tip-text">Choose a question to unlock your daily</div>
          <div class="tip-text">bird insight.</div>
        </div>
      </div>
      <div v-for="(item, index) in state.chatList" :key="index">
        <div class="item-right" v-if="item.screen === 0">
          <p class="text-p">{{ item.question }}</p>
        </div>
        <div class="item-left" v-else>
          <div class="item-box">
            <img class="item-left-img" src="../assets/left.png" alt="" />
            <p class="text-p" v-html="item.question"></p>
          </div>
        </div>
      </div>
    </div>
    <div class="pages-footer">
      <div class="footer-title">Pick a question</div>
      <div class="footer-list">
        <div
          class="list-item"
          @click="clickCaht(item)"
          v-for="item in state.questionList"
          :key="item.id"
        >
          <div class="item-text">{{ item.question }}</div>
          <!-- <span class="tip-odd">FUN</span> -->
        </div>
        <div class="last-item" @click="lastChat">
          <div class="last-text">+ See more questions</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { get_questions, submit_question, get_images } from "@/api"
import { ref, reactive, onMounted, nextTick } from "vue"
import type { chatData, imgListData, imagesData } from "@/api/base"
import config from "@/config"
import { useRouter } from "vue-router"
import { click_dismiss } from "@/utils"
import { uploadToOSS } from "@/oss"
import { md5 } from "js-md5"
import { showLoadingToast, closeToast } from "vant"

const router = useRouter()
const state = reactive({
  questionList: [] as chatData[],
  chatList: [] as chatData[],
  mdl: {} as imgListData,
  imageList: [] as unknown as [any],
})
const chatData = ref<HTMLElement | null>(null)

const lastChat = () => {
  router.push("/chatList")
}

const clickCaht = (item: chatData) => {
  item.screen = 0
  state.chatList.push(item)
  submitQuestion(item.id)
}

// 滚动到底部的方法
const scrollToBottom = () => {
  nextTick(() => {
    if (chatData.value) {
      chatData.value.scrollTop = chatData.value.scrollHeight
    }
  })
}

const submitQuestion = (id: number) => {
  showLoadingToast({
    message: "loading...",
    forbidClick: true,
  })
  submit_question(
    config.session_id,
    config.card_id,
    id,
    md5(`${config.session_id + config.key}`)
  ).then((res: any) => {
    closeToast()
    if (res.resultCode === 0) {
      if (res.content && res.content.answer && res.content.answer.answer) {
        state.chatList.push({
          question: res.content.answer.answer,
          screen: 1,
          id: res.content.answer.id,
        })
        scrollToBottom()
      }
    }
  })
}

const fetch = () => {
  get_questions(0).then((res: any) => {
    if (res.resultCode === 0) {
      state.questionList = res.content.questions
    }
  })
}

const getImageList = () => {
  get_images(
    config.card_id,
    config.session_id,
    md5(`${config.card_id}/${config.session_id + config.key}`)
  ).then((res: any) => {
    if (res.resultCode === 0) {
      state.mdl = res.content
      if (res.content && res.content.images && res.content.images.length) {
        res.content.images.forEach((item: imagesData) => {
          if (item.url) {
            loadImage(res.content.bucket, res.content.endpoint, item.url)
          }
        })
      }
    }
  })
}

async function loadImage(bucket: string, endpoint: string, url: string) {
  try {
    const imageUrl = await uploadToOSS(bucket, endpoint, url)
    state.imageList.push(imageUrl)
  } catch (error) {
    console.error("Failed to load image:", error)
  }
}

onMounted(() => {
  fetch()
  getImageList()
  if (config.chatItem.id) {
    state.chatList.push(config.chatItem)
    submitQuestion(config.chatItem.id)
  }
})
</script>

<style lang="less" scoped>
.pages {
  background-color: #fff3dc;
  height: 100vh;
  .ios-top {
    height: 40px;
  }
  .pages-title {
    display: flex;
    justify-content: space-between;
    padding: 0 16px;
    margin-bottom: 10px;
    .title-text {
      font-size: 26px;
      color: #222;
    }
  }
  .tip-box {
    padding: 0 16px;
    margin-bottom: 15px;
  }
  .tip-text {
    font-size: 13px;
    color: #66645e;
    line-height: 18px;
  }
  .chat-list {
    padding: 0 16px;
    height: calc(100vh - 310px);
    overflow-y: auto;
    .item-left {
      margin-bottom: 20px;
      .item-box {
        display: flex;
        .item-left-img {
          width: 26px;
          height: 26px;
          margin-right: 5px;
        }
        .item-data {
          overflow-x: auto;
          display: flex;
          align-items: center;
          .item-data-img {
            width: 143px;
            height: 106px;
            border-radius: 5px;
            margin-right: 5px;
          }
        }
        .item-data::-webkit-scrollbar {
          display: none;
        }
        .text-p {
          background: #fff;
          padding: 10px;
          font-size: 14px;
          color: #222;
          line-height: 18px;
          border-radius: 10px;
          white-space: pre-wrap;
          word-wrap: break-word;
        }
      }
      .item-data-title {
        padding-left: 30px;
        padding-top: 8px;
      }
    }
    .item-right {
      margin-bottom: 20px;
      display: flex;
      justify-content: flex-end;
      .text-p {
        background: #cae5ed;
        padding: 10px;
        font-size: 14px;
        color: #222;
        line-height: 18px;
        border-radius: 10px;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
  .pages-footer {
    background-color: #fff;
    padding: 16px;
    .footer-title {
      font-size: 16px;
      color: #222;
      margin-bottom: 10px;
    }
    .footer-list {
      display: flex;
      overflow-x: auto;
      .list-item {
        padding: 10px;
        width: 220px;
        height: 98px;
        background: #cae5ed;
        margin-right: 5px;
        position: relative;
        border-radius: 5px;
        flex-shrink: 0;
        .item-text {
          font-size: 14px;
          color: #222;
          line-height: 18px;
        }
        .tip-odd {
          position: absolute;
          left: 10px;
          bottom: 10px;
          background-color: #fac370;
          padding: 3px 8px;
          font-size: 12px;
          color: #fff;
          border-radius: 10.5px;
        }
      }
      .last-item {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        width: 130px;
        .last-text {
          font-size: 13px;
          color: #7f9898;
        }
      }
    }
    .footer-list::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>
