{"name": "vue3-template", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force"}, "dependencies": {"@types/swiper": "^6.0.0", "@vant/touch-emulator": "^1.4.0", "crypto-js": "^4.2.0", "js-md5": "^0.8.3", "normalize.css": "^8.0.1", "swiper": "^11.2.0", "vant": "^4.9.10", "vue": "^3.5.12", "vue-router": "^4.4.5"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/crypto-js": "^4.2.2", "@types/js-md5": "^0.7.2", "@types/node": "^22.9.0", "@vitejs/plugin-vue": "^5.1.4", "@vue/tsconfig": "^0.5.1", "less": "^4.2.1", "npm-run-all2": "^7.0.1", "postcss-px-to-viewport": "^1.1.1", "typescript": "~5.6.3", "vite": "^5.4.10", "vite-plugin-vue-devtools": "^7.5.4", "vue-tsc": "^2.1.10"}}