<template>
  <div class="flex-footer">
    <div
      class="flex-item"
      @click="serviceClick(0)"
      v-if="useStore.htmlType[0] === '1'"
    >
      <div class="flex-tip" v-if="serviceNum">{{ serviceNum }}</div>
      <img
        v-if="currentIndex == 0"
        class="tabbar-img"
        src="https://cache.gdxp.com/acce/assets/tabbar/1x.png"
        alt=""
      />
      <img
        v-else
        class="tabbar-img"
        src="https://cache.gdxp.com/acce/assets/tabbar/1.png"
        alt=""
      />
      <div :class="currentIndex == 0 ? 'active-tab' : ''">
        {{ $t('tabbar.service') }}
      </div>
    </div>
    <div
      class="flex-item"
      @click="serviceClick(1)"
      v-if="useStore.htmlType[1] === '1'"
    >
      <div class="flex-tip" v-if="evaluationNum">{{ evaluationNum }}</div>
      <img
        v-if="currentIndex == 1"
        class="tabbar-img"
        src="https://cache.gdxp.com/acce/assets/tabbar/2x.png"
        alt=""
      />
      <img
        v-else
        class="tabbar-img"
        src="https://cache.gdxp.com/acce/assets/tabbar/2.png"
        alt=""
      />
      <div :class="currentIndex == 1 ? 'active-tab' : ''">
        {{ $t('tabbar.evaluation') }}
      </div>
    </div>
    <div
      class="flex-item"
      @click="serviceClick(2)"
      v-if="useStore.htmlType[2] === '1'"
    >
      <img
        v-if="currentIndex == 2"
        class="tabbar-img"
        src="https://cache.gdxp.com/acce/assets/tabbar/3x.png"
        alt=""
      />
      <img
        v-else
        class="tabbar-img"
        src="https://cache.gdxp.com/acce/assets/tabbar/3.png"
        alt=""
      />
      <div :class="currentIndex == 2 ? 'active-tab' : ''">
        {{ $t('tabbar.advertising') }}
      </div>
    </div>
    <!-- <div class="flex-item" @click="serviceClick(3)" v-if="useStore.htmlType[3] === '1'">
            <img v-if="currentIndex==3" class="tabbar-img" src="https://cache.gdxp.com/acce/assets/tabbar/4x.png" alt="">
            <img v-else class="tabbar-img" src="https://cache.gdxp.com/acce/assets/tabbar/4.png" alt="">
            <div :class="currentIndex==3?'active-tab':''">{{ $t('tabbar.satisfaction') }}</div>
        </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { getUnreadMsg, readMsg } from '@/api'
import config from '@/config'
import { useHomeStore } from '@/stores/home'
const useStore = useHomeStore()
// 父传子
const porps = defineProps({
  chatList: {
    type: Array,
    default: [],
  },
})

// 子传父
const emit = defineEmits(['tabClick'])
const currentIndex = ref(0)
const serviceNum = ref(null)
const evaluationNum = ref(null)
const advertisingNum = ref(null)

//  0 客服   1 测评   2 广告   3 满意度
const serviceClick = (index: any) => {
  if (currentIndex.value === index) return
  currentIndex.value = index
  tabValue(index)
}

function tabValue(num: number) {
  emit('tabClick', num)
}

const msgIdList = computed(() => {
  const list: any = []
  porps.chatList?.forEach((item: any) => {
    if (item.role === 1) {
      list.push(item.msg_id)
    }
  })
  return list
})

function readMsgData() {
  const params = {
    screen: currentIndex.value,
    msg_id: msgIdList.value,
    cmd: 'screen_read_msg',
    device_sn: config.device_sn,
  }
  readMsg({ session_id: config.sessionId, data: JSON.stringify(params) }).then(
    () => {
      fetch(0)
    }
  )
}

// 获取未读消息
function fetch(type: any) {
  getUnreadMsg({
    session_id: config.sessionId,
    devices: config.device_sn,
  }).then((res: any) => {
    if (res.resultCode == 0 && res.content.length) {
      if (res.content[0].badge.length > 0 && res.content[0].badge[0]) {
        serviceNum.value = res.content[0].badge[0]
        addKass(type)
      }
      if (res.content[0].badge.length > 1 && res.content[0].badge[1]) {
        evaluationNum.value = res.content[0].badge[0]
        addKass(type)
      }
      if (res.content[0].badge.length > 2 && res.content[0].badge[2]) {
        advertisingNum.value = res.content[0].badge[0]
        addKass(type)
      }
    }
  })
}

function addKass(type: any) {
  if (type == 1) {
    readMsgData()
  }
}

onMounted(() => {
  setTimeout(() => {
    fetch(1)
  }, 1000)
  if (useStore.screen) {
    currentIndex.value = useStore.screen
  }
})
</script>

<style lang="less" scoped>
.flex-footer {
  display: flex;
  height: 79px;
  padding: 10px 20px 0px 20px;
  justify-content: space-around;
  border-top: 1px solid #ccc;
  .flex-item {
    width: 25%;
    height: 51px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    font-size: 14px;
    color: #666;
    .active-tab {
      color: #1989fa;
    }
  }
  .flex-tip {
    position: absolute;
    left: 0px;
    top: 0px;
    background-color: #ff3131;
    border-radius: 50%;
    width: 15px;
    height: 15px;
    line-height: 16px;
    font-size: 11px;
    font-weight: 700;
    color: #fff;
    display: flex;
    justify-content: center;
  }
  .tabbar-img {
    width: 32px;
    height: 32px;
  }
}
</style>
