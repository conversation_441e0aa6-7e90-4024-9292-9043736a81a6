<template>
  <div v-if="isShowCookiesPopup" class="cookies-popup">
    <div class="mask">
      <div class="cookies-container">
        <div class="left">
          <h1 class="title">Your Choices Regarding Cookies on this Site</h1>
          <p class="desc">
            We use cookies to give you the best online experience. Please let us
            know if you 'Agree to All' of these cookies. If you want to manage
            your preferences click on 'Privacy Settings'. You can change your
            settings anytime by clicking 'Cookie Preferences' at the bottom of
            any page.
          </p>
        </div>
        <div class="right">
          <div @click="handlePopup(false)" class="right-butotn">
            Agree to All
          </div>
          <div @click="handlePopup(false)" class="right-butotn">Reject All</div>
          <div class="privacy-settings-button">Privacy-Settings</div>
        </div>
      </div>
    </div>
    <!-- setting popup -->
    <div class="privacy-settings-container">
      <h1 class="title">Your Choices Regarding Cookies on this Site</h1>
      <p class="desc">
        Please choose whether this site may use Functional, Performance and/or
        Advertising cookies, as described below:
      </p>
      <a
        href="https://www.equinix.com/about/legal/cookie-notice"
        class="Find out more on how we use cookies."
      ></a>
      <div class="agree-button">Agree All</div>

      <div class="table-container">
        <h1 class="table-title">Manage Consent Preferences</h1>
        <div class="item">
          <div class="header">
            <div class="left">Required Cookies</div>
            <div class="right">Always Active</div>
          </div>
          <p class="desc">
            These are cookies that we have to include in order for certain web
            pages to function. For this reason, they do not require your
            consent.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const isShowCookiesPopup = ref<boolean>(true);
const handlePopup = (isShow: boolean) => (isShowCookiesPopup.value = isShow);
</script>

<style lang="less" scoped>
.cookies-popup {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  .mask {
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    .cookies-container {
      position: absolute;
      bottom: 0;
      display: flex;
      padding: 22px 36px;
      background-color: rgb(47, 53, 65);
      gap: 20px;
      .left {
        width: 65%;
        .title {
          font-size: 25px;
          font-weight: 600;
        }
        .desc {
          margin-top: 10px;
          font-size: 16px;
        }
      }
      .right {
        padding-right: 30px;
        width: 35%;
        .right-butotn {
          height: 43px;
          width: 100%;
          line-height: 43px;
          text-align: center;
          background-color: #e9dbff;
          border-radius: 10px;
          margin-bottom: 10px;
          color: #000;
          font-weight: 600;
          cursor: pointer;
          &:hover {
            filter: brightness(0.8);
          }
        }
        .privacy-settings-button {
          font-weight: 600;
          text-decoration: underline;
          text-align: center;
          margin-top: 20px;
          &:hover {
            filter: brightness(0.8);
          }
        }
      }
    }
  }
}
</style>
