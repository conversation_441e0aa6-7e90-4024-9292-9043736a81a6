<template>
  <Nav />
  <div class="vulnerability-report">
    <h1>Submit Vulnerability Report</h1>
    <p class="text-1">
      Thank you for contributing to the Aiwit App Vulnerability Disclosure
      Program. Your efforts help us enhance the security and reliability of our
      products. Please provide as much detail as possible about the potential
      issue you’ve discovered. The more comprehensive your report, the faster we
      can validate and address the issue.
    </p>
  </div>
  <van-form class="van-form-class" @submit="onSubmit" label-align="top">
    <h3 style="color: black; padding: 15px 15px">Required Information</h3>
    <!-- <van-field
      v-model="formData.product_model"
      readonly
      name="product_model"
      label="Product Model"
      placeholder="Specify the product model where the vulnerability was observed."
      @click="showPicker = true"
      :rules="[{ required: true, message: 'Product Model is required!' }]"
    />
    <van-popup v-model:show="showPicker" destroy-on-close position="bottom">
      <van-picker
        :columns="columns"
        :model-value="pickerValue"
        @confirm="onConfirm"
        @cancel="showPicker = false"
        :columns-field-names="{ text: 'product_name', value: 'product_name' }"
      />
    </van-popup> -->
    <van-field
      v-model="formData.product_model"
      name="product_model"
      label="Product Model"
      placeholder="Specify the product model where the vulnerability was observed."
      :rules="[{ required: true, message: 'Product Model is required!' }]"
    />

    <van-field
      v-model="formData.vulnerability_title"
      name="vulnerability_title"
      label="Vulnerability Title"
      placeholder="Provide a concise and descriptive title summarizing the vulnerability."
      :rules="[{ required: true, message: 'Vulnerability Title is required!' }]"
    />
    <van-field
      v-model="formData.vulnerability_description"
      name="vulnerability_description"
      label="Vulnerability Description"
      type="textarea"
      rows="4"
      autosize
      :placeholder-style="{ 'white-space': 'pre-wrap' }"
      placeholder="Include a detailed description of the vulnerability, such as:
• Steps to reproduce the issue.
• The potential security impact.
• Any other details to help us understand and assess the issue."
      :rules="[
        { required: true, message: 'Vulnerability Description is required!' },
      ]"
    />
    <van-field name="uploader" label="File Upload">
      <template #input>
        <van-uploader
          v-model="uploadList"
          :max-size="10 * 1024 * 1024"
          :before-read="beforeRead"
          :after-read="afterRead"
          :before-delete="beforeDelete"
        />
      </template>
    </van-field>

    <van-field
      v-model="formData.name"
      name="name"
      label="Name"
      placeholder="Providing your name is required to ensure a clear record of submissions and to address you personally in our communications."
      :rules="[{ required: false, message: 'name is required!' }]"
    />
    <van-field
      v-model="formData.email"
      name="email"
      label="Email Address"
      type="textarea"
      rows="4"
      autosize
      :placeholder-style="{ 'white-space': 'pre-wrap' }"
      placeholder="Providing your email address is required for us to:
• Update you on the status of your report.
• Request additional information if necessary. Your email will only be used for this purpose and will not be shared with any third party without your consent."
      :rules="[{ required: false, message: 'Email Address is required!' }]"
    />
    <van-cell-group style="display: flex; align-items: end">
      <van-field
        style="width: 600px"
        v-model="formData.inputIdentifyCode"
        label="Verification Code"
        placeholder="To ensure the security of your submission, please complete the CAPTCHA or verification code section."
      />
      <div
        style="position: absolute; left: 150px; top: 6px"
        @click="refreshCode"
      >
        <Sidentify :identifyCode="identifyCode" />
      </div>
    </van-cell-group>

    <div style="margin: 16px; display: flex; justify-content: center">
      <van-button
        style="background-color: #0071e3; padding: 0 20px"
        class="pointer"
        round
        type="primary"
        native-type="submit"
        >Submit</van-button
      >
    </div>
  </van-form>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import Nav from "@/components/Nav.vue";
import Sidentify from "@/components/Sidentify.vue";
import { showToast } from "vant";
import { useRouter } from "vue-router";

const formData = ref({
  product_model: "",
  vulnerability_title: "",
  vulnerability_description: "",
  file_url: [],
  name: "",
  email: "",
  inputIdentifyCode: "",
});

const pickerValue = ref([]);
const showPicker = ref(false);
const columns = ref([]);

const onConfirm = ({ selectedValues, selectedOptions }: any) => {
  formData.value.product_model = selectedOptions[0].product_name;
  showPicker.value = false;
};

const uploadList: any = ref([]);
const beforeRead = (file: any) => {
  const fileName = file.name;
  const isDuplicate = uploadList.value.some(
    (item: { file: { name: any } }) => item.file && item.file.name === fileName
  );

  if (isDuplicate) {
    showToast("文件已存在，请勿重复上传");
    return false;
  }
  return true;
};
const afterRead = (files: any) => {
  const formData = new FormData();
  formData.append("file", files.file);

  fetch("https://company.ekenelectronics.com/api/upload_file", {
    method: "POST",
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      const index = uploadList.value.findIndex(
        (item: { file: { name: string } }) => item.file.name === files.file.name
      );
      if (index !== -1) {
        uploadList.value[index].$url = data.data;
      }
    })
    .catch((error) => {
      console.error("文件上传失败", error);
    });
};
const router = useRouter();
const nav = (url: string) => {
  router.push(url);
};
const beforeDelete = () => {};
const onSubmit = (values: any) => {
  if (localStorage.getItem("isLogin") !== "true") {
    showToast("Please log in first");
    setTimeout(() => {
      nav("/login");
    }, 1500);
    return;
  }

  const params = { ...values };
  const uploadArr: any = [];
  values.uploader.forEach((element: any) => {
    uploadArr.push(element.$url);
  });
  params.file_url = uploadArr.join(",");
  if (formData.value.inputIdentifyCode !== identifyCode.value) {
    showToast("验证码错误");
    return;
  }
  delete params.uploader;
  delete params.inputIdentifyCode;
  fetch("https://company.ekenelectronics.com/api/set_vulnerability", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(params),
  })
    .then((response) => response.json())
    .then((data) => {
      showToast("提交成功");
      formData.value.product_model = "";
      formData.value.vulnerability_title = "";
      formData.value.vulnerability_description = "";
      formData.value.file_url = [];
      formData.value.name = "";
      formData.value.email = "";
      formData.value.inputIdentifyCode = "";
      uploadList.value = [];
    })
    .catch((error) => {
      console.error("提交失败", error);
    });
};

const fetchModelData = async () => {
  try {
    const response = await fetch(
      " https://company.ekenelectronics.com/api/get_wz_support_producttype"
    );
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const result = await response.json();
    columns.value = result.data;
  } catch (err) {}
};

// 图形验证码
let identifyCodes = "1234567890";
let identifyCode = ref("3212");

const randomNum = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min) + min);
};

const makeCode = (o: string | any[], l: number) => {
  for (let i = 0; i < l; i++) {
    identifyCode.value += o[randomNum(0, o.length)];
  }
};

const refreshCode = () => {
  identifyCode.value = "";
  makeCode(identifyCodes, 4);
};
onMounted(() => {
  identifyCode.value = "";
  makeCode(identifyCodes, 4);
});
</script>

<style lang="less" scoped>
@media screen and (min-width: 992px) {
  .van-form-class {
    width: 700px;
    margin: 0 auto;
  }
  .text-1 {
    color: #888;
    font-size: 15px;
    width: 1000px;
    margin: 0 auto;
  }
}
.vulnerability-report {
  background-color: #fff;
  padding: 0 20px 40px;

  h1 {
    padding: 20px 0;
    color: #000000d9;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
  }

  .text-1 {
    color: #888;
    font-size: 18px;
  }
}

.text-2 {
  color: #000000d9;
  font-size: 18px;
  padding: 40px 20px;
}

.pointer {
  cursor: pointer;
}

::v-deep(.van-field__control::placeholder) {
  font-size: 14px;
}
</style>
