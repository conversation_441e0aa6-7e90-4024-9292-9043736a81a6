<template>
  <div class="form-container" :key="route.fullPath">
    <van-form class="van-form-class" @submit="onSubmitSignIn">
      <h1 class="h1">Create account</h1>
      <van-field
        v-model="formData.country.name"
        label=""
        placeholder="Country/Region"
        @click="showCountryPicker = true"
        readonly
        right-icon="arrow-down"
        :rules="[{ required: false, message: 'Regin is required!' }]"
      />
      <van-popup v-model:show="showCountryPicker" position="bottom" round>
        <van-picker
          show-toolbar
          title="countryList"
          :columns="countryList"
          @confirm="onCountryConfirm"
          :columns-field-names="{ text: 'name', value: 'code' }"
          cancel-button-text="cancel"
          confirm-button-text="confirm"
        />
      </van-popup>

      <div>
        <van-field
          v-model="formData.email"
          label=""
          placeholder="Email"
          type="email"
          :rules="[{ required: false, message: 'Email is required!' }]"
        />
      </div>

      <van-field
        v-model="formData.emailCode"
        label=""
        placeholder="Verification code"
        :rules="[
          { required: false, message: 'Verification code is required!' },
        ]"
      >
        <template #button>
          <van-button
            class="get-code-button"
            @click="getEmailCode"
            size="small"
            type="primary"
            :disabled="requiredEmali"
            >{{
              !emailCodeTimerFlag ? emailCodeTimer + "s" : "Get Code"
            }}</van-button
          >
        </template>
      </van-field>

      <van-field
        v-model="formData.password"
        label=""
        placeholder="password"
        type="password"
        :rules="[{ required: false, message: 'Password is required!' }]"
      />

      <van-button
        class="submit-button"
        type="primary"
        block
        :disabled="!formData.email || !formData.password || !formData.emailCode"
        native-type="submit"
      >
        Create account
      </van-button>
    </van-form>
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from "vue";
import Headers from "@/components/Headers.vue";
import countries from "@/assets/data/countries";
import { showToast } from "vant";
import { useRoute, useRouter } from "vue-router";
import { md5 } from "js-md5";

const router = useRouter();
const route = useRoute();

interface Country {
  name: string;
  code: string;
}
interface FormData {
  country: { name: string; code: string };
  email: string;
  emailCode: string;
  password: string;
  repeatPassword: string;
}

/**
 *  signType 0 注册 1 忘记密码 2 登录
 **/
const showCountryPicker = ref(false);
const countryList: Country[] = countries;

const formData = ref<FormData>({
  country: { name: "", code: "" },
  email: "",
  emailCode: "",
  password: "",
  repeatPassword: "",
});

const nav = (url: string) => router.push(url);
// Submit
const onSubmitSignIn = async () => {
  const { email, password, emailCode, country } = formData.value;
  const encryptedPassword = md5(password);

  // Registered
  const response = await fetch("https://company.ekenelectronics.com/api/register", {
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      email: email,
      name: country.code,
      code: emailCode,
      pass_word: encryptedPassword,
    }),
  });
  const { code, msg } = (await response.json()) as any;
  if (code == 0) {
    formData.value.email = "";
    formData.value.country = { name: "", code: "" };
    formData.value.email = "";
    formData.value.emailCode = "";
    formData.value.password = "";
    showToast("registered successfully");
    setTimeout(() => {
      router.replace("/login");
    }, 1500);
  } else {
    showToast(msg);
  }
};
// Get code
const emailCodeTimer = ref(0);
const emailCodeTimerFlag = ref(true);
const getCode = ref<string>("");
const getEmailCode = async () => {
  const response = await fetch("https://company.ekenelectronics.com/api/send_email_code", {
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ email: formData.value.email }),
  });

  const { data, code, msg } = (await response.json()) as any;

  if (code == 0) {
    getCode.value = data.code;

    emailCodeTimer.value = 60;
    emailCodeTimerFlag.value = false;
    const emailCodeInterval = setInterval(() => {
      if (emailCodeTimer.value > 0) {
        emailCodeTimer.value--;
      } else {
        clearInterval(emailCodeInterval);
        emailCodeTimerFlag.value = true;
      }
    }, 1000);
  } else {
    showToast(msg);
  }
};

const onCountryConfirm = ({ selectedValues, selectedOptions }: any) => {
  formData.value.country = selectedOptions[0];
  showCountryPicker.value = false;
};

const requiredEmali = computed(
  () => !formData.value.email || !emailCodeTimerFlag.value
);
</script>

<style lang="less" scoped>
.form-container {
  background: url(../assets/sign/signbg.png) no-repeat 100% / cover;
  position: relative;
  height: calc(100vh);
  background-color: rgb(246, 247, 250);
  overflow: hidden;
  padding-top: 130px;

  .van-form-class {
    h1 {
      color: black;
      text-align: center;
      padding: 0px 0 10px;
    }
    background-color: #fff;
    width: 400px;
    margin: 100px auto 0;
    padding: 30px 20px 30px;
    border-radius: 10px;
    overflow: hidden;
    .get-code-button {
      width: 90px;
    }
    .submit-button {
      margin-top: 30px;
      border-radius: 6px;
    }
  }

  .forgot-create-container {
    display: flex;
    justify-content: space-between;
    padding: 5px 10px;
    .text-button {
      color: #00000099;
      font-size: 13px;
      cursor: pointer;
    }
    .create-text {
      color: #1989fa;
    }
  }
  @media screen and (max-width: 550px) {
    .van-form-class {
      width: 90vw;
    }
  }
}
</style>
