{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": [
  "env.d.ts", 
  "src/**/*", 
  "src/**/*.vue",
  "src/**/*.ts",
  "src/**/*.d.ts",
  "src/**/*.tsx",
  "src/**/*.vue",
  "build/**/*.ts",
  "build/**/*.d.ts",
  "vite.config.ts"],
  "exclude": ["src/**/__tests__/*","node_modules", "dist", "**/*.js"],
  "compilerOptions": {
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",

    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    "lib": ["DOM"],
    "types": ["vite/client"],
  }
}
