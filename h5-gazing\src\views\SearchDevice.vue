<template>
  <div class="page">
    <Header title="搜索设备" :left-type="1" />
    <div class="contariner">
      <div class="serach-box">
        <img v-if="status == 1 || status == 0" class="serach-img" src="../assets/device/no.png" alt="" />
        <img v-else-if="status == 2" class="serach-imgs" src="../assets/device/no.png" alt="" />
        <img v-else class="serach-img" src="../assets/1.gif" alt="" />
      </div>
      <!-- 搜索失败 -->
      <div class="serach-tip no-tip" v-if="status == 2">未发现设备</div>
      <!-- 未开启蓝牙 -->
      <div class="serach-tip" v-else-if="status == 0 || status == 1 || status == 4">
        请开启手机蓝牙，以便查找附近蓝牙设备
      </div>
      <div class="serach-tip" v-else>
        正在搜索设备
        <span class="time-span">{{ countdownTime }}</span>
        <span class="times">s</span>
      </div>
      <div class="serach-text">
        {{
          status == 2
            ? "可再次尝试，或尝试二维码扫描"
            : status == 0 || status == 1 || status == 4
            ? "（从屏幕右上角下划打开）"
            : "请将手机尽量靠近您要添加的设备"
        }}
      </div>
    </div>
    <div class="footer-btn" v-if="status == 0 || status == 1 || status == 2 || status == 4">
      <van-button
        @click="again"
        class="okk-btn"
        type="primary"
        round
        size="large"
        color="linear-gradient(90deg,#4774F5 3.73%,#4977F4 100%)"
      >
        {{ status == 0 || status == 1 || status == 4 ? "蓝牙已开启" : "再次尝试" }}
      </van-button>
      <van-button plain type="primary" round size="large" @click="goScanning">尝试二维码扫描</van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import { showToast } from "vant"
import { ref, onMounted, onUnmounted } from "vue"
import { useRouter, useRoute } from "vue-router"
import { click_search_device, end_search_bluetooth } from "@/utils"
import config from "@/config"

const router = useRouter()
const route = useRoute()
const status = ref(-1)
const countdownTime = ref(15)
const timer = ref<any>(null)
const countdown = () => {
  timer.value = setInterval(() => {
    if (countdownTime.value === 0) {
      clearInterval(timer.value)
      timer.value = null
      countdownTime.value = 0
      status.value = 2
      end_search_bluetooth()
    } else {
      countdownTime.value--
    }
  }, 1000)
}

const again = () => {
  click_search_device()
  status.value = -1
  countdownTime.value = 15
  timer.value = null
  clearInterval(timer.value)
  countdown()
}

/* 
        APP搜索设备回调 
        0:蓝牙未打开
        1：蓝牙未授权
        2：未搜索到蓝牙设备
        3：搜索到蓝牙设备，已连接
        4：蓝牙设备断开
    */
const searchResult = (type: string) => {
  status.value = Number(type)
  clearInterval(timer.value)
  timer.value = null
  if (type == "3") {
    setTimeout(() => {
      if (route.name === "searchDevice") {
        router.push("configurationNetwork")
      }
    }, 500)
  } else if (type == "4") {
    showToast("蓝牙已断开")
    setTimeout(() => {
      if (route.name !== "deviceReset") {
        router.push("deviceReset")
      }
    }, 1000)
  }
}

const goScanning = ()=>{
  if(config.add_device_item.bluetooth === 1){
    config.add_device_item.bluetooth = 0
    config.is_bluetooth = status.value
  }
  router.push('deviceReset')
}

onMounted(() => {
  window.searchResult = searchResult
  if(config.add_device_item.bluetooth === 1){
    countdown()
  }else {
    if(config.is_bluetooth >= 0){
      status.value = config.is_bluetooth
    }
  }
})

onUnmounted(() => {
  clearInterval(timer.value)
  end_search_bluetooth()
})
</script>

<style lang="less" scoped>
.contariner {
  padding: 50px 20px 20px 20px;
  .serach-box {
    min-height: 244px;
    margin-bottom: 25px;
    display: flex;
    justify-content: center;
    .serach-img {
      max-width: 100%;
      height: 244px;
    }
    .serach-imgs {
      max-width: 100%;
      height: 288px;
    }
  }
  .serach-tip {
    font-size: 18px;
    color: #333333;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    .time-span {
      font-size: 32px;
      color: #4774f5;
      margin-left: 10px;
      margin-right: 2px;
    }
    .times {
      padding-top: 8px;
      color: #4774f5;
    }
  }
  .no-tip {
    color: #e65547;
  }
  .serach-text {
    font-size: 14px;
    color: #999999;
    text-align: center;
  }
}
.footer-btn {
  position: absolute;
  bottom: 50px;
  width: 89%;
  margin: 0 auto;
  padding: 0px 20px;
  left: 0px;
  .okk-btn {
    margin-bottom: 20px;
  }
}
</style>
