/* color palette from <https://github.com/vuejs/theme> */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-weight: normal;
  font-family: 'Nexa Bold', Arial, sans-serif;
}
/* body, h1, h2, h3, h4, h5, h6, p, span, input, button, table, div ,li{
  font-family: 'PingFang SC', sans-serif;
} */

body {
  min-height: 100vh;
  /* color: var(--color-text); */
  /* background: var(--color-background); */
  color: #fff;
  background: #fff;
  transition: color 0.5s, background-color 0.5s;
  line-height: 1.6;
  /* font-family: Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif; */
  font-family: 'Nexa Bold', Arial, sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
li {
  list-style-type: none;
}

p, ul, ol, li, span, a, input {
  font-family: 'Nexa Book', Arial, sans-serif;
}