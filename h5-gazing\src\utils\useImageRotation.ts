import { ref, computed } from 'vue';

// 定义组合式函数
export function useImageRotation() {
  // 定义初始旋转角度
  const rotationAngle = ref(0);

  // 是否正在旋转
  const isRotating = ref(false);

  // 旋转速度（度数/秒）
  const rotationSpeed = 2; // 每秒增加或减少1度

  let intervalId: number | null = null;

  // 计算图片的样式
  const imageStyle = computed(() => ({
    transform: `rotate(${rotationAngle.value}deg)`,
  }));

  // 开始旋转函数
  const startRotation = () => {
    if (!isRotating.value) {
      isRotating.value = true;
      intervalId = window.setInterval(() => {
        rotationAngle.value += rotationSpeed * 1;
      }, 1000 / 60); // 每秒60帧
    }
    setTimeout(() => {
        if(isRotating.value){
            resetRotation()
        }
    }, 30000);
  };

  // 停止旋转函数
  const stopRotation = () => {
    if (isRotating.value && intervalId !== null) {
      isRotating.value = false;
      clearInterval(intervalId);
      intervalId = null;
    }
  };

  // 重置旋转函数
  const resetRotation = () => {
    stopRotation();
    rotationAngle.value = 0;
  };

  return {
    rotationAngle,
    isRotating,
    imageStyle,
    startRotation,
    stopRotation,
    resetRotation,
  };
}