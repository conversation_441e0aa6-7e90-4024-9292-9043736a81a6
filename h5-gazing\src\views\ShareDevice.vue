<template>
  <div class="pages">
    <Header title="分享设备" :left-type="1" />
  <div class="share-box">
    <div class="share-info">
      <div class="share-info-header">
        <div class="share-info-header-left">
          <div class="share-info-header-left-title">
            <img class="header-img" src="../assets/device/default_head.png">
            <span class="header-text">设备管理者</span>
          </div>
          <div class="share-info-header-left-name">{{ config.username.value }}</div>
        </div>
        <img class="share-info-header-right" src="../assets/device/seeing_device_blue.png" alt="设备转让" @click="router.push('/transferDevice')">
      </div>
      <div class="share-info-share">
        <div class="facility-img-box">
          <img class="facility-img" :src="config.device.product_img" alt="产品"/>
        </div>
        <div class="share-icon-box">
          <img class="share-icon" src="../assets/device/qrcode_share.png" alt="二维码分享" @click="router.push('/shareCode')"/>
          <img class="share-icon" src="../assets/device/email_share.png" alt="email分享" @click="router.push('/emailShareDevice')"/>
        </div>
      </div>
    </div>
    <div class="share-list">
      <div class="share-title">已分享的使用者</div>
      <div v-if="state.shareList.length">
        <div class="share-item" v-for="(item, index) in state.shareList" :key="index">
          <div class="share-item-left">
            <div class="serial">{{ index > 9 ? index : `0${index + 1}` }}</div>
          </div>
          <div class="share-item-right">
            <div class="share-item-user">
              <div class="share-item-text">{{ item.email }}</div>
              <div class="share-item-time">
                {{ timestampToDate(item.create_at, config.timezone) }}
              </div>
            </div>
            <van-icon @click="delShareData(item)" name="clear" color="#848CA4" size="20" />
          </div>
        </div>
      </div>
      <van-empty v-if="state.shareList.length == 0" description="还未分享过" />
    </div>
  </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref } from "vue"
import { type shareData } from "@/api/base"
import config from "@/config"
import Header from "@/components/Header.vue"
import { get_share_qrkey, device_share_list, delete_share } from "@/api/index"
import { showNotify, showConfirmDialog } from "vant"
import { timestampToDate } from "@/utils"
import { useRouter } from "vue-router"

const router = useRouter()

const qrData = ref<string>("") // 要编码为二维码的数据
const state = reactive({
  shareList: [] as shareData[],
})

const delShareData = (item: shareData) => {
  showConfirmDialog({
    title: "提示",
    message: "是否确认删除?",
  })
    .then(() => {
      delete_share(item.user_id, config.product_key, config.device_sn).then((res: any) => {
        if (res.code === 0) {
          fetch()
          showNotify({
            type: "warning",
            position: "bottom",
            message: "删除成功",
          })
        }
      })
    })
    .catch(() => {})
}

const fetch = () => {
  device_share_list(config.product_key, config.device_sn).then((res: any) => {
    if (res.code === 0) {
      state.shareList = res.data || []
    }
  })
}

const shareCode = () => {
  get_share_qrkey(config.product_key, config.device_sn).then((res: any) => {
    if (res.data) {
      qrData.value = res.data.qr_key
    }
    fetch()
  })
}

onMounted(() => {
  shareCode()
})
</script>

<style lang="less" scoped>
.share-box {
  padding: 20px 15px;
  box-sizing: border-box;
  .share-info {
    width: 100%;
    background-image: url('@/assets/device_info_bg.png');
    background-repeat: no-repeat;
    background-size: 100% auto;
    padding: 12px 18px;
    box-sizing: border-box;
    &-header {
      width: 100%;
      border-bottom: 1px solid #f3f3f3;
      padding-bottom: 14px;
      display: flex;
      justify-content: space-between;
      &-left {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-content: spac;
        &-title {
          display: flex;
          align-items: center;
          margin-bottom: 5px;
          .header-img {
            width: 20px;
            height: 20px;
            margin-right: 7px;
          }
          .header-text {
            font-size: 16px;
            font-weight: 700;
            color: #222222;
          }
        }
        &-name {
          font-size: 15px;
          font-weight: 400;
          color: #666666;
          padding-left: 27px;
        }

      }
      &-right {
        width: 50px;
        height: 50px;
      }
    }
    &-share {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      padding-top: 45px;
      .facility-img-box {
        width: 45%;
        text-align: center;
        .facility-img {
          height: 126px;
        }
      }
      .share-icon-box {
        width: 55%;
        display: flex;
        justify-content: space-between;
        .share-icon {
          width: 75px;
          height: 75px;
        }
      }
    }
  }
  .share-list {
    padding: 20px 20px 0 20px;
    background-color: #fff;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
    .share-title {
      font-size: 16px;
      color: #494854;
      font-weight: 700;
    }
    .share-item {
      display: flex;
      height: 65px;
      .share-item-left {
        width: 30px;
        padding-top: 12px;
        .serial {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background-color: #eee;
          font-size: 11px;
          color: #666666;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .share-item-right {
        flex: 1;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #f3f3f3;
        .share-item-user {
          display: flex;
          flex-direction: column;
          justify-content: center;
          .share-item-text {
            font-size: 15px;
            color: #494854;
            margin-bottom: 8px;
            font-weight: 700;
          }
          .share-item-time {
            font-size: 12px;
            color: #848ca4;
          }
        }
      }
    }
  }
}
</style>
