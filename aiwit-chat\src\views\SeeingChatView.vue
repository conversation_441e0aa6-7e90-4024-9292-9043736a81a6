<template>
  <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
    <main>
      <ul class="card-container">
        <li @click="navDetail(item)" v-for="item in list" :key="item.device_sn" class="card-item">
          <div class="pic-container">
            <van-badge
              color="red"
              position="bottom-right"
              :content="item.tip$ > 0 ? item.tip$ : ''"
            >
              <img class="pic" :src="item.pic$" alt="" />
            </van-badge>
          </div>
          <div class="name-content-container">
            <h1 class="name">{{ item.title$ }}</h1>
            <p class="content">{{ item.content$ }}</p>
          </div>
          <p class="time">{{ item.time$ }}</p>
        </li>
      </ul>
      <p v-if="loadingData && list && list.length === 0" class="empty-data">
        {{ $t("chat.emptyMsg1") }}
        <br />
        {{ $t("chat.emptyMsg2") }}
      </p>
    </main>
  </van-pull-refresh>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { getChatList } from "@/api/modules/chat"
import { navDetail, filterData } from "@/utils"
import config from "@/config"

const loadingData = ref<boolean>(false)
const refreshing = ref<boolean>(false)
const list = ref<any[]>([])

const onRefresh = () => {
  // 清空列表数据
  fetchList()
}

const fetchList = async () => {
  const data = await getChatList({ sessionId: config.token, lang: config.lang })
  loadingData.value = true
  refreshing.value = false
  if (data.content) {
    filterData(data.content)
  }
  list.value = data.content ? data.content : []
}

// 接口APP传过来的数据对比列表
const updateChatData = () => {
  fetchList()
}

onMounted(() => {
  fetchList()
  window.updateChatData = updateChatData
})
</script>

<style lang="scss" scoped>
main {
  background: #ffffff;
  min-height: 100vh;
  padding: 0 16px 10px;

  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  padding-bottom: calc(10px + constant(safe-area-inset-bottom));
  padding-bottom: calc(10px + env(safe-area-inset-bottom));

  .card-container {
    border-radius: 8px;
    overflow: hidden;

    .card-item {
      position: relative;
      background: #ffffff;
      padding: 12px 0px;
      display: flex;

      .pic-container {
        height: 70px;
        width: 70px;
        flex-shrink: 0;
        margin-right: 8px;
        background: #96afa3;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;

        .pic {
          height: 56px;
          width: 56px;
          object-fit: contain;
          flex-shrink: 0;
        }
      }

      .time {
        font-family: PingFang SC;
        font-size: 12px;
        font-weight: 400;
        line-height: 17px;
        color: #999999;
        margin-left: auto;
        position: absolute;
        right: 0px;
        top: 10px;
      }

      .name-content-container {
        margin-top: 6px;

        .name {
          font-family: PingFang SC;
          font-size: 16px;
          font-weight: bolder;
          line-height: 22px;
          max-width: 60vw;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          word-break: break-word;
          color: rgb(74, 74, 74);
        }

        .content {
          font-family: PingFang SC;
          font-size: 14px;
          font-weight: 400;
          line-height: 17px;
          margin-top: 8px;
          max-width: 60vw;
          color: rgb(154, 154, 154);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          word-break: break-word;
        }
      }
    }
  }

  .empty-data {
    font-family: PingFang SC;
    font-size: 20px;
    font-weight: 400;
    line-height: 28px;
    text-align: center;
    margin-top: 40%;
    color: #333333;
  }
}
:deep(.van-badge--bottom-right) {
  bottom: 10px;
}
</style>
