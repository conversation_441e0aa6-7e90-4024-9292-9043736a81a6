<template>
  <van-popup
    closeable
    v-model:show="showBottom"
    position="bottom"
    :style="{ minHeight: '35%' }"
    @close="htmlClose"
  >
    <div class="modal-title">{{ $t('home.thisService') }}</div>
    <div class="rate-box">
      <van-rate
        v-model="rateValue"
        :gutter="10"
        :size="26"
        color="#33CC99"
        void-icon="star"
        void-color="#ccc"
      />
    </div>
    <div class="rate-text">{{ $t('home.solved') }}</div>
    <div class="rate-data">
      <div
        class="rate-add"
        :class="showRate ? 'active-add' : ''"
        @click=";(showRate = !showRate), (showNoRate = false)"
      >
        <img
          v-if="showRate"
          class="rate-img"
          src="https://cache.gdxp.com/acce/assets/dzb.png"
          alt=""
        />
        <img
          v-else
          class="rate-img"
          src="https://cache.gdxp.com/acce/assets/dz.png"
          alt=""
        />
        <div>{{ $t('home.yes') }}</div>
      </div>
      <div
        class="rate-add"
        :class="showNoRate ? 'active-add' : ''"
        @click=";(showNoRate = !showNoRate), (showRate = false)"
      >
        <img
          v-if="showNoRate"
          class="rate-img"
          src="https://cache.gdxp.com/acce/assets/bzb.png"
          alt=""
        />
        <img
          v-else
          class="rate-img"
          src="https://cache.gdxp.com/acce/assets/bz.png"
          alt=""
        />
        <div>{{ $t('home.no') }}</div>
      </div>
    </div>
    <div class="rate-btn">
      <van-button
        type="success"
        round
        size="large"
        color="#33CC99"
        @click="submitRate"
        >{{ $t('home.submit') }}</van-button
      >
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { showNotify } from 'vant'
import { saveAppraise } from '@/api'
import { showSuccessToast } from 'vant'
import config from '@/config'
import moment from 'moment'
import { i18n } from '@/lang'
const { t } = i18n.global as any

const showBottom = ref(false) //弹框
const rateValue = ref(0) //评星
const showRate = ref(false) //是否解决 是
const showNoRate = ref(false) //是否解决 否

// 打开弹框
const openEvaluate = () => {
  showBottom.value = true
}
// 父组件共享子组件的方法
defineExpose({
  openEvaluate,
})

// 子传父组件
const emit = defineEmits(['sendMsg', 'htmlClose'])

const props = defineProps({
  msgId: Number,
  serviceUid: Number,
})

// 关闭事件
const htmlClose = () => {
  emit('htmlClose')
}

// 提交评价
const submitRate = () => {
  if (showRate.value == false && showNoRate.value == false)
    return showNotify({ type: 'warning', message: t('home.please') })
  let isSolve = -1
  if (showRate.value) {
    isSolve = 1
  }
  if (showNoRate.value) {
    isSolve = 0
  }
  fetch(isSolve)
}

function fetch(isSolve: number) {
  // saveAppraise(config.sessionId,props.msgId,rateValue.value,isSolve).then((res:any)=>{
  //     if(res.resultCode == 0){
  //         showSuccessToast(t('home.evaluateSuccess'));
  //         localStorage.setItem('evaluateTime',moment().format('YYYY-MM-DD HH:mm:ss'))
  //         showBottom.value = false;
  //         emit('sendMsg',{service_uid:props.serviceUid,star:rateValue.value,solved:isSolve});
  //     }
  // })
  showSuccessToast(t('home.evaluateSuccess'))
  localStorage.setItem('evaluateTime', moment().format('YYYY-MM-DD HH:mm:ss'))
  showBottom.value = false
  emit('sendMsg', {
    service_uid: props.serviceUid,
    star: rateValue.value,
    solved: isSolve,
  })
}
</script>

<style lang="less" scoped>
.modal-title {
  background-color: #f3f3f3;
  font-size: 15px;
  padding-left: 15px;
  color: #333;
  margin-bottom: 10px;
  height: 50px;
  display: flex;
  align-items: center;
}
.rate-box {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}
.rate-text {
  font-size: 14px;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}
.rate-data {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 50px;
  .rate-add {
    width: 70px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #999;
    font-size: 15px;
    color: #999;
    border-radius: 20px;
    margin-right: 15px;
    .rate-img {
      width: 15px;
      height: 15px;
      margin-right: 2px;
    }
  }
}
.rate-btn {
  padding: 0px 20px 30px 20px;
}
.active-add {
  background-color: #33cc99;
  color: #fff !important;
  border: none !important;
}
.catek {
  display: none;
}
</style>
