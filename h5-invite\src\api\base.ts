export interface listData {
  detail_two:string
  title: string
  detail:string
  detail_img: string
  img_link: string
  url_link: string
  discount: number
  discount_code:  string
}

export interface userData {
  question_id: number
  msg_id: number
  msg_type: number
  from_name: string
  from: string
  ctime: string
  role: number
  expiry_time: number
  uid: number
  show_button: number
  screen: number
  content: {
    txt: string
    cloud_storage_txt: any
    service_satisfaction_survey_txt: any
    url: any
    img?:string
  }
}

export interface formData {
  name: string
  phone: string
  state: string
  city: string
  street: string
  zipcode: string
  order_status:number
  error_text:string
  ship_no:string
  sp_status:number
  last_time:string
}