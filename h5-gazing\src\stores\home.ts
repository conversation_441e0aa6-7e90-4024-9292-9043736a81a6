import { reactive, ref } from "vue"
import { defineStore } from "pinia"
import { get_by_user, check_ota,getUnreadCount } from "@/api/index"
import type { deviceData } from "@/api/base"
import { subData } from "@/components/WebRTCClient"
import config from "@/config"

export const useHomeStore = defineStore("home", () => {
  const state = reactive({
    deviceList: [] as deviceData[],
    mute_notify_to: "",
  })
  const deviceIdList = ref<any>([])
  const getDeviceList = async (type: number) => {
    try {
      const res: any = await get_by_user()
      if (res.code === 0) {
        if (res.data.device_list && res.data.device_list.length) {
          config.sub_id.value = []
          if (type === 1) {
            try {
              res.data.device_list.forEach((item: deviceData) => {
                deviceIdList.value.push(item.device_sn)
                item.status = 0
                subData(item.device_sn, item.product_key)
                if (item.cloud_product_id) {
                  config.sub_id.value.push(item.cloud_product_id)
                }
              })
            } catch (error) {
              console.error("Error in subData:", error)
            }
            get_check_ota()
            setTimeout(() => {
              get_unread_count()
            }, 800);
            // getMsg()
          } else if (type === 2) {
            res.data.device_list.forEach((item: deviceData) => {
              item.status = 0
              subData(item.device_sn, item.product_key)
              if (item.cloud_product_id) {
                config.sub_id.value.push(item.cloud_product_id)
              }
            })
          }
        }
        if (res.data.device_list) {
          state.deviceList = res.data.device_list
          config.deviceList = res.data.device_list
          config.webrtc_list = res.data.webrtc_list
        } else {
          state.deviceList = []
          config.deviceList = []
          config.webrtc_list = res.data.webrtc_list
        }
        if (res.data.project_attribute) {
          config.device_config = res.data.project_attribute
        }
        if (res.data.mute_notify_to) {
          config.mute_notify_to = res.data.mute_notify_to
          state.mute_notify_to = res.data.mute_notify_to
        }
      }
    } catch (error) {
      console.error("Error fetching device list:", error)
    }
  }

  const get_check_ota = async () => {
    try {
      const res: any = await check_ota(deviceIdList.value, 1)
      if (res.code === 0) {
        if (res.data && res.data.length) {
          state.deviceList.forEach(item => {
            res.data.forEach((i: any) => {
              if (item.device_sn === i.device_sn) {
                item.ota_item = i
                item.ota_state = 0
                item.ota_progress = -1
              }
            })
          })
        }
      }
    } catch (error) {
      console.error("Error fetching check_ota:", error)
    }
  }

  const get_unread_count = async () => {
    try {
      const res: any = await getUnreadCount({
        device_sns: deviceIdList.value,
        screen: 0,
      })
      if (res.code === 0) {
        if (res.data) {
          state.deviceList.forEach(item => {
            item.unreadCount = res.data[item.device_sn]
          })
        }
      }
    } catch (error) {
      console.error("Error fetching get_unread_count:", error)
    }
  }

  const setDeviceList = (newList: deviceData[]) => {
    state.deviceList = newList
  }
  
  return { state, getDeviceList, setDeviceList, get_unread_count }
})
