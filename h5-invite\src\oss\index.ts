import OSS from 'ali-oss'
import { getOssKey } from '@/api/index'
import config from '@/config'


let ossSTS:any

// 从后端服务获取 STS 临时访问凭证，这个让后端照着阿里云的文档写，拿到一些keyid和临时秘钥啥的
async function getOSSSTS() {
    const res:any = await getOssKey(config.token)
    if (res.resultCode === 0) {
        // 设置保存时间为一小时
        const expires:any = Date.now() + 1000 * 60 * 60;
        localStorage.setItem('timeKey',expires);
        localStorage.setItem('ossSTS',JSON.stringify(res.content));
        return res.content;
    }
}


export async function authTemps() {
    if(!localStorage.getItem('timeKey')){
        ossSTS = await getOSSSTS()
    }
    // 读取并检查是否过期
    const time:any = Number(localStorage.getItem('timeKey'))
    if (time < Date.now()) {
        localStorage.removeItem('timeKey')
        localStorage.removeItem('ossSTS')
        ossSTS = await getOSSSTS()
    }
    if(ossSTS == undefined){
        const day:any = localStorage.getItem('ossSTS')
        ossSTS = JSON.parse(day)
    }
    return new OSS({
        accessKeyId: ossSTS.AccessKeyId,
        accessKeySecret: ossSTS.AccessKeySecret,
        bucket: "usw-aiwit-chat",
        stsToken:ossSTS.SecurityToken,
        region:"oss-us-west-1",
    })
}


// 上传文件到阿里云 OSS
export async function uploadToOSS(name:any,file:any) {
    const client = await authTemps();
    const result = await client.put(name, file);
    return result.url;
}