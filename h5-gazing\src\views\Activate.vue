<template>
  <div class="pages">
    <div class="login-top">
      <img class="login-img" src="../assets/logo.png" alt="logo" />
    </div>
    <div class="tip-text" v-if="activateType === '1'">您已注册成功，请查收邮件激活即可使用。</div>
    <div class="tip-text" v-else>{{ msg?msg:'激活成功，请登陆。' }}</div>
    <div class="reset-sub">
      <van-button
        round
        block
        type="primary"
        color="#6D24D3"
        @click="goLogin"
      >
        去登陆
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref,onMounted } from "vue"
import { useRoute,useRouter } from "vue-router";

const route = useRoute()
const router = useRouter()
const activateType = ref<string>(String(route.params.type))
const msg = ref<any>('')

const goLogin = ()=>{
  router.push("/login/1")
}

onMounted(()=>{
  const str = window.location.search
  const match = str.match(/&msq=([^&]*)/)
  msg.value = (match && match[1])?.replace(/%20/g,' ')
})
</script>

<style lang="less" scoped>
.login-top {
  padding-top: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 50px;

  .login-img {
    width: 125px;
    height: 100px;
  }
}
.tip-text {
  font-size: 16px;
  color: #333;
  text-align: center;
}
.reset-sub {
  padding: 50px 16px 0 16px;
  margin-bottom: 20px;
}
</style>
