<template>
  <div class="pages">
    <Header :title="config.device.nickname" :left-type="1">
      <div class="title-edit" v-if="config.device.is_primary" @click="saveVolume">保存</div>
    </Header>
    <div class="volume-box">
      <div class="volume-title">门铃音量</div>
      <div class="volume-tip">按设备门铃时的门铃响音大小</div>
      <div class="volume-sex">
        <div class="volume-left">
          <img class="volume-left-img" :src="config.device.doorbell_diagram" alt="" />
        </div>
        <div class="volume-data">
          <div class="volume-num">{{ ring_volume }}</div>
          <img
            class="volume-img"
            src="https://cache.gdxp.com/acce/assets/facility/seeing_max.png"
            alt=""
          />
          <div class="volume-link">
            <van-slider vertical reverse bar-height="14px" active-color="#1BA674" />
          </div>
          <img
            class="volume-img"
            src="https://cache.gdxp.com/acce/assets/facility/min.png"
            alt=""
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import config from "@/config"
import Header from "@/components/Header.vue"
import { ref } from "vue"
import { change_volume } from "@/api"
import { showToast } from "vant"
import { setVolume } from "@/components/WebRTCClient"

const ring_volume = ref(config.device.volume)
const saveVolume = () => {
  setVolume(ring_volume.value)
  change_volume(config.device_sn, ring_volume.value).then((res: any) => {
    if (res.code === 0) {
      showToast("保存成功")
    }
  })
}
</script>

<style lang="less" scoped>
.title-edit {
  font-size: 15px;
  color: #fff;
  background-color: #1ba674;
  padding: 3px 15px;
  border-radius: 20px;
}
.volume-box {
  background-color: #fff;
  border-radius: 12px;
  margin: 0 16px;
  padding: 10px;
  .volume-title {
    font-size: 24px;
    color: #333;
    margin-bottom: 20px;
  }
  .volume-tip {
    font-size: 15px;
    color: #999;
    margin-bottom: 5vh;
  }
  .volume-sex {
    display: flex;
    justify-content: space-between;
    .volume-left {
      width: 65%;
      .volume-left-img {
        height: 373px;
      }
    }
  }
  .volume-data {
    width: 35%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-between;
    .volume-num {
      font-size: 36px;
      color: #1ba674;
      margin-bottom: 10px;
    }
    .volume-link {
      height: 220px;
      display: flex;
      justify-content: left;
      width: 21px;
      margin-bottom: 20px;
    }
    .volume-img {
      width: 37px;
      height: 33px;
      margin-bottom: 20px;
    }
  }
}
</style>
