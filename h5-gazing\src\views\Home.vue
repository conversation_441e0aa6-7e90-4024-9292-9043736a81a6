<template>
  <div class="container">
    <HomeHeader />
    <HomeVideoList />
    <Tabbar :active-index="0"/>
  </div>
</template>

<script lang="ts">
import HomeHeader from "@/components/HomeHeader/index.vue"
import Tabbar from "@/components/Tabbar/index.vue"
import HomeVideoList from "@/components/HomeVideoList/index.vue"
import { MQTTInit } from "@/components/WebRTCClient"
import { onMounted, defineComponent } from "vue"
import config from "@/config"

export default defineComponent({
  name: "home",
  components: {
    HomeHeader,
    HomeVideoList,
    Tabbar,
  },
  setup() {
    onMounted(() => {
      if (config.mqtt_status === 0) {
        MQTTInit()
      }
    })
  },
})
</script>

<style lang="less" scoped>
.container {
  min-height: 100vh;
  background: #f1f1f1;
}
</style>
