import config from "@/config"
import { showLoadingToast } from "vant"
import axios from "axios"

/**
 * @description 获取url地址参数
 * @param url
 * @returns {object|boolean}
 */

export function getUrlParamsObject(url: string) {
  if (url.indexOf("?") == -1) return false
  let arr = url.split("?")
  let params = arr[1].split("&")
  let obj = {} as any
  for (let i = 0; i < params.length; i++) {
    let param = params[i].split("=")
    obj[param[0]] = param[1]
  }
  return obj
}

/**
 * @description 赋值全局变量参数
 * @param obj
 * @returns {object}
 * @param obj1
 * @returns {object}
 */
export function getConfig(obj: object, obj1: object) {
  obj = Object.assign(obj, obj1)
}

// 处理安卓随机数据中的id
export function getRandomElement(arr: any) {
  if (arr.length === 0) return null
  const randomIndex = Math.floor(Math.random() * arr.length)
  return arr[randomIndex]
}

// 点击通知APP返回首页
export function click_dismiss() {
  try {
    if (config.platform == "android") {
      window.android.click_dismiss("返回首页")
    } else {
      window.webkit.messageHandlers.click_dismiss.postMessage("返回首页")
    }
  } catch {
    console.log("不在内嵌手机端")
  }
}

// 点击通知APP发送参数
export function send_oss_params() {
  try {
    if (config.platform == "android") {
      window.android.send_oss_params("发送oss相关参数")
    } else {
      window.webkit.messageHandlers.send_oss_params.postMessage("发送oss相关参数")
    }
  } catch {
    console.log("不在内嵌手机端")
  }
}

export function init_params(data: string) {
  if(data){
    config.ossItem = JSON.parse(data)
  }
}

// 点击套餐订阅跟APP交互
export function subscribe(item: any) {
  showLoadingToast({
    message: "Loading...",
    forbidClick: true,
  })
  try {
    if (config.platform == "android") {
      window.android.click_subscribe(item.os_pay_id)
    } else {
      window.webkit.messageHandlers.click_subscribe.postMessage({ ...item })
    }
  } catch {
    console.log("不在内嵌手机端")
  }
}
