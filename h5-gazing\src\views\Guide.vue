<template>
  <div class="video-container">
    <video ref="videoPlayer" class="fullscreen-video" autoplay muted loop playsinline webkit-playsinline>
      <source src="../assets/video.mp4" type="video/mp4" />
      Your browser does not support the video tag.
    </video>
    <div class="logo-name">Camtro</div>
    <div class="logo-box">
      <van-button class="logo-btn" to="/login/1" color="#1E6FE8" round type="primary">
        登录
      </van-button>
      <van-button class="logo-btn" to="/login/2" color="#1E6FE8" round type="primary">
        注册
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue"

const videoPlayer = ref<HTMLVideoElement | null>(null)

onMounted(() => {
  if (videoPlayer.value) {
    videoPlayer.value.width = window.innerWidth
    videoPlayer.value.height = window.innerHeight
  }
})
</script>

<style lang="less" scoped>
.video-container {
  position: relative;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: #000;
}
.fullscreen-video {
  position: absolute;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  object-fit: cover;
  z-index: 1;
}
.logo-name {
  font-size: 32px;
  color: #fff;
  font-weight: bold;
  position: absolute;
  left: 50%;
  top: 35%;
  transform: translate(-50%, -50%);
  z-index: 2;
}
.logo-box {
  position: absolute;
  left: 50%;
  bottom: 10%;
  transform: translate(-50%);
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 89.5%;
  padding: 0 20px;
  z-index: 2;
}
.logo-btn {
  width: 140px;
  height: 46px;
  font-size: 16px;
  font-weight: 500;
}
</style>
