<template>
  <div class="free-badge-box">
    <img class="free-badge" src="../assets/3.png" alt="" />
  </div>
  <div class="invite-top">Free Camera Replacement!</div>
  <div class="invite-btn">
    <div class="invite-box-btn" @click="deviceAddress">Enter Address</div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["deviceAddress"])
const deviceAddress = () => {
  emit("deviceAddress")
}
</script>

<style lang="less" scoped>
.free-badge-box {
  position: absolute;
  right: 0;
  top: 0;
}

.free-badge {
  animation: pulse 2s infinite;
  width: 100px;
  height: 48px;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

.invite-top {
  font-size: 28px;
  color: #fff;
  font-weight: bold;
  word-break: break-word;
  text-align: center;
}
.invite-btn {
  text-align: center;
  padding-top: 20px;
  .invite-box-btn {
    display: inline-block;
    background-color: #4caf50;
    color: white;
    font-size: 18px;
    font-weight: bold;
    padding: 12px 30px;
    border-radius: 30px;
    text-decoration: none;
    position: relative;
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
    transition: all 0.3s ease;
  }
}
</style>
