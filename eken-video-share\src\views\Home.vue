<template>
  <div class="page-wrapper">
    <div class="container">
      <div class="video-section">
        <div class="video-player">
          <div class="video-header">
            <div class="brand-logo">
              <img src="../assets/home/<USER>" alt="" />
            </div>
          </div>
          <div class="video-content">
            <video
              class="video"
              v-if="state.mdl.url && state.mdl.url.indexOf('.mp4') !== -1"
              controls
              autoplay
              muted
              loop
              preload="auto"
              disablePictureInPicture
              controlslist="nodownload noremoteplayback"
            >
              <source :src="state.mdl.url" type="video/mp4" />
              Your browser does not support the video tag.
            </video>
            <img v-else class="video" :src="state.mdl.url" alt="">
          </div>
        </div>
      </div>

      <div class="info-section">
        <div class="up">
          <div class="header">
            <h1 class="title">{{ state.mdl.tag }}</h1>
          </div>
          <div class="details">
            <div class="detail">
              <div class="icon-wrapper home-icon">
                <img src="../assets/home/<USER>" alt="" />
              </div>
              <span class="detail-text">{{ state.mdl.device_name }}</span>
            </div>
            <div class="detail">
              <div class="icon-wrapper location-icon">
                <img src="../assets/home/<USER>" alt="" />
              </div>
              <div class="location-info">
                <span class="detail-text">{{ state.mdl.addr }}</span>
                <span class="time-text">{{ state.mdl.time }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="down">
          <div class="quote-section">
            <h2 class="main-quote">One bird a day keeps the doctor away.</h2>
            <p class="sub-quote">
              Subscribe for a daily moment of nature in your inbox, every morning.
            </p>
          </div>
          <div class="subscribe-section">
            <input
              v-model="email"
              placeholder="Email"
              type="email"
              class="email-input"
              :border="false"
            />
            <van-button type="primary" class="subscribe-btn" block @click="subscribe">
              Subscribe
            </van-button>
          </div>
        </div> -->
      </div>
    </div>
    <!-- <div class="flooter">
      <a href="#" class="brand-logo">
        <img src="../assets/home/<USER>" alt="" />
      </a>
      <div class="footer-text">
        <p>
          This moment was captured with a
          <a href="#">Bird Buddy Smart Bird Feeder</a>
        </p>
      </div>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue"
import { useRouter } from "vue-router"
import { getVideo } from "@/api"
import config from "@/config"
import type { videoData } from "@/api/base"

const email = ref("")
const router = useRouter()
const state = reactive({
  mdl: {} as videoData,
})

const goToMore = () => {
  router.push("/more")
}

const subscribe = () => {
  if (email.value) {
    console.log("Subscribing with email:", email.value)
  }
}

const fetch = () => {
  getVideo(config.p).then((res: any) => {
    console.log(res)
    if (res.resultCode === 0) {
      state.mdl = res.content
    }
  })
}

onMounted(() => {
  fetch()
})
</script>

<style lang="less" scoped>

.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 4rem);
  padding-top: 4rem;
  gap: 2rem;
}

.container {
  width: 40vw;
  min-width: 800px;
  margin: 0 auto;
  aspect-ratio: 1.3;
  display: flex;
  border-radius: 0.8rem;
  border: 1px solid #cbd5e1;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.video-section {
  flex: 1.6;
  background-color: #3a3a3a;
}

.video-player {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.video-header {
  display: flex;
  padding: 16px;
  align-items: center;
}

.brand-logo img {
  height: 36px;
}

.video-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.video {
  width: 100%;
  max-width: 100%;
  height: auto;
  border-radius: 30px;
}

.info-section {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow-y: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-top: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid #cbd5e1;
}

.title {
  font-size: 27px;
  font-weight: 400;
  font-family: "Georgia", serif;
  letter-spacing: -0.5px;
  text-transform: scale(1, 1.2);
  transform: scaleY(1.2);
  margin: 0;
  margin-bottom: 0.5rem;
}

.discover-btn {
  border: 1px solid #171717;
  color: #171717;
  font-size: 13px;
  padding: 4px 8px;
  border-radius: 22px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 2px;
}

.discover-btn:hover {
  background-color: #f6f8fa;
}

.details {
  margin-bottom: 36px;
}

.detail {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 2px;

  img {
    width: 36px;
    height: 36px;
  }
}

.detail-text {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
  margin-left: 18px;
}

.location-info {
  display: flex;
  flex-direction: column;
}

.time-text {
  font-size: 16px;
  color: #668585;
  line-height: 1.4;
  margin-left: 18px;
}

.quote-section {
  text-align: center;
}

.main-quote {
  font-size: 27px;
  font-weight: 400;
  line-height: 1.2;
  font-family: "Georgia", serif;
  letter-spacing: -0.5px;
  text-transform: scale(1, 1.2);
  transform: scaleY(1.2);
}

.sub-quote {
  font-size: 16px;
  margin-bottom: 16px;
  line-height: 1.3;
}

.subscribe-section {
  display: flex;
  flex-direction: column;
  padding: 2px 0 48px 0;
}

.email-input {
  height: 58px;
  background-color: #f9fafb;
  border: 1px solid #6b7280;
  border-radius: 30px;
  padding: 0 26px;
  margin-bottom: 10px;
}

.email-input::placeholder {
  font-size: 16px;
}

.subscribe-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #003333;
  border: none;
  border-radius: 28px;
  font-size: 18px;
  font-weight: 600;
  color: white;
  height: 56px;
}

.subscribe-btn:hover {
  background-color: #00cc66;
}

.flooter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
  background-color: #171717;
  border-top-left-radius: 38px;
  border-top-right-radius: 38px;
  padding: 16px 26px;
  margin-top: auto;

  .footer-text {
    color: #668585;
    font-size: 14px;
    text-align: right;
  }

  .footer-text a {
    color: #668585;
    text-decoration: underline;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .container {
    width: 85vw;
    min-width: 600px;
  }
}

@media (min-width: 1025px) and (max-width: 1440px) {
  .container {
    width: 60vw;
    min-width: 600px;
  }
}

@media (min-width: 1441px) and (max-width: 1919px) {
  .container {
    width: 50vw;
    min-width: 600px;
  }
}

@media screen and (max-width: 767px) {
  .page-wrapper {
    padding-top: 2rem;
    gap: 1rem;
  }

  .container {
    flex-direction: column;
    width: calc(100% - 2rem);
    min-width: auto;
    height: calc(100vh - 4rem);
    aspect-ratio: auto;
  }

  .video-section {
    flex: 1;
  }

  .info-section {
    flex: 1;
  }
}
</style>
