<template>
  <div class="container">
    <Header title="流量充值" :left-type="1" />
    <div class="top-title">Buy Plan</div>
    <div class="list">
      <div
        class="list-item"
        @click="selectFlow(item)"
        v-for="item in state.goodsDetails.attrs"
        :key="item.product_attr_value_id"
      >
        <div class="item-title" v-if="item.suk">{{ item.suk[0] }}</div>
        <div class="item-text">{{ item.price }} USD</div>
      </div>
    </div>
  </div>
  <!-- 支付弹框 -->
  <PaymentPopup :orderId="''" :price="Number(totalPrice)" ref="paypalHtml" />
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import config from "@/config"
import { md5 } from "js-md5"
// import { submitOrder, apple_pay_wallets } from "@/api/index"
import PaymentPopup from "@/components/PaymentPopup.vue"
import Header from "@/components/Header.vue"
import type { goodsListProduct, defaultSkuData } from "@/api/base"

const state = reactive({
  goodsDetails: {
    product_id: 136,
    store_id: 0,
    master_id: 0,
    model: "流量充值",
    sku: "",
    upc: "",
    ean: "",
    jan: "",
    isbn: "",
    mpn: "",
    location: "",
    variant: null,
    override: null,
    quantity: 988,
    stock_status_id: 5,
    image:
      "https://hk-aiwit-ota.oss-cn-hongkong.aliyuncs.com/acce/mall-img/catalog/20241010/1503001728543780_p3tdguofzt.png",
    manufacturer_id: 0,
    shipping: 1,
    price: 9.99,
    points: 0,
    tax_class_id: 0,
    date_available: "2024-10-30",
    weight: "0.00000000",
    weight_class_id: 2,
    length: "0.00000000",
    width: "0.00000000",
    height: "0.00000000",
    length_class_id: 1,
    subtract: 1,
    minimum: 1,
    rating: 0,
    sort_order: 0,
    status: 1,
    date_added: "2024-10-10 15:06:28",
    date_modified: "2024-10-30 16:46:40",
    language_id: 1,
    name: "流量充值",
    description: "流量充值",
    tag: "",
    meta_title: "流量充值",
    meta_description: "",
    meta_keyword: "",
    reviews: 0,
    is_virtual: 1,
    attrs: [
      {
        product_id: 136,
        suk: ["2GB 30day"],
        stock: 994,
        sales: 5,
        price: "15.00",
        image:
          "https://hk-aiwit-ota.oss-cn-hongkong.aliyuncs.com/acce/mall-img/catalog/20241030/1644151730277855_nrshmsuuzw.png",
        unique: "9db02628",
        cost: "0.00",
        bar_code: "",
        ot_price: "0.00",
        vip_price: "0.00",
        weight: "0.00",
        volume: "0.00",
        product_attr_value_id: 272,
      },
      {
        product_id: 136,
        suk: ["12GB 180day"],
        stock: 999,
        sales: 0,
        price: "79.90",
        image:
          "https://hk-aiwit-ota.oss-cn-hongkong.aliyuncs.com/acce/mall-img/catalog/20241030/1644181730277858_4cufxmztxu.png",
        unique: "734ce59c",
        cost: "0.00",
        bar_code: "",
        ot_price: "0.00",
        vip_price: "0.00",
        weight: "0.00",
        volume: "0.00",
        product_attr_value_id: 273,
      },
      {
        product_id: 136,
        suk: ["24GB 360day"],
        stock: 999,
        sales: 0,
        price: "139.90",
        image:
          "https://hk-aiwit-ota.oss-cn-hongkong.aliyuncs.com/acce/mall-img/catalog/20241030/1644211730277861_l1jftiy2ej.png",
        unique: "b4c07060",
        cost: "0.00",
        bar_code: "",
        ot_price: "0.00",
        vip_price: "0.00",
        weight: "0.00",
        volume: "0.00",
        product_attr_value_id: 274,
      },
      {
        product_id: 136,
        suk: ["50GB 360day"],
        stock: 999,
        sales: 0,
        price: "229.90",
        image:
          "https://hk-aiwit-ota.oss-cn-hongkong.aliyuncs.com/acce/mall-img/catalog/20241030/1644241730277864_2tfw3ickke.png",
        unique: "981f5e0a",
        cost: "0.00",
        bar_code: "",
        ot_price: "0.00",
        vip_price: "0.00",
        weight: "0.00",
        volume: "0.00",
        product_attr_value_id: 275,
      },
    ],
  } as unknown as goodsListProduct,
})
const totalPrice = ref<number>(0)
const paypalHtml = ref<(HTMLElement & { handleDisplay: (data: string) => {} }) | null>(null)
const selectFlow = (item: defaultSkuData) => {
  const params = [
    {
      name: state.goodsDetails.name,
      image: state.goodsDetails.image,
      product_id: state.goodsDetails.product_id,
      price: state.goodsDetails.price,
      product_attrs: item,
      skuList: [],
      quantity: 1,
      model: state.goodsDetails.model,
      master_id: state.goodsDetails.master_id,
      is_virtual: state.goodsDetails.is_virtual,
      reward: state.goodsDetails.reward ? state.goodsDetails.reward : "0",
    },
  ]
  // submitOrder({
  //   shipping_code: "flat",
  //   postcode: "0",
  //   products: params,
  //   cart_ids: [],
  //   remark: "",
  //   sim_device: {
  //     device_sn: config.device_sn,
  //     name: config.device_name,
  //   },
  //   sign: md5(`${config.token}/${0}/${"flat" + config.key}`),
  // }).then((res: any) => {
  //   if (res.resultCode === 0) {
  //     if (config.platform === "ios") {
  //       totalPrice.value = Number(res.content.total)
  //       getPaySecret(String(res.content.order_id), Number(res.content.total))
  //     } else if (config.platform === "android") {
  //       console.log(
  //         `${window.location.origin}/h5-mall/googlepay.html?token=${config.token}&order=${
  //           res.content.order_id
  //         }&price=${res.content.total}&app_name=${config.app_name.toLowerCase()}`
  //       )
  //       window.android.click_transfer(
  //         `${window.location.origin}/h5-mall/googlepay.html?token=${config.token}&order=${
  //           res.content.order_id
  //         }&price=${res.content.total}&app_name=${config.app_name.toLowerCase()}`
  //       )
  //     }
  //   }
  // })
}

const getPaySecret = (orderId: string, price: number) => {
  // apple_pay_wallets(
  //   orderId,
  //   "USD",
  //   price,
  //   md5(`${config.token}/${orderId}/'USD'/${price + config.key}`)
  // ).then((res: any) => {
  //   if (res.resultCode === 0) {
  //     config.intentId = res.content.id
  //     config.clientSecret = res.content.client_secret
  //     paypalHtml.value?.handleDisplay(orderId)
  //   }
  // })
}

// const fetch = () => {
//   get_virtual_products().then((res: any) => {
//     state.goodsDetails = res.content
//   })
// }

// onMounted(() => {
//   fetch()
// })
</script>

<style lang="less" scoped>
.container {
  min-height: 100vh;
  .top-title {
    padding-top: 50px;
    padding-left: 16px;
    font-size: 28px;
    color: #333;
    margin-bottom: 15px;
  }
  .list {
    display: flex;
    padding: 0 16px;
    flex-wrap: wrap;
    justify-content: space-between;
    color: rgb(76, 201, 135);
    font-size: 14px;
    .list-item {
      width: 48%;
      height: 75px;
      border: 1px solid rgb(76, 201, 135);
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      border-radius: 5px;
      margin-bottom: 10px;
      .item-title {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
