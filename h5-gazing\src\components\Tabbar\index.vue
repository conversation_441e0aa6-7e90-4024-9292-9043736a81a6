<template>
  <div class="tabbar-container">
    <div class="grid-wrapper">
      <div
        class="grid-item"
        v-for="(item, index) in state.list"
        :key="item.text"
        @click="handleTabClick(index, item.path)"
      >
        <img
          class="icon"
          :src="state.activeIndex === index ? item.activeIcon : item.icon"
          alt=""
        />
        <span class="text" :class="{ active: state.activeIndex === index }">{{ item.text }}</span>
      </div>
    </div>
    <div class="custom-container">
      <img
        class="icon"
        @click="columnTabClick"
        src="../../assets/tabbar/04.png"
        alt=""
      />
      <span class="text">添加设备</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import icon01 from "@/assets/tabbar/01.png";
import active01 from "@/assets/tabbar/01active.png";
// import icon03 from "@/assets/tabbar/03.png";
// import active03 from "@/assets/tabbar/03active.png";
import { useRouter } from "vue-router";
import { useRoute } from "vue-router";
import { ref, onMounted,onActivated } from "vue";
const state = ref({
  activeIndex: 0,
  list: [
    { icon: icon01, activeIcon: active01, text: "设备", path: "/home" },
    // { icon: icon03, activeIcon: active03, text: "对话", path: "/chatList" }
  ],
});
const router = useRouter();
const route = useRoute();

// handleTabClick
const handleTabClick = (index: number, path: string) => {
  state.value.activeIndex = index;
  navigateReplace(path);
};

// columnTabClick
const columnTabClick = () => {
  navigateReplace("/device");
};

// navigateReplace
const navigateReplace = (path: string) => router.push(path);

// 初始化页面tabIndex
const init = ()=>{
  const routeIndex = state.value.list.findIndex(
    (item) => item.path == route.path
  );
  state.value.activeIndex = routeIndex;
}

// 初始化页面tabIndex
onMounted(() => {
  init()
});

onActivated(() => {
  init()
});
</script>

<style lang="less" scoped>
.tabbar-container {
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 10;
  display: flex;
  background-color: #fff;
  padding-bottom: constant(safe-area-inset-bottom, 0px);
  padding-bottom: env(safe-area-inset-bottom, 0px);
  .grid-wrapper {
    display: flex;
    width: 100%;
    justify-content: space-around;
    padding-top: 10px;
    padding: 10px 10% 0 10%;
    .grid-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      .icon {
        width: 26px;
        height: 26px;
        object-fit: contain;
      }
      .text {
        margin-top: 5px;
        font-family: Roboto;
        font-size: 10px;
        font-weight: 400;
        line-height: 12.89px;
        text-align: center;
        color: #666;
        &.active {
          color: #1E6FE8;
        }
      }
    }
  }
  .custom-container {
    margin-right: 15px;
    margin-top: -11px;
    z-index: 9;
    .icon {
      overflow: hidden;
      border-radius: 50%;
      width: 69px;
      height: 69px;
      background-color: #fff;
      padding: 8px;
      box-sizing: border-box;
    }
    .text {
      display: block;
      font-family: Roboto;
      font-size: 10px;
      font-weight: 400;
      line-height: 12.89px;
      text-align: center;
      color: #666;
      margin: -8px 0 8px;
    }
  }
}
</style>
