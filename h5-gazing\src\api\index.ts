import http from "./request"
import type {
  loginData,
  registerData,
  videoParams,
  appleParams,
  googleParams,
  sortsParams,
  chatListsParams,
  screenSayParams,
  screenReadParams,
  screenUnreadCountParams,
} from "./base"
import config from "@/config"

/**
 * @summary 获取设备列表
 */
export async function get_by_user() {
  return await http("/user/get_device", {
    method: "POST",
    params: {},
  })
}

/**
 * @summary 获取回放列表
 */
export async function get_videos(params: videoParams) {
  return await http("/user/get_videos", {
    method: "POST",
    params,
  })
}

/**
 * @summary 获取视频回放计数
 * @param {string} device_sn 设备ID
 */
export async function get_videos_count(device_sn: string) {
  return await http("/user/get_videos_count", {
    method: "POST",
    params:{ device_sn },
  })
}

/**
 * @summary 删除回放视频
 */
export async function delete_videos(device_sn:string,paths:Array<any>) {
  return await http("/user/delete_videos", {
    method: "POST",
    params:{device_sn,paths},
  })
}

/**
 * @summary 生成二维码
 */
export async function get_qr_key() {
  return await http("/user/get_qrkey", {
    method: "POST",
    params: {},
  })
}

/**
 * @summary 生成分享码
 */
export async function get_share_qrkey(product_key: string, device_sn: string) {
  return await http("/user/share_qrkey", {
    method: "POST",
    params: { product_key, device_sn },
  })
}

/**
 * @summary 扫码分享设备
 */
export async function get_device_share(qr_key: string) {
  return await http("/user/device_share", {
    method: "POST",
    params: { qr_key },
  })
}

/**
 * @summary 扫码注册设备
 */
export async function network_config(qr_code: string) {
  return await http("/user/network_config", {
    method: "POST",
    params: { qr_code },
  })
}

/**
 * @summary 添加静默时间
 */
export async function mute_notifications(mute_min: number, type: string) {
  return await http("/user/mute_notifications", {
    method: "POST",
    params: { mute_min, type },
  })
}

/**
 * @summary 登陆
 * @param {string} params.username 邮箱
 * @param {number} params.password 密码
 */
export async function submitLogin(params: loginData) {
  return await http("/user/login", {
    method: "POST",
    params: params,
  })
}

/**
 * @summary 登出
 */
export async function submitLogout() {
  return await http("/user/logout", {
    method: "POST",
    params: {},
  })
}

/**
 * @summary 注册
 * @param {string} params.username 邮箱
 * @param {number} params.password 密码
 */
export async function submitRegister(params: registerData) {
  return await http("/user/register", {
    method: "POST",
    params: params,
  })
}

// 获取m3u8文件地址
export async function get_m3u8(sign: string, endpoint: string, bucket_name: string, path: string) {
  return await http(
    `/sts/get_m3u8?sign=${sign}&endpoint=${endpoint}&bucket_name=${bucket_name}&path=${path}`,
    { method: "", params: {} },
    { "Content-Type": "application/vnd.apple.mpegurl", "x-Token": `${config.token}`, "x-Date": "" }
  )
}

/**
 * @summary 删除设备
 * @param {string} product_key 设备KEY
 * @param {string} device_sn 设备ID
 */
export async function delFacility(product_key: string, device_sn: string,reasons:Array<{id:number,content:string}>,remark:string) {
  return await http("/user/delete_device", {
    method: "POST",
    params: { product_key, device_sn,reasons,remark },
  })
}

// 获取阿里云OSS服务key
export async function getOssKey(endpoint: string) {
  return await http("/cloud/get_sts", {
    method: "POST",
    params: { endpoint },
  })
}

/**
 * @summary 重置密码
 * @param {string} old_password 旧密码
 * @param {string} new_password 新密码
 */
export async function reset_password_proactively(old_password: string, new_password: string) {
  return await http("/user/reset_password_proactively", {
    method: "POST",
    params: { old_password, new_password },
  })
}

/**
 * @summary 重置密码
 * @param {string} new_password 新密码
 * @param {string} token 跳转带过来的token
 * @param {string} random 跳转带过来的random
 */
export async function reset_password_passive(new_password: string, token: string, random: string) {
  return await http("/user/reset_password_passive", {
    method: "POST",
    params: { new_password, token, random },
  })
}

/**
 * @summary 忘记密码发送邮件
 * @param {string} email 邮箱
 */
export async function forgot_password(email: string) {
  return await http("/user/forgot_password", {
    method: "POST",
    params: { email },
  })
}

/**
 * @summary Apple pay创建钱包
 * @param {string} order_id 订单ID
 * @param {string} currency_code 货币类型
 * @param {number} amount 订单金额
 * @param {string} sign 校验码
 */
export async function apple_pay_wallets(
  order_id: string,
  currency_code: string,
  amount: number,
  sign: string
) {
  return await http(
    `/mall/pay/airwallex_create_payment_intent/${order_id}/${currency_code}/${amount}/${sign}`
  )
}

/**
 * @summary 修改设备名称
 * @param {string} product_key 设备KEY
 * @param {string} device_sn 设备ID
 * @param {string} nickname 设备名称
 */
export function editDevice(product_key: string, device_sn: string, nickname: string) {
  return http("/user/reset_device_name", {
    method: "POST",
    params: { product_key, device_sn, nickname },
  })
}

/**
 * @summary 分享者列表
 * @param {string} product_key 设备KEY
 * @param {string} device_sn 设备ID
 */
export function device_share_list(product_key: string, device_sn: string) {
  return http("/user/device_share_list", {
    method: "POST",
    params: { product_key, device_sn },
  })
}

/**
 * @summary 删除分享者
 * @param {string} user_id 用户ID
 * @param {string} product_key 设备KEY
 * @param {string} device_sn 设备ID
 */
export function delete_share(user_id: string, product_key: string, device_sn: string) {
  return http("/user/delete_share", {
    method: "POST",
    params: { user_id, product_key, device_sn },
  })
}

/**
 * @summary 刷新电量
 * @param {string} product_key 设备KEY
 * @param {string} device_sn 设备ID
 */
export function refresh_device_battery(product_key: string, device_sn: string) {
  return http("/user/sync_device_battery", {
    method: "POST",
    params: { product_key, device_sn },
  })
}

/**
 * @summary 设备转让
 * @param {string} user_id 用户ID
 * @param {string} product_key 设备KEY
 * @param {string} device_sn 设备ID
 * @param {string} email 邮箱
 */
export function device_transfer(
  user_id: string,
  product_key: string,
  device_sn: string,
  email: string
) {
  return http("/user/device_transfer", {
    method: "POST",
    params: { user_id, product_key, device_sn, email },
  })
}

/**
 * @summary 邮箱分享
 * @param {string} product_key 设备KEY
 * @param {string} device_sn 设备ID
 * @param {string} email 邮箱
 * @param {string} device_name 设备名称
 */
export function device_share_email(
  product_key: string,
  device_sn: string,
  email: string,
  device_name: string
) {
  return http("/user/device_share_email", {
    method: "POST",
    params: { product_key, device_sn, email, device_name },
  })
}

/**  ota升级
 * @param {Array} device_sns 设备ID
 */
export function check_ota(device_sns: Array<string>,enable_test:number) {
  return http("/user/check_ota", {
    method: "POST",
    params: { device_sns,enable_test },
  })
}

/**
 * @param {string} device_sn 设备ID
 */
export function set_device_timezone(device_sn: string, timezone: string) {
  return http("/user/set_device_timezone", {
    method: "POST",
    params: { device_sn, timezone },
  })
}

/**
 * @param {string} device_sn 设备ID
 * @param {string} function_bitmap 门铃通知 移动通知
 */
export function set_device(device_sn: string, function_bitmap: number) {
  return http("/user/set_device_function", {
    method: "POST",
    params: { device_sn, function_bitmap },
  })
}

/**
 * @param {string} device_sn 设备ID
 * @param {string} volume 门铃音量
 */
export function change_volume(device_sn: string, volume: number) {
  return http("/user/change_volume", {
    method: "POST",
    params: { device_sn, volume },
  })
}

/**
 * 获取添加设备页面的配置信息
 */
export function get_add_list() {
  return http("/user/get_add_list", {
    method: "POST",
    params: {},
  })
}

// 获取设备列表套餐
export function getExpand() {
  return http("/sub/cloud_product_list", {
    method: "POST",
    params: {},
  })
}

// ios上报套餐失败
export function saveIosLog(params: appleParams) {
  return http("/sub/apple_reported_order", {
    method: "POST",
    params: params,
  })
}

// 安卓上报套餐失败
export function saveAndLog(params: googleParams) {
  return http("/sub/google_reported_order", {
    method: "POST",
    params: params,
  })
}

// 注销账号
export function deregister(password:string) {
  return http("/user/deregister", {
    method: "POST",
    params: {password},
  })
}

// 用户设备排序
export function sort_device(sorts:Array<sortsParams>) {
  return http("/user/sort_device", {
    method: "POST",
    params: {sorts},
  })
}

// 消息列表(接口不需要 gz-api 开头, 重新定义 baseURL)
export function chat_lists(chat: chatListsParams) {
  return http(`/service_api/chat_lists/${chat.device_sn}/${chat.screen}/${chat.page}`, {
    method: "",
    params: {},
  })
}

// 发送消息
export function screen_say(screen: screenSayParams) {
  return http("/service_api/screen_say", {
    method: "POST",
    params: screen,
  })
}

// 已读信息
export function screen_read(screen: screenReadParams) {
  console.log(screen)
  return http("/service_api/screen_read", {
    method: "POST",
    params: screen,
  })
}

// 获取每个设备未读消息数目
export function getUnreadCount(screen: screenUnreadCountParams) {
  return http("/service_api/get_unread_count", {
    method: "POST",
    params: screen,
  })
}