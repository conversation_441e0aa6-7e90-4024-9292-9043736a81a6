<template>
  <div class="pages">
    <div class="ios-top"></div>
    <div class="pages-title">
      <van-icon name="arrow-left" size="25" color="#FFFFFF" @click="click_dismiss" />
    </div>
    <div class="text-content">
      <div class="subtitle">
        <div>Bring Your Family Closer to</div>
        <div>Nature—Unlock</div>
        <div>Premium Birdwatching Today</div>
      </div>
      <div class="description">
        <div>Experience unlimited AI- </div>
        <div>powered bird discovery with premium features</div>
        <div>designed for the whole family</div>
      </div>
    </div>
    <div class="card-container">
      <div class="card" v-for="(item, index) in state.list" :key="index">
        <div class="icon">
          <img v-if="item.icon" :src="item.icon" alt="" />
        </div>
        <div class="content">
          <div class="title">{{ item.title }}</div>
          <p class="details">{{ item.details }}</p>
        </div>
      </div>
    </div>
    <div class="fixed-bottom">
      <div class="bottom-button" @click="confirm"> Confirm </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import config from "@/config"
import { click_dismiss } from "@/utils"
import { useRouter } from "vue-router";

const router = useRouter()
const state = reactive({
  list: [
    { 
      icon: new URL('@/assets/bird.png', import.meta.url).href, 
      title: `Unlimited AI Bird Recognition Never miss a species again`, 
      details: `Instantly identify 10000+ bird species with 90% accuracy Advanced AI technology 
                powered by millions of bird images Recognition works offline—no internet required 
                in the field Get detailed species information, behavior patterns, and fun facts` 
    },
    { 
      icon: new URL('@/assets/spin.png', import.meta.url).href, 
      title: `Unlimited Bird Knowledge Q&A Your 24/7 bird expert companion`, 
      details: `Ask anything about bird behavior, migration patterns, or breeding habitsGet instant, 
                scientifically-backed answers from our AI ornithologist Perfect for curious kids and 
                passionate bird watchers alike Learn something new with every question—no limits, ever`,
    },
    { 
      icon: new URL('@/assets/cloud.png', import.meta.url).href, 
      title: `Unlimited Cloud Storage & HD Playback Preserve every magical moment`, 
      details: `Store unlimited bird photos and videos in crystal-clear quality Access your collection 
                from any device, anywhere Never worry about running out of space on your phone Share 
                stunning footage with friends and fellow birdwatchers`,
    },
    { 
      icon: new URL('@/assets/consumer.png', import.meta.url).href, 
      title: `Family Sharing Features Make memories together`,
      details: `Store unlimited bird photos and videos in crystal-clear quality Access your collection 
                from any device, anywhere Never worry about running out of space on your phone Share stunning 
                footage with friends and fellow birdwatchers`,
    },
  ],
})

const confirm = () => {
  router.push('/cloudBuy')
}

</script>

<style lang="less" scoped>
.pages {
  height: 100%;
  padding: 0 16px 20px 16px;
  display: flex;
  flex-direction: column;
  background-color: #003333;

  .ios-top {
    height: 40px;
  }
  
  .pages-title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .text-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin: 20px 0;

    .subtitle {
      font-size: 24px;
      font-weight: 500;
      line-height: 1.25;
      text-align: center;
      line-height: 1.5;
      color: #FAC370;
    }

    .description {
      padding: 0 24px;
      text-align: center;
      font-size: 13px;
      color: #A4AFAF;
      line-height: 1.5;
      margin: 0;
    }
  }
  
  .card-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 80px;
  }

  .card {
    background: #1A4848;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: flex-start;
    gap: 12px;

    .icon {
      margin-right: 3px;
      img {
        width: 24px;
        height: 24px;
      }
    }

    .content {
      flex: 1;

      .title {
        font-size: 16px;
        font-weight: 500;
        line-height: 1.25;
        color: #ffffff;
        margin-bottom: 8px;
      }

      .details {
        font-size: 13px;
        color: #A4AFAF;
        line-height: 1.5;
        margin: 0;
        word-break: break-word;
      }
    }
  }

  .fixed-bottom {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 91.5%;
    padding: 27px 16px;
    background-color: #003333;
    .bottom-button {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 44px;
      background: #FAC370;
      border-radius: 22px;
      color: #003333;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>
