<template>
  <div class="pages">
    <Header title="更改Wi-Fi网络" :left-type="1"></Header>
    
    <div class="reset-wifi-web" @click="router.push('/wifiSet')">
      <span class="van-cell__title">更改 Wi-Fi 网络</span>
      <van-icon name="arrow" size="20" color="#999" />
    </div>

    <div class="tip-text">设备正常工作，我想要更换 Wi-Fi 网络</div>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import { useRouter } from "vue-router"

const router = useRouter()
</script>

<style lang="less" scoped>
.reset-wifi-web {
  width: 100%;
  height: 56px;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border-top: 1px solid #E4E8EC;
  border-bottom: 1px solid #E4E8EC;
  background: white;
  margin-bottom: 10px;
  margin-top: 50px;
  .van-cell__title {
    font-size: 16px;
    color: #000000;
  }
}
.tip-text {
  padding: 0 16px;
  font-size: 14px;
  color: #999999;
  margin-bottom: 14px;
}
</style>