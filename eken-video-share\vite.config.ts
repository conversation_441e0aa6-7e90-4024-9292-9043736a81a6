import { fileURLToPath, URL } from 'node:url'

import { defineConfig,loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import postcsspxtoviewport8plugin from 'postcss-px-to-viewport-8-plugin'



export default defineConfig(({ mode })=>{
  const env = loadEnv(mode, process.cwd(), "");
  return  {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    base: './',
    // base: 'https://cache.gdxp.com/acce/meal-dev/', //CND引入静态资源 测试环境
    // base: 'https://cache.gdxp.com/acce/meal-formal/', //CND引入静态资源 正式环境
    server: {
      host: "0.0.0.0",
      port: 5174,
      proxy: {
          [env.VITE_APP_BASE_API]: {
              target: env.VITE_SERVER,
              changeOrigin: true,
              rewrite: (path) => path.replace(/^\/api/, '')
          },
      },
    },
    css: {
      postcss: {
        plugins: [
          postcsspxtoviewport8plugin({
            viewportWidth: 1920, // 最常用的PC设计稿宽度
            unitToConvert: 'px', // 要转换的单位
            unitPrecision: 6, // 单位转换后保留的精度
            propList: ["*"], // 能转化为vw的属性列表
            viewportUnit: 'rem', // 希望使用的视口单位
            fontViewportUnit: 'rem', // 字体使用的视口单位
            selectorBlackList: ['ignore-'], // 需要忽略的CSS选择器，不会转为视口单位，使用原有的px等单位。
            minPixelValue: 1, // 设置最小的转换数值，如果为1的话，只有大于1的值会被转换
            mediaQuery: true, // 改为 true 以支持媒体查询中的单位转换
            replace: true, //  是否直接更换属性值，而不添加备用属性
            exclude: undefined, // 忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
            include: undefined, // 如果设置了include，那将只有匹配到的文件才会被转换
            landscape: false, // 是否添加根据 landscapeWidth 生成的媒体查询条件 @media 
          }),
        ],
      },
    }, 
    build: {
      // assetsDir:'assets_1.1.5',
      target: 'es2015',
      chunkSizeWarningLimit: 1024,
    },
  }
})