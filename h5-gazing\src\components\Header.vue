<template>
  <van-nav-bar
    :fixed="true"
    :title="title"
    :safe-area-inset-top="config.platform === 'ios'"
    @click-left="onClickLeft"
    :border="false"
    class="nav-bar"
  >
    <template #left>
      <div class="header-left" v-if="leftType">
        <van-icon name="arrow-left" color="#303A58" size="18" />
      </div>
      <van-icon v-else name="arrow-left" color="#303A58" size="18" />
    </template>
    <template #right>
      <slot></slot>
    </template>
  </van-nav-bar>
</template>

<script setup lang="ts">
import config from "@/config"
import { Standby } from "@/components/WebRTCClient"
import { closeToast } from "vant"
import { break_bluetooth } from "@/utils"
import { useRouter } from "vue-router";

const props = defineProps({
  title: String,
  leftType: {
    type: Number,
    default: 0,
  },
  videoType: {
    type: Number,
    default: 0,
  },
  wifiType: {
    type: Number,
    default: 0,
  },
})

const router = useRouter()

const onClickLeft = () => {
  if (props.wifiType === 1) {
    break_bluetooth()
    router.go(-2);
    return
  }
  if (props.videoType === 1 && (config.video_state === 1 || config.video_state === 2)) {
    closeToast()
    Standby()
  }
  router.go(-1);
}
</script>

<style lang="less">
:root {
  --van-nav-bar-title-text-color: #222;
  --van-nav-bar-background: #e2e3e5;
}
.nav-bar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}
.header-left {
  display: inline-block;
  width: 36px;
  height: 36px;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
</style>
