<template>
  <div class="my-device">
    <Header title="我的设备" :left-type="1" />
    <div class="device-list">
      <div class="device-item" v-for="item in deviceList" :key="item.device_sn" @click="deviceMsg(item)">
        <div class="device-left">
          <div class="pic-container"><img class="pic-img" :src="item.product_img" alt="" /></div>
          <div class="device-name">{{ item.nickname }}</div>
        </div>
        <van-icon name="arrow" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import { useHomeStore } from "@/stores/home"
import { computed } from "vue"
import type { deviceData } from "@/api/base"
import config from "@/config"
import { useRouter } from "vue-router"

const homeStore = useHomeStore()
const router = useRouter()

const deviceList = computed(() => {
  return homeStore.state.deviceList
})

const deviceMsg = (item: deviceData) => {
  config.product_key = item.product_key
  config.device_sn = item.device_sn
  config.device = item
  config.light_leve.value = config.device.attributes.light_leve
  config.device_config_item = config.device_config[item.project_id]
  router.push({
    path: '/deviceInfo',
    query: { 
      device_status: item.status,
    }
  })
}
</script>

<style lang="less" scoped>
.my-device {
  background-color: #EFF3F6;
  min-height: 100vh;
  .device-list {
    padding-top: 10px;
    .device-item {
      display: flex;
      padding: 0 16px;
      height: 80px;
      align-items: center;
      justify-content: space-between;
      background-color: #fff;
      margin-bottom: 10px;
      .device-left {
        display: flex;
        align-items: center;
        .pic-container {
          height: 70px;
          width: 70px;
          flex-shrink: 0;
          margin-right: 8px;
          background: #eee;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          .pic-img {
            height: 56px;
            width: 48px;
            object-fit: contain;
            flex-shrink: 0;
          }
        }
        .device-name{
          display: flex;
          align-items: center;
          margin-left: 10px;
          font-size: 16px;
          color: #303A58;
        }
      }
    }
  }
}
</style>
