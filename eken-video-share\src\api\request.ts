import axios from 'axios'
import configs from '@/config'
import {
  showToast,
  // showLoadingToast,
  // closeToast,
  setToastDefaultOptions,
} from 'vant'
//  设置提示时效
setToastDefaultOptions({ duration: 3500 })

const service = axios.create({
  // 环境变量，需要在.env文件中配置
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 20000,
})

// loading 次数
// let loadingCount = 0
service.interceptors.request.use(
  (config) => {
    // showLoadingToast({
    //   message: '加载中...',
    //   //禁止背景点击
    //   forbidClick: true,
    // })
    // loadingCount++
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  (response) => {
    // 关闭loading
    // loadingCount--
    // if (loadingCount === 0) {
    //   closeToast()
    // }
    return response
  },
  (error) => {
    // closeToast()
    // 处理异常情况，根据项目实际情况处理或不处理
    if (error && error.response) {
      // 根据约定的响应码处理
      switch (error.response.status) {
        case 403:
          error.message = '拒绝访问'
          break
          case 404:
            error.message = '找不到服务器资源'
            break
        case 502:
          error.message = '服务器端出错'
          break
        default:
          error.message = `连接错误${error.response.status}`
      }
    } else {
      // 超时处理
      error.message = '服务器响应超时，请刷新当前页'
    }
    showToast(error.message)
    return Promise.resolve(error.response)
  }
)

// 封装请求
const Request = (
  url: string,
  options = { method: '', params: {} },
  headers = { 'Content-Type': 'application/json;charset=UTF-8' }
) => {
  let method = options.method || 'get'
  let params = options.params || {}

  if (method === 'get' || method === 'GET') {
    return new Promise((resolve, reject) => {
      service
        .get(url, {
          params: params,
        })
        .then((res) => {
          if (res && res.data) {
            if (res.data.resultCode != 0) {
              showToast(res.data.msg ? res.data.msg : '服务器错误，请稍后再试')
            }
            resolve(res.data)
          }
        })
        .catch((err) => {
          reject(err)
        })
    })
  } else {
    return new Promise((resolve, reject) => {
      service
        .post(url, params, { headers })
        .then((res) => {
          if (res && res.data) {
            if (res.data.resultCode != 0) {
              showToast(res.data.msg ? res.data.msg : '服务器错误，请稍后再试')
            }
            resolve(res.data)
          }
        })
        .catch((err) => {
          reject(err)
        })
    })
  }
}

export default Request
