<template>
  <router-view></router-view>
</template>

<script setup lang="ts">
import config from "./config"
import { getUrlParamsObject, getConfig } from "@/utils"

const str = navigator.userAgent
if (!!str.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
  config.platform = "ios"
} else if (str.indexOf("Android") > -1 || str.indexOf("Adr") > -1) {
  config.platform = "android"
}

// 线上逻辑
//示例路径  https://api.test.gdxp.com/h5/token=8a587f675e471f2edce49e927e2738c3&device_sn=EKDB_216A53E5-E0BE-70EE-2892-FCC31670EE79&cloud_product_id=sub_cloud_storage_30_6m&cycle_days=30&event=1&lang=en&app_name=aiwit&os=1&is_test=1&type=2&sub_id=
if (window.location.href.indexOf("p=") !== -1) {
  const getUrlKey: any = getUrlParamsObject(window.location.href)
  getConfig(config, getUrlKey)

} else {
  // 设置全局参数适用本地调试
  config.p = "01972026-aab4-7a68-bc5a-6855652d5d07"
}
</script>

<style lang="less">
html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Segoe UI, Arial,
    Roboto, "PingFang SC", "miui", "Hiragino Sans GB", "Microsoft Yahei", sans-serif;
  height: 100%;
  background-color: #fff9f1;
  color: #003333;
  min-width: 375px;
  overflow: auto;
  box-sizing: border-box;
}
</style>