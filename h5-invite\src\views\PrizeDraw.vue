<template>
  <div class="container">
    <LuckyWheel
      ref="myLucky"
      width="300px"
      height="300px"
      :prizes="state.prizes"
      :blocks="state.blocks"
      :buttons="state.buttons"
      @start="startCallback"
      @end="endCallback"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue"

const state = reactive({
  blocks: [
    { 
      padding: "22px",
      imgs:[
        {
          src:'https://cache.gdxp.com/acce/assets/mall/chouj.png',
          width:300,
          height:300
        }
      ]
    }
  ],
  prizes: [
    { fonts: [{ text: "8折", top: "20%" }], background: "#FEDC6C" },
    { fonts: [{ text: "6折", top: "20%" }], background: "#FFFAEE" },
    { fonts: [{ text: "5折", top: "20%" }], background: "#FEDC6C" },
    { fonts: [{ text: "祝你好运", top: "20%" }], background: "#FFFAEE" },
  ],
  buttons: [
    {
      radius: "35%",
      background: "transparent",
      imgs:[
        {
          src:'https://cache.gdxp.com/acce/assets/mall/play.png',
          width:80,
          height:102,
          top:-60
        }
      ]
    },
  ],
})

const myLucky = ref<HTMLElement | any>(null)

// 点击抽奖按钮会触发star回调
const startCallback = () => {
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play()
  // 模拟调用接口异步抽奖
  setTimeout(() => {
    // 假设后端返回的中奖索引是0
    const index = 0
    // 调用stop停止旋转并传递中奖索引
    myLucky.value.stop(index)
  }, 3000)
}

const endCallback = (prize: string) => {
  console.log(prize)
}
</script>

<style lang="less" scoped>
.container {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}
</style>
