import { resolve } from 'path'
import { defineConfig, type ConfigEnv, type UserConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { wrapperEnv } from './build/getEnv'
import { createProxy } from './build/proxy'

export default defineConfig(({ mode }: ConfigEnv): UserConfig => {
  const root = process.cwd()
  const env = loadEnv(mode, root)
  const viteEnv = wrapperEnv(env)
  return {
    plugins: [vue()],
    resolve: {
      alias: {
        // '@': fileURLToPath(new URL('./src', import.meta.url))
        '@': resolve(__dirname, './src'),
      },
    },
    base: './',
    // base: 'https://acce.kement.cn/acce/chat-dev/', //CND引入静态资源 测试环境
    // base: 'https://acce.kement.cn/acce/chat-formal/', //CND引入静态资源 正式环境
    server: {
      host: '0.0.0.0',
      port: viteEnv.VITE_PORT,
      open: viteEnv.VITE_OPEN,
      cors: true,
      proxy: createProxy(viteEnv.VITE_PROXY),
    },
    esbuild: {
      pure: viteEnv.VITE_DROP_CONSOLE ? ['console.log', 'debugger'] : [],
    },
    build: {
      assetsDir:'assets_1.1.0',
      target: 'es2015',
      chunkSizeWarningLimit: 1024,
    },
  }
})
