{
  "extends": "@tsconfig/node20/tsconfig.json",
  "include": [
    "src/**/*.ts",
    "vite.config.*",
    "vitest.config.*",
    "cypress.config.*",
    "nightwatch.conf.*",
    "playwright.config.*",
    "src/**/*.vue",

    "src/**/*.d.ts",
    "build/**/*.ts",
    "build/**/*.d.ts",
    "vite.config.ts",
  ],
  "compilerOptions": {
    "composite": true,
    "noEmit": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo",

    "module": "ESNext",
    "moduleResolution": "Bundler",
    // "types": ["node"]
    "types": ["vite/client"],
    "lib": ["DOM"],
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
