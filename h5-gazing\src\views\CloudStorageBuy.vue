<template>
  <div class="container">
    <img class="top-imgs" src="https://cache.gdxp.com/acce/assets/meal/bj.png" alt="" />
    <div class="top-box" v-if="config.platform == 'android'">
      <img
        class="close"
        @click="backApp"
        src="https://cache.gdxp.com/acce/assets/meal/close.png"
        alt=""
      />
    </div>
    <div v-else class="ios-top-box">
      <img
        class="close"
        @click="backApp"
        src="https://cache.gdxp.com/acce/assets/meal/close.png"
        alt=""
      />
      <div class="top-right" @click="click_resume_purchase">
        <div>Restore</div>
        <div>Purchases</div>
      </div>
    </div>
    <div
      class="goods"
      @click="subscribe(item)"
      v-for="(item, index) in state.goodsList"
      :class="
        index === 0 ? 'goods-one' : index === 1 ? 'goods-two' : index === 2 ? 'goods-vip' : ''
      "
    >
      <!-- 发版 -->
      <div class="goods-left" v-if="config.platform === 'ios'">
        <div class="goods-left-text">{{ index === 1 ? "One" : "6-month" }}</div>
        <div class="goods-left-text">{{ index === 1 ? "Year" : "" }}</div>
        <div class="goods-left-text">Plan</div>
      </div>
      <div class="goods-left" v-else-if="config.lang === 'en'">
        <div class="goods-left-text">Cloud</div>
        <div class="goods-left-text">Storage</div>
        <div class="goods-left-text">{{ index === 2 ? "Premium" : "Basic" }}</div>
      </div>
      <div class="goods-left" v-else>
        <div class="goods-left-text new-goods-left-text">{{ index === 2 ? "VIP" : "基础" }}</div>
        <div class="goods-left-text new-goods-left-text">套餐</div>
      </div>
      <div class="goods-right">
        <!-- 发版 -->
        <div class="goods-year" v-if="config.platform === 'ios'">
          {{ item.currency }}
          {{ index === 1 ? "29.99" : "28.99" }}
        </div>
        <div class="goods-year" v-else>
          {{ item.currency }}
          {{ (Number(item.price) / item.service_days).toFixed(3) }}
          <span class="goods-span">/days</span>
        </div>
        <div class="goods-box">
          <div class="goods-day-title">
            <span class="day-dian"></span>
            Service time {{ item.service_days }} days
          </div>
          <div class="goods-day-title">
            <span class="day-dian"></span>
            History records {{ item.cycle_days }} days
          </div>
          <div class="goods-day-title" v-if="index === state.goodsList.length - 1">
            <span class="day-dian"></span>
            30-day video history for up to 10 devices.
          </div>
        </div>
        <div class="footer-btn">Subscribe</div>
      </div>
    </div>
    <!-- 发版 -->
    <div class="footer-user" v-if="config.platform == 'ios'">
      <div class="user-policy" @click="privacy(1)">Private Policy</div>
      <div class="user-policy" @click="privacy(2)">Terms of Use</div>
    </div>
    <div class="footer-user new-footer-user" v-if="config.platform == 'ios'">
      <div class="user-policy" @click="privacy(3)">Subscription Service</div>
    </div>
    <div class="footer-text">
      {{
        config.platform == "android"
          ? "Subscription can be canceled at any time in Google Play"
          : "Subscription can be canceled at any time in the App Store."
      }}
    </div>
    <div class="footer-link">You need to subscribe to cloud storage first to view videos.</div>
  </div>
</template>

<script setup lang="ts">
import config from "@/config"
import { onMounted, reactive, ref } from "vue"
import { getExpand, saveIosLog, saveAndLog } from "@/api"
import type { exportData } from "@/api/base"
import { subscribe } from "@/utils"
import { showToast, closeToast } from "vant"
import { useHomeStore } from "@/stores/home"
import { useRouter } from "vue-router"

const homeStore = useHomeStore()
const router = useRouter()
const state = reactive({
  goodsList: [] as exportData[],
})

if (config.platform === "ios") {
  state.goodsList = [
    {
      currency: "$",
      cycle_days: 30,
      trial_days: 30,
      service_days: 180,
      product_id: "sub_cloud_storage_cm_30_6m_1",
      price: 183.2,
      group: 1,
    },
    {
      currency: "$",
      cycle_days: 30,
      trial_days: 30,
      service_days: 365,
      product_id: "sub_cloud_storage_cm_30_1y_1",
      price: 183.2,
      group: 1,
    },
  ]
} else {
  state.goodsList = [
    {
      currency: "$",
      cycle_days: 30,
      trial_days: 30,
      service_days: 180,
      product_id: "sub_cloud_storage_cm_30_6m_1",
      price: 183.2,
      group: 1,
    },
    {
      currency: "$",
      cycle_days: 30,
      trial_days: 30,
      service_days: 365,
      product_id: "sub_cloud_storage_cm_30_1y_1",
      price: 183.2,
      group: 1,
    },
    {
      currency: "$",
      cycle_days: 30,
      trial_days: 0,
      service_days: 365,
      product_id: "t_sub_cloud_storage_all_fa_30_1y_1",
      price: 99,
      group: 1,
    },
  ]
}

const windowHeight = ref(window.innerHeight)

const initHeight = () => {
  const elements = document.querySelectorAll(".goods")
  elements.forEach((el: any) => {
    if (config.platform === "ios") {
      el.style.height = `${(windowHeight.value - 180) / 2.3}px`
    } else {
      el.style.height = `${(windowHeight.value - 180) / 3}px`
    }
  })
}

const backApp = () => {
  router.go(-1)
}

const privacy = (type: number) => {
  if (type === 1) {
    window.webkit.messageHandlers.click_privacy.postMessage("点击了")
  } else if (type === 2) {
    window.webkit.messageHandlers.click_user_agreement.postMessage("点击了")
  } else if (type === 3) {
    window.webkit.messageHandlers.click_subscription_service.postMessage("点击了")
  }
}

// 通知APP恢复购买
const click_resume_purchase = () => {
  try {
    window.webkit.messageHandlers.click_resume_purchase.postMessage("点击了恢复购买")
  } catch {
    console.log("不在内嵌手机端,通知APP打开相册")
  }
}

// 获取设备列表套餐
const fetch = () => {
  getExpand().then((res: any) => {
    initHeight()
    if (res.data && res.data.basic.length) {
      if (config.sub_id.value.length) {
        const filteredArray1 = res.data.basic.filter((item1: exportData) => {
          return !config.sub_id.value.find((item2: string) => item1.product_id === item2)
        })
        if (filteredArray1.length) {
          dataList(filteredArray1, res.data.plus)
        } else {
          dataList(res.data.basic, res.data.plus)
        }
      } else {
        dataList(res.data.basic, res.data.plus)
      }
    }
  })
}

// 过滤套餐
function dataList(filteredArray1: Array<exportData>, arr: Array<exportData>) {
  const result: any = Object.values(
    filteredArray1.reduce((acc: any, obj: any) => {
      const key = obj["group"].toString()
      if (!acc[key]) {
        acc[key] = []
      }
      acc[key].push(obj)
      return acc
    }, {})
  )
  if (result.length == 0) return
  state.goodsList = []
  let str = ""
  console.log(result)
  if (config.platform == "android") {
    result.forEach((item: any) => {
      state.goodsList.push(getRandomElement(item))
    })
    if (arr.length) {
      state.goodsList.push(arr[0])
    }
  } else {
    state.goodsList = getRandomElement(result)
    // if (arr.length) {
    //   state.goodsList.push(arr[0])
    // }
  }
  if (config.platform == "android") {
    state.goodsList.forEach(item => {
      str += `${item.product_id}&`
    })
    str = str.replace(/&([^&]*)$/, "")
  }
  if (config.platform == "android") {
    window.android.send_data(str)
  }
}

// 处理安卓随机数据中的id
function getRandomElement(arr: any) {
  if (arr.length === 0) return null
  const randomIndex = Math.floor(Math.random() * arr.length)
  return arr[randomIndex]
}

// 订阅成功回调
const subscribeSuccessfully = (
  receipt_data: string,
  product_id: string,
  order_sn: string,
  is_sandbox: string,
  password: string,
  currencyCode: string,
  productPrice: string
) => {
  closeToast()
  saveIosLog({
    product_key: config.product_key,
    device_sn: config.device_sn,
    src: 4,
    receipt_data,
    product_id,
    order_sn,
    is_sandbox: Number(is_sandbox),
    password,
    fee_type: currencyCode,
    fee_total: Number(productPrice),
  }).then((res: any) => {
    if (res.code === 0) {
      homeStore.getDeviceList(1)
      showToast("Subscribe successful")
      setTimeout(() => {
        router.push("/home")
      }, 2000)
    }
  })
}

// 订阅成功回调
const subscribeAndSuccessfully = (
  purchase_token: string,
  package_name: string,
  subscription_id: string,
  order_sn: string,
  fee_type: string,
  fee_total: string
) => {
  closeToast()
  saveAndLog({
    purchase_token: purchase_token,
    device_sn: config.device_sn,
    package_name,
    order_sn,
    src: 4,
    subscription_id,
    fee_type: fee_type,
    fee_total: Number(fee_total),
  }).then((res: any) => {
    if (res.code === 0) {
      homeStore.getDeviceList(1)
      showToast("Subscribe successful")
      setTimeout(() => {
        router.push("/home")
      }, 2000)
    }
  })
}

// 订阅失败回调
const subscribeError = (error: string) => {
  closeToast()
  switch (error) {
    case "1":
      showTestTip("Failed purchase")
      break
    case "2":
      showTestTip("Cancel purchase")
      break
    case "3":
      showTestTip("Verification failed")
      break
    case "4":
      showTestTip("Verification Successful")
      break
    case "5":
      showTestTip(
        "This iOS device does not support in-app purchases or needs to open in-app purchase permissions"
      )
      break
    case "6":
      showTestTip("Under review and processing")
      break

    default:
      break
  }
}

const showTestTip = (str: string) => {
  showToast({
    message: str,
    duration: 3500,
  })
}

onMounted(() => {
  fetch()
  window.subscribeSuccessfully = subscribeSuccessfully
  window.subscribeAndSuccessfully = subscribeAndSuccessfully
  window.subscribeError = subscribeError
})
</script>

<style lang="less" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(170.02deg, #161616 1.7%, #06322f 99.1%);
  padding: 0 16px 0 16px;
  position: relative;
  .top-imgs {
    position: absolute;
    top: 0px;
    right: 0px;
    width: 270px;
    height: 300px;
  }
  .top-box {
    height: 60px;
    display: flex;
    align-items: center;
  }
  .close {
    width: 18px;
    height: 18px;
  }
  .ios-top-box {
    height: 40px;
    padding-top: constant(safe-area-inset-top); /* iOS < 11.2 */
    padding-top: env(safe-area-inset-top); /* iOS >= 11.2 */
    display: flex;
    align-items: center;
    justify-content: space-between;
    .top-right {
      font-size: 14px;
      color: #fff;
      position: relative;
      z-index: 999;
    }
  }
  .goods {
    background-position: center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 12px;
    position: relative;
    margin-bottom: 20px;
    display: flex;
    min-height: 150px;
    .goods-left {
      border-radius: 12px;
      width: 25%;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .goods-left-text {
        font-size: 14px;
        color: #fff;
        margin-bottom: 5%;
        padding-left: 15%;
        font-weight: bold;
      }
      .new-goods-left-text {
        text-align: center;
        padding-left: 0;
      }
      .bottom-yuan {
        position: absolute;
        right: -11px;
        bottom: -8px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #f5f6f8;
        z-index: 9;
      }
    }
    .goods-right {
      flex: 1;
      padding: 15px 0px 15px 5px;
      position: relative;
    }
    .goods-year {
      font-size: 28px;
      color: #072e6a;
      margin-bottom: 5px;
      .goods-span {
        font-size: 16px;
        color: #072e6a;
      }
    }
    .goods-box {
      margin-bottom: 5%;
      .goods-day-title {
        font-size: 12px;
        color: #072e6a;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
        .day-dian {
          display: inline-block;
          width: 6px;
          height: 6px;
          background-color: #072e6a;
          border-radius: 50%;
          margin-right: 3px;
          opacity: 0.5;
        }
        .goods-day-text {
          color: #28b0ba;
        }
        .one-text {
          color: #000;
        }
      }
    }
    .footer-btn {
      position: absolute;
      width: 90%;
      left: 50%;
      bottom: 15px;
      transform: translate(-50%);
      min-height: 42px;
      background: linear-gradient(180deg, #00ff57 0%, #25c20b 100%);
      font-size: 20px;
      font-weight: bold;
      color: #051938;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      border-radius: 26px;
      word-break: break-all;
    }
  }
}

.goods-one {
  background-image: url("../assets/one.png") !important;
}
.goods-two {
  background-image: url("../assets/two.png") !important;
  .goods-day-title {
    color: #3a084e;
  }
}
.goods-vip {
  background-image: url("../assets/tre.png") !important;
  margin-bottom: 10px !important;
  .goods-day-title {
    color: #6a3c07;
  }
}

@media screen and (min-height: 737px) {
  .goods-box {
    .goods-day-title {
      margin-bottom: 3% !important;
    }
  }
  .goods-title {
    margin-bottom: 5% !important;
  }
  .goods-year {
    margin-bottom: 5% !important;
  }
}

.footer-text {
  font-size: 12px;
  color: #fff;
  text-align: center;
  margin-bottom: 2px;
}
.footer-link {
  font-size: 12px;
  color: #fff;
  text-align: center;
}

.footer-user {
  display: flex;
  justify-content: space-around;
  font-size: 12px;
  color: #0fe537;
  padding-bottom: 5px;
  padding-top: 12px;
  .user-policy {
    border: 1px solid #0fe537;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    padding: 0 5px;
  }
}
.new-footer-user {
  padding: 0;
  margin-bottom: 10px;
}
</style>
