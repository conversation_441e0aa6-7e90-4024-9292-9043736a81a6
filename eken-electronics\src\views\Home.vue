<template>
  <header>
    <div class="banner-media">
      <div class="left" :class="{ fadeIn: leftVisible }">
        <div class="title-container">
          <div class="text-color">Aiwit App</div>
          <div class="subtitle">Simple, Smart Home Security Anywhere.</div>
        </div>
        <p class="desc-text">
          Stay connected to your home with the Aiwit app. Get instant motion alerts, watch live in
          crystal-clear HD, and talk to visitors with Two-Way Talk. Effortlessly manage your video
          doorbells and security cameras—all from one intuitive app. Upgrade to an Aiwit Plan to
          review, save, and share recordings, giving you complete peace of mind, wherever you are.
        </p>
      </div>
      <div class="right" :class="{ fadeIn: rightVisible }">
        <video
          controls
          autoplay
          muted
          loop
          preload="auto"
          disablePictureInPicture
          controlslist="nodownload noremoteplayback"
          src="../assets/video/Video.mp4"
        ></video>
      </div>
    </div>
    <div class="wrapper-container" ref="wrapperRef" :class="{ fadeIn: wrapperVisible }">
      <div class="text-center">Our Valued Partners</div>
      <div class="wrapper">
        <div class="marquee">
          <div class="marquee__group" :class="{ paused: isPaused }" v-for="item in 3">
            <div class="item" v-for="(imgItem, index) in imgList">
              <img
                @mouseover="pauseAll(index)"
                @mouseleave="resumeAll(index)"
                :src="imgItem"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <div class="feature-swiper-container">
    <h1 class="title">
      Unlock Premium Features With
      <span class="colorful-text">Aiwit Secure.</span>
    </h1>
    <div class="swiper-container" ref="swiperRef" :class="{ fadeIn: swiperVisible }">
    <swiper v-if="!isMobile()" class="swiper-class" :slides-per-view="'auto'" :space-between="60">
      <swiper-slide class="swiper-slide-class" v-for="item in swiperData">
        <div class="text-box">
          <h1 class="title">{{ item.title }}</h1>
          <p class="subtitle">
            {{ item.subtitle }}
          </p>
        </div>
        <div class="img-container">
          <img class="bg-img" :src="item.imgUrl" alt="" />
        </div>
      </swiper-slide>
    </swiper>
    <swiper
      v-if="isMobile()"
      class="swiper-class"
      :modules="modules"
      :slides-per-view="'auto'"
      :space-between="10"
    >
      <swiper-slide class="swiper-slide-class" v-for="item in swiperData">
        <div class="text-box">
          <h1 class="title">{{ item.title }}</h1>
          <p class="subtitle">
            {{ item.subtitle }}
          </p>
        </div>
        <div class="img-container">
          <img class="bg-img" :src="item.imgUrl" alt="" />
        </div>
      </swiper-slide>
    </swiper>
  </div>
  </div>
  <div class="box-data" ref="boxDataRef" :class="{ fadeIn: boxDataVisible }">
    <div class="box-left">
      <div class="box-title">Optional AES 128-bit Encrypted</div>
      <div class="box-title">
        <span class="colorful-text">Cloud Storage Plans</span>
      </div>
      <p class="box-text">
        <!-- Keep every moment at your door securely stored and ready to replay. With our optional
        subscription,you'll enjoy AES 128-bit encryption for all your recorded videos, plus up to 30
        Days of Video Event History—so you never miss a thing. This plan requires an annual fee and
        is available for purchase separately through the app. -->
        Keep every moment at your door securely stored and ready to replay.
      </p>
    </div>
    <div class="web-box" v-if="!isMobile()">
      <img class="web-left-img" src="../assets/index/app-left.png" alt="" />
      <img class="web-right-img" src="../assets/index/app-right.png" alt="" />
    </div>
    <div class="app-box" v-if="isMobile()">
      <img class="app-left-img" src="../assets/index/app-1.png" alt="" />
      <img class="app-left-img" src="../assets/index/app-2.png" alt="" />
      <img class="app-right-img" src="../assets/index/app-right.png" alt="" />
    </div>
  </div>
  <div class="section-box" ref="sectionBoxRef" :class="{ fadeIn: sectionBoxVisible }">
    <img
    class="section-data"
    src="../assets/index/data.png"
    alt=""
    />
  </div>
  <div class="section-container" ref="sectionContainerRef" :class="{ fadeIn: sectionContainerVisible }">
    <div>
      <div class="imgbg-title new-title">See Your Child's Every Move In Real Time</div>
    </div>
  </div>
  <div class="phone-box" ref="phoneBoxRef" :class="{ fadeIn: phoneBoxVisible }">
    <div class="phone-item">
      <img class="phone-img" src="../assets/index/1.png" alt="" />
      <div class="phone-text">
        <img class="phone-icon" src="../assets/index/4.png" alt="" />
        Human Detection
      </div>
    </div>
    <div class="phone-item">
      <img class="phone-img" src="../assets/index/2.png" alt="" />
      <div class="phone-text">
        <img class="phone-icon"  src="../assets/index/5.png" alt="" />
        Push Message
      </div>
    </div>
    <div class="phone-item">
      <img class="phone-img" src="../assets/index/3.png" alt="" />
      <div class="phone-text">
        <img class="phone-icon"  src="../assets/index/6.png" alt="" />
        Real Time Monitoring
      </div>
    </div>
  </div>

  <footer class="footer" ref="footerRef" :class="{ fadeIn: footerVisible }">
    <div class="col-container">
      <div class="col-1">
        <h1 class="title">About Us</h1>
        <p class="desc">
          EKEN Electronics is a technology-driven software company specializing in software
          development with a large and skilled R&D team. Headquartered in Hong Kong, the company
          provides app platforms for smart doorbells and battery-powered security cameras, offering
          users a secure and comfortable home protection experience. Its main app, Aiwit, supports
          global users, with the company's business spanning worldwide.
        </p>
      </div>
      <div class="col-1 col-2">
        <h1 class="title">Contact Us</h1>
        <div class="email-call">
          <a
            class="item"
            href="mailto:<EMAIL>"
            rel="noopener noreferrer"
            target="_blank"
          >
            <EMAIL>
          </a>
        </div>
      </div>
    </div>
    <p class="copyright">© 2024 Eken Electronics. All rights reserved.</p>
  </footer>
</template>
<script setup lang="ts">
import logo1 from "@/assets/index/logo1.png"
import logo2 from "@/assets/index/logo2.png"
import logo3 from "@/assets/index/logo3.png"
import logo4 from "@/assets/index/logo4.png"
import logo5 from "@/assets/index/logo5.png"
import logo6 from "@/assets/index/logo6.png"

import swiper1 from "@/assets/index/swiper1.png"
import swiper2 from "@/assets/index/swiper2.png"
import swiper3 from "@/assets/index/swiper3.png"
import { ref, onMounted, type Ref } from "vue"
import { Navigation } from "swiper/modules"
import { Swiper, SwiperSlide } from "swiper/vue"

import { isMobile } from "@/utils"

import "swiper/less"
import "swiper/css/navigation"

const modules = [Navigation]

const imgList = [logo1, logo2, logo3, logo4, logo5, logo6]

const isPaused = ref(false)

const pauseAll = (index: number) => {
  isPaused.value = true
}

const resumeAll = (index: number) => {
  isPaused.value = false
}

const swiperData = [
  {
    title: "Aiwit Intelligence ",
    subtitle: "Person, Vehicle, and Custom Detection, powered by AI.",
    imgUrl: swiper1,
  },
  {
    title: "Video & Event History",
    subtitle: "View, share, and save 30 days of video and event recordings from anywhere.",
    imgUrl: swiper2,
  },
  {
    title: "Person & Vehicle Recognition",
    subtitle: "Personalise your security to recognise familiar people and vehicles.",
    imgUrl: swiper3,
  },
]

const leftVisible = ref(false);
const rightVisible = ref(false);

const wrapperVisible = ref(false);
const swiperVisible = ref(false);
const boxDataVisible = ref(false);
const sectionBoxVisible = ref(false);
const sectionContainerVisible = ref(false);
const phoneBoxVisible = ref(false);
const footerVisible = ref(false);

const wrapperRef = ref(null);
const swiperRef = ref(null);
const boxDataRef = ref(null);
const sectionBoxRef = ref(null);
const sectionContainerRef = ref(null);
const phoneBoxRef = ref(null);
const footerRef = ref(null);

const observeElement = (elementRef: Ref<HTMLElement | null>, visibilityRef: Ref<boolean>) => {
  const observer = new IntersectionObserver(
    ([entry]) => {
      if (entry.isIntersecting) {
        visibilityRef.value = true;
        observer.disconnect(); // 只触发一次
      }
    },
    { threshold: 0.1 }
  );
  if (elementRef.value) {
    observer.observe(elementRef.value);
  }
};

onMounted(() => {
  // 确保banner动画按顺序触发
  setTimeout(() => {
    leftVisible.value = true; // 左侧文字淡入
    setTimeout(() => {
      rightVisible.value = true; // 右侧视频淡入
    }, 1000); // 延迟500ms显示右侧视频
  }, 500); // 延迟500ms显示左侧文字

  //banner以下内容跟随鼠标滚动淡出
  observeElement(wrapperRef, wrapperVisible);
  observeElement(swiperRef, swiperVisible);
  observeElement(boxDataRef, boxDataVisible);
  observeElement(sectionBoxRef, sectionBoxVisible);
  observeElement(sectionContainerRef, sectionContainerVisible);
  observeElement(phoneBoxRef, phoneBoxVisible);
  observeElement(footerRef, footerVisible);
});
</script>

<style lang="less" scoped>
@color2f3541: #2f3541;
@width1800: 80%;
header {
  overflow: hidden;
  color: #2f3541;
  padding: 100px 0;
  background: linear-gradient(60deg, #d2f0ff, #affcff, #d0edfc, #f2d8ff, #f4deff, #ffd2b4, #ffbb8f);
  background-size: 200% 200%;
  animation: gradientAnimation 9s ease-in-out infinite;
  width: 100%;
  .banner-media {
    width: @width1800;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    gap: 50px;
    padding: 260px 0;
    position: relative;
    .left {
      width: 50%;
      flex-direction: column;
      .title-container {
        margin-bottom: 30px;
        .text-color {
          display: inline-block;
          line-height: 1.05em;
          letter-spacing: -0.7px;
          margin-top: -6px;
          font-size: 80px;
          font-weight: 600;
          -webkit-background-clip: text;
          color: transparent;
          background-image: linear-gradient(97deg, #0096ff, #bb64ff 42%, #f2416b 74%, #eb7500);
        }
        .subtitle {
          color: #2F3541;
          font-size: 38px;
          line-height: 1.05em;
          letter-spacing: -0.7px;
          padding: 30px 0 10px;
        }
      }
      .desc-text {
        font-size: 21px;
        line-height: 30px;
        font-weight: 400;
        color: #2F3541;
      }
    }
    .right {
      position: absolute;
      right: 0;
      top: 200px;
      width: 40%;
      border-radius: 20px;
      text-align: center;
      
      video {
        width: 100%;
        height: 100%;
        border-radius: 20px;
        object-fit: cover;
      }
    }
  }
  .text-center {
    color: #2F3541;
    text-align: center;
    padding-top: 30px;
    margin-bottom: 60px;
    font-size: 26px;
    font-weight: 600;
  }

  .marquee-container {
    display: flex;
    margin-top: 40px;
    &:hover {
      animation-play-state: paused;
    }
    &:not(:hover) {
      animation-play-state: running;
    }

    .marquee-item {
      padding: 0 13.5px;
      margin: 0 10px;
      height: 48px;
      flex-shrink: 0;

      .logo {
        width: 100%;
        height: 100%;
        object-fit: contain;
        filter: brightness(0);
        &:hover {
          filter: brightness(1);
        }
      }
    }
  }

  .wrapper {
    --logo-width: 200px;
    --logo-height: 80px;
    --gap: calc(var(--logo-width) / 6);
    --duration: 60s;
    --scroll-start: 0;
    --scroll-end: calc(-100% - var(--gap));

    display: flex;
    flex-direction: column;
    gap: var(--gap);
    margin: 0 auto;
    max-width: 80%;
  }

  .marquee {
    display: flex;
    overflow: hidden;
    user-select: none;
    gap: var(--gap);
    mask-image: linear-gradient(
      to right,
      hsl(0 0% 0% / 0),
      hsl(0 0% 0% / 1) 20%,
      hsl(0 0% 0% / 1) 80%,
      hsl(0 0% 0% / 0)
    );
  }

  .marquee__group {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    gap: var(--gap);
    min-width: 100%;
    animation: scrollLeft var(--duration) linear infinite;
  }

  .paused {
    animation-play-state: paused;
  }

  .wrapper .marquee:nth-child(even) {
    margin-left: calc(var(--logo-width) / -2);
  }

  .item {
    img {
      width: var(--logo-width);
      height: var(--logo-height);
      object-fit: contain;
      filter: brightness(0);
      cursor: pointer;
      &:hover {
        filter: brightness(1);
      }
    }
  }

  @keyframes scrollLeft {
    from {
      transform: translateX(var(--scroll-start));
    }
    to {
      transform: translateX(var(--scroll-end));
    }
  }
}
.section-box{
  padding-top: 50px;
  width: 80%;
  margin: 0 auto;
  text-align: center;
  padding-bottom: 100px;
}
.section-data{
  margin: 0 auto;
  height: 1218px;
}
.imgbg-title {
  color: #2F3541;
  font-size: 60px;
  font-weight: 600;
  text-align: center;
  padding-top: 20px;
  margin-bottom: 10px;
  line-height: 1;
}
.new-title {
  padding-top: 60px;
}
.imgbg-subtitle {
  font-size: 21px;
  line-height: 30px;
  font-weight: 400;
  color: #2F3541;
}
.section-bg {
  display: block;
  width: 100%;
  object-fit: cover;
}

.section-container {
  width: 80%;
  margin: 0 auto;
  background-color: #fff;
  .imgbg-title {
    text-align: left;
    padding-bottom: 10px;
    margin-bottom: 40px;
  }
  .colorful-text {
    font: inherit;
    -webkit-background-clip: text;
    color: transparent;
    background-image: linear-gradient(97deg, #0096ff, #bb64ff 42%, #f2416b 74%, #eb7500);
  }
}
.feature-swiper-container {
  position: relative;
  background: url(../assets/index/pinkbg.jpg) no-repeat 100% / cover;
  padding: 195px 0 160px;
  > .title {
    font-size: 60px;
    width: 80%;
    margin: 0 auto;
    font-weight: bold;
    color: #2F3541;
    margin-bottom: 50px;
    .colorful-text {
      font: inherit;
      -webkit-background-clip: text;
      color: transparent;
      background-image: linear-gradient(97deg, #0096ff, #bb64ff 42%, #f2416b 74%, #eb7500);
    }
  }
  .swiper-class {
    width: 80%;
    margin: 0 auto;
    padding-bottom: 100px;
    .swiper-slide-class {
      overflow: hidden;
      background-color: #fafafa;
      border-radius: 15px;
      width: 450px !important;
      height: 720px !important;
      .text-box {
        padding: 32px 32px 0 32px;
        height: 146px;
        .title {
          font-size: 21px;
          color: #1d72ba;
          font-weight: 600;
          margin: 0;
          margin-bottom: 10px;
        }
        .subtitle {
          font-size: 18px;
          color: #475467;
        }
      }
      .img-container {
        width: 100%;
        .bg-img {
          width: 100%;
          display: block;
          object-fit: cover;
        }
      }
    }
  }
}

:deep(.swiper-button-prev) {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  top: unset;
  left: -90px;
  right: 0;
  bottom: 0px;
  margin: auto;
  color: rgba(0, 0, 0, 0);
  background-image: url(../assets/index/arrow1.png);
  background-size: 100%;
  transform: rotate(180deg);
  transition: all 0.2s;
  &:hover {
    background-image: url(../assets/index/arrow2.png);
  }
}
:deep(.swiper-button-next) {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: absolute;
  top: unset;
  left: 90px;
  right: 0;
  bottom: 0px;
  margin: auto;
  color: rgba(0, 0, 0, 0);
  background-image: url(../assets/index/arrow1.png);
  background-size: 100%;
  transition: all 0.2s;
  &:hover {
    background-image: url(../assets/index/arrow2.png);
  }
}

@media screen and (max-width: 1390px) {
  .box-data {
    padding: 0 20px;
    .box-left {
      .box-title {
        color: #2F3541;
        font-size: 60px;
        font-weight: 600;
        padding-top: 20px;
        margin-bottom: 10px;
        line-height: 1.2;
        .colorful-text {
          font: inherit;
          -webkit-background-clip: text;
          color: transparent;
          background-image: linear-gradient(97deg, #0096ff, #bb64ff 42%, #f2416b 74%, #eb7500);
        }
      }
      .box-text {
        width: 32rem;
        padding-top: 10px;
        font-size: 20px;
        line-height: 30px;
        font-weight: 400;
        color: #2F3541;
      }
    }
  }
}

@media (min-width: 100px) and (max-width: 600px) {
  .box-data {
    padding: 45px 20px;
    .box-left {
      width: 90%;
      padding-left: 20px;
      > .box-title {
        font-size: 38px;
      }
      .box-text {
        width: 100%;
        padding-top: 10px;
        font-size: 20px;
        line-height: 30px;
        font-weight: 400;
        color: #2F3541;
      }
    }
  }
  .section-container {
    .imgbg-title {
      font-size: 38px;
      line-height: 1.2;
      font-weight: bold;
      color: #2F3541;
    }
  }
  .feature-swiper-container {
    padding: 45px 0 20px;
    > .title {
      font-size: 38px;
      line-height: 1.2;
      font-weight: bold;
      color: #2F3541;
    }
    .swiper-class {
      padding-bottom: 50px;
      .swiper-slide-class {
        width: 100% !important;
        height: 615px !important;
        .text-box {
          padding: 32px 32px 0 32px;
          height: auto;
          .title {
            font-size: 21px;
            color: #1d72ba;
            font-weight: 600;
            margin: 0;
            margin-bottom: 10px;
          }
          .subtitle {
            font-size: 18px;
            color: #475467;
          }
        }
      }
    }
  }
  .banner-media {
    padding: 130px 0 !important;
    .left {
      width: 100% !important;
      .title-container .text-color {
        font-size: 65px !important;
      }
    }
    .right {
      position: relative !important;
      top: 0 !important;
      right: 0 !important;
      flex-shrink: 1 !important;
      width: 100% !important;
      border-radius: 20px;
      overflow: hidden;
      
      video {
        width: 100%;
        height: 100%;
        border-radius: 20px;
        object-fit: cover;
      }
    }
  }
  .phone-box{
    display: block !important;
  }
  .section-box{
    width: 100%!important;
    padding-top: 0!important;
    padding-bottom: 0!important;
  }
  .section-data{
    width: 100%!important;
    height: 520px!important;
  }
  .phone-img{
    width: 100%!important;
    height: 432px!important;
  }
}

.web-box {
  padding-top: 100px;
  padding-bottom: 80px;
  display: flex;
  width: 80%;
  margin: 0 auto;
  .web-left-img {
    margin-top: 280px;
    max-width: 955px;
    max-height: 553px;
  }
  .web-right-img {
    margin-left: 80px;
    max-width: 473px;
    max-height: 881px;
  }
}

.app-box {
  padding-top: 100px;
  width: 80%;
  margin: 0 auto;
  text-align: center;
  .app-left-img {
    max-width: 100%;
    height: auto;
  }
  .app-right-img {
    max-width: 100%;
    height: auto;
  }
}

@media (min-width: 600px) and (max-width: 1389px) {
  .box-data {
    padding: 0 20px;
    .box-left {
      text-align: center;
      margin-bottom: 10px;
      .box-text {
        margin: 0 auto;
        text-align: left;
        // width: 32rem;
        padding-top: 10px;
        font-size: 20px;
        line-height: 30px;
        font-weight: 400;
        color: #504a53;
      }
    }
  }
  .banner-media {
    padding: 130px 0 !important;
    .left {
      width: 48% !important;
    }
    .right {
      position: absolute !important;
      top: 130px !important;
      right: 0 !important;
      width: 50% !important;
      border-radius: 20px;
      overflow: hidden;
      
      video {
        width: 100%;
        height: 100%;
        border-radius: 20px;
        object-fit: cover;
      }
    }
  }
}

@media (min-width: 1390px) {
  .box-data {
    position: relative;
    .box-left {
      position: absolute;
      left: 10%;
      top: 8%;
      .box-title {
        color: #2F3541;
        font-size: 60px;
        font-weight: 600;
        margin-top: 20px;
        margin-bottom: 10px;
        line-height: 1;
        .colorful-text {
          font: inherit;
          -webkit-background-clip: text;
          color: transparent;
          background-image: linear-gradient(97deg, #0096ff, #bb64ff 42%, #f2416b 74%, #eb7500);
        }
      }
      .box-text {
        // width: 504px;
        padding-top: 10px;
        font-size: 21px;
        line-height: 30px;
        font-weight: 400;
        color: #504a53;
      }
    }
  }
}

.section-1 {
  padding: 100px 0;
  background-image: url(../assets/index/homepage-solutions-background-desktop.jpg);
  background-color: transparent;
  color: rgb(47, 53, 65);
  .title {
    font-size: 16px;
    line-height: 1.1em;
    text-transform: uppercase;
    color: #5a657b;
  }
  .text-1 {
    display: block;
    font-size: 52px;
    margin: 30px 0;
    font-weight: bold;
    unicode-bidi: isolate;
    color: @color2f3541;
  }
  .text-2 {
    font-size: 20px;
    font-weight: 400;
    color: @color2f3541;
  }
  .tab-list-container {
    width: @width1800;
    margin: 0 auto;
    display: flex;
    gap: 30px;
    padding-top: 40px;
    flex-wrap: wrap;
    .tab-list-item {
      width: 364px;
      height: 378px;
      padding: 45px;
      border-left: 5px solid;
      background-color: #fff;
      border-radius: 0 9pt 9pt 0;
      border-image: linear-gradient(#7739d9, #e91c24) 1;
      transition: border-image-source 0.3s ease, box-shadow 0.3s ease, -webkit-box-shadow 0.3s ease;
      &:hover {
        -o-border-image: linear-gradient(#7739d9, #7739d9) 1;
        border-image: -webkit-gradient(linear, left top, left bottom, from(#7739d9), to(#7739d9)) 1;
        border-image: linear-gradient(#7739d9, #7739d9) 1;
        -webkit-box-shadow: 0 2px 20px 8px rgba(0, 0, 0, 0.2);
        box-shadow: 0 2px 20px 8px rgba(0, 0, 0, 0.2);
      }

      .title {
        font-size: 22.5px;
        color: @color2f3541;
      }
      .desc {
        font-size: 18px;
        color: @color2f3541;
        padding: 10px 0 20px;
      }
      .button {
        font-size: 18px;
        color: #e91c24;
      }
    }
  }
}

.text-container {
  width: @width1800;
  margin: 0 auto;
}

.section-2 {
  padding: 100px 0;
  background-image: url(../assets/index/homepage-data-center-background-desktop.jpg);
  background-color: transparent;
  color: rgb(47, 53, 65);
  .title {
    font-size: 22.5px;
    color: #edccff;
  }
  .text-1 {
    font-size: 52px;
    color: #fff;
    line-height: 52px;
    margin: 20px 0;
  }
  .card-data {
    width: @width1800;
    padding-bottom: 50px;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    .card-item {
      padding: 40px;
      border: 1px solid #fff;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .data {
        font-size: 52px;
        color: #fff;
      }
      .data-desc {
        font-size: 14px;
        color: #aaa4b7;
      }
    }
  }
  .bottom-card {
    padding: 72px 45px;
    text-align: center;
    background: url(../assets/index/homepage-sustainability-background-desktop.jpg) no-repeat 100% /
      cover;
    border-radius: 20px;

    width: @width1800;
    margin: 0 auto;
    .title {
      font-size: 52px;
      padding-bottom: 27px;
    }
    .desc {
      font-size: 18px;
      color: #2f3541;
    }
  }
}

.section-3 {
  padding: 100px 0;
  box-sizing: border-box;

  .section-3-title {
    width: @width1800;
    margin: 20px auto;
    color: #2f3541;
    font-size: 52px;
  }
  .card-container {
    width: @width1800;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 80px;
    .card-item {
      .picture {
        height: 256px;
        background-color: #5a657b;
        border-radius: 10px;
      }
      .title {
        font-size: 23px;
        color: #2f3541;
      }
      .desc {
        font-size: 18px;
        color: #2f3541;
      }
    }
  }
}
.phone-box {
  width: 80%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  margin-bottom: 80px;
  .phone-item {
    .phone-img {
      height: 522px;
      margin-bottom: 20px;
    }
    .phone-icon{
      width: 36px;
      height: 36px;
      margin-right: 10px;
    }
    .phone-text{
      font-size: 20px;
      color:#504A53;
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }
  }
}

.footer {
  color: #fff;
  background: rgb(47, 53, 65);
  .col-container {
    width: 80%;
    padding-top: 72px;
    padding-bottom: 112px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    .col-1 {
      width: 70%;
      .title {
        font-size: 40px;
      }
      .desc {
        font-size: 18px;
      }

      .email-call {
        display: flex;
        align-items: center;
        gap: 100px;
        .item {
          font-size: 18px;
          cursor: pointer;
          color: #fff;
        }
      }
    }
    .col-2 {
      width: 20%;
    }
  }
  .copyright {
    background-color: #2F3541;
    font-size: 18px;
    text-align: center;
    padding: 45px 0;
  }
}

@keyframes gradientAnimation {
  0% {
    background-position: 0 50%;
  }
  25% {
    background-position: 50% 100%;
  }
  50% {
    background-position: 100% 50%;
  }
  75% {
    background-position: 50% 0;
  }
  100% {
    background-position: 0 50%;
  }
}

@media screen and (max-width: 550px) {
  header {
    .banner-media {
      padding-bottom: 0;
      .right {
        width: 100%;
      }
    }
  }
  .section-container {
    padding: 0 2vw;
  }
  .footer {
    .col-container {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      .col-1 {
        width: 100%;
        .desc {
          font-size: 15px;
        }
      }
    }
  }
}

.fadeIn {
  opacity: 1 !important; /* 确保动画生效 */
  transform: translateY(0) !important;
  transition: transform 1s ease-in, opacity 1s ease-in;
}

.left,
.right,
.wrapper-container,
.swiper-container,
.box-data,
.section-box,
.section-container,
.phone-box,
.footer {
  opacity: 0;
  transform: translateY(80px);
}
</style>
