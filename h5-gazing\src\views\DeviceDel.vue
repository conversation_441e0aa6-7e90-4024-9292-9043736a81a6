<template>
  <div class="pages">
    <Header title="删除设备" :left-type="1">
      <div class="title-edit" @click="delFehact">确定</div>
    </Header>
    <div class="content">
      <div class="tip-text">告诉我们您对设备和app的看法。</div>
      <van-checkbox-group v-model="checkList">
        <van-cell-group class="check-group">
          <van-cell
            v-for="item in state.delReasonList"
            class="check-item"
            :class="item.checked ? 'check-active' : ''"
            clickable
            :key="item.id"
            :title="item.content"
          >
            <van-checkbox
              :name="item.id"
              ref="checkboxes"
              slot="right-icon"
              @click="setChecked(item)"
            />
          </van-cell>
          <div class="reset-wifi-web check-item">
            <span class="van-cell__title" @click="router.push('/wifiReset')">更改Wi-Fi网络</span>
            <van-icon name="arrow" size="20" color="#999" />
          </div>
        </van-cell-group>
      </van-checkbox-group>
      <van-field
        class="feedback-text"
        type="textarea"
        v-model="feedback"
        placeholder="请输入反馈内容"
        rows="3"
        autosize
        required
      />
      <van-button
        class="customer-service"
        plain
        type="default"
        :icon="customerServiceIcon"
        @click="customerService"
      >
        客户服务
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import { useRouter } from "vue-router"
import config from "@/config"
import { ref, reactive } from "vue"
import { showSuccessToast, showConfirmDialog } from "vant"
import customerServiceIcon from "../assets/device/customer-service.png"
import { delFacility } from "@/api/index"
import { useHomeStore } from "@/stores/home"

const router = useRouter()
const homeStore = useHomeStore()
interface dataList {
  id: number
  content: string
  checked: boolean
}
const state = reactive({
  delReasonList: [
    {
      id: 0,
      content: "无法实时预览",
      checked: false,
    },
    {
      id: 1,
      content: "设备无法使用",
      checked: false,
    },
  ] as dataList[],
  delReasonChecks:[] as dataList []
})

const feedback = ref<string>("") // 反馈内容
const checkList = ref([])

const setChecked = (item: dataList) => {
  item.checked = !item.checked
  if (item.checked) {
    if (!state.delReasonChecks.some(check => check.id === item.id)) {
      state.delReasonChecks.push(item)
    }
  } else {
    const index = state.delReasonChecks.findIndex(check => check.id === item.id)
    if (index !== -1) {
      state.delReasonChecks.splice(index, 1)
    }
  }
}

// 删除设备
const delFehact = () => {
  showConfirmDialog({
    title: "提示",
    message: "是否确认删除该设备",
  })
    .then(async () => {
      const res: any = await delFacility(config.product_key, config.device_sn,state.delReasonChecks,feedback.value)
      if (res.code == 0) {
        showSuccessToast("设备删除成功")
        homeStore.getDeviceList(2)
        setTimeout(() => {
          router.push("/home")
        }, 2000)
      }
    })
    .catch(() => {})
}

// 客户服务
const customerService = () => {}
</script>

<style lang="less" scoped>
.pages {
  background: white;
}
.content {
  padding: 16px 22px;
  box-sizing: border-box;
  .title-edit {
    font-size: 15px;
    color: #1e6fe8;
  }
  .tip-text {
    font-size: 16px;
    color: #000000;
    margin-bottom: 14px;
  }
  :deep(.check-group) {
    background: none !important;
    .check-item {
      height: 40px;
      padding: 0 16px;
      border-radius: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      border: 1px solid #e4e8ec;
      background: white;
      margin-bottom: 10px;
      .van-cell__title {
        font-size: 14px;
        color: #666666;
      }
      .van-cell__value {
        flex: none;
        .van-checkbox {
          .van-checkbox__icon {
            width: 23px;
            height: 23px;
            .van-icon {
              width: 23px;
              height: 23px;
              line-height: 23px;
              border: 1px solid #e4e8ec;
            }
            &.van-checkbox__icon--checked {
              .van-icon {
                background-color: #37c99d;
                border-color: #37c99d;
              }
            }
          }
        }
      }
    }
    .check-active {
      border: 1px solid #37c99d;
    }
  }
  .feedback-text {
    margin-top: 50px;
    border: 1px solid #e4e8ec;
    border-radius: 10px;
    // background: #F5F7F9;
  }
  :deep(.customer-service) {
    width: 100%;
    color: #999999;
    font-size: 16px;
    margin-top: 11px;
    border: 1px solid #999999;
    border-radius: 10px;
    background: white;
    .van-icon__image {
      width: 23px;
      height: 23px;
      transform: translateY(-1px);
    }
  }
}
</style>
