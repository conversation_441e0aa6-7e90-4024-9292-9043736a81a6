<template>
  <div class="page">
    <Header title="添加设备" />
    <div class="container">
      <div
        class="device-item"
        v-for="item in state.deviceList"
        :key="item.id"
        :style="getBackgroundImageStyle(item.product_item_img)"
        @click="detail(item)"
      >
        <span class="device-span">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted } from "vue"
import { useRouter } from "vue-router"
import Header from "@/components/Header.vue"
import { get_add_list } from "@/api"
import type { addDeviceData } from "@/api/base"
import config from "@/config"
import { inform_bluetooth } from "@/utils"

const router = useRouter()
const state = reactive({
  deviceList: [] as addDeviceData[],
})
const detail = (item: addDeviceData) => {
  config.add_device_list = item.children
  router.push("deviceList")
}

// 动态生成背景图片样式的方法
const getBackgroundImageStyle = (imageUrl: string) => ({
  backgroundImage: `url(${imageUrl})`,
  backgroundSize: "cover",
  backgroundPosition: "center",
  backgroundRepeat: "no-repeat",
})

const fetch = () => {
  get_add_list().then((res: any) => {
    if (res.code === 0) {
      state.deviceList = res.data
    }
  })
}

onMounted(() => {
  inform_bluetooth()
  fetch()
})
</script>

<style lang="less" scoped>
.container {
  padding: 20px;
  .device-item {
    width: 100%;
    height: 106px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 700;
    color: #222;
    position: relative;
    .device-span {
      position: absolute;
      left: 55%;
      top: 50%;
      transform: translate(0, -50%);
      .device-text {
        padding-top: 5px;
        color: #666666;
        font-size: 15px;
        font-weight: 400;
      }
    }
  }
  .device-one {
    background-image: url("../assets/1.png");
  }
  .device-two {
    background-image: url("../assets/2.png");
  }
}
</style>
