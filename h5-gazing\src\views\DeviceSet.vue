<template>
  <div class="pages">
    <Header :title="config.device.nickname" :left-type="1">
      <!-- <van-button
        type="primary"
        class="save-btn"
        color="#16BF82"
        size="small"
        round
        @click="deviceSave"
      >
        保存
      </van-button> -->
    </Header>
    <div class="box-data">
      <!-- 基本信息 -->
      <div v-if="pagesType === 1">
        <div class="details">
          <div class="details-title">设备信息</div>
          <div class="detail-item" @click="clickZone(state.info.is_primary)">
            <div class="detail-item-left">
              <div>时区</div>
              <div class="detail-item-left-text">{{ state.info.timezone }}</div>
            </div>
            <div class="detail-item-right">
              <van-icon name="arrow" size="20" color="#666" />
            </div>
          </div>
          <div class="detail-item">
            <div class="detail-item-left">
              <div>设备序列号</div>
              <div class="detail-item-left-text">{{ state.info.sn_show }}</div>
            </div>
            <div class="detail-item-right" @click="clickCopy(state.info.sn_show)">
              <div class="copy">拷贝</div>
            </div>
          </div>
          <div class="detail-item" v-if="config.device_config_item.is_4g_card">
            <div class="detail-item-left">
              <div>ICCID</div>
              <div class="detail-item-left-text">{{ state.info.attributes.sim_number }}</div>
            </div>
          </div>
          <div class="detail-item" v-if="config.device_config_item.is_4g_card === 0">
            <div class="detail-item-left">
              <div>Wi-Fi名称</div>
              <div class="detail-item-left-text">{{ state.info.attributes.wifi_ssid || '' }}</div>
            </div>
          </div>
          <div class="detail-item" v-if="config.device_config_item.is_4g_card === 0">
            <div class="detail-item-left">
              <div>MAC地址</div>
              <div class="detail-item-left-text">{{ state.info.attributes?.wifi_mac || '' }}</div>
            </div>
          </div>
          <div class="detail-item">
            <div class="detail-item-left">
              <div>固件版本</div>
              <div class="detail-item-left-text">{{ state.info.attributes.soc_version }}</div>
            </div>
          </div>
          <div class="detail-item">
            <div class="detail-item-left">
              <div>MCU版本号</div>
              <div class="detail-item-left-text">{{ state.info.attributes.mcu_version }}</div>
            </div>
          </div>
          <div class="detail-item">
            <div class="detail-item-left">
              <div>APP版本号</div>
              <div class="detail-item-left-text">{{ config.app_version }}</div>
            </div>
          </div>
        </div>

        <div class="delet" @click="delFehact">
          <img class="del-img" src="https://cache.gdxp.com/acce/assets/facility/del.png" alt="" />
          设备删除
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import config from "@/config"
import { ref, reactive, onActivated ,onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"
import { delFacility,set_device_timezone } from "@/api/index"
import { clickZone,clickCopy } from "@/utils";
import { showSuccessToast, showConfirmDialog } from "vant"
import { useHomeStore } from "@/stores/home"


const router = useRouter()
const route = useRoute()
const homeStore = useHomeStore()
const pagesType = ref<number>(0)
const state = reactive({
  info: config.device,
  cloudObj: {},
})

// 保存
const deviceSave = () => {
  // if (pagesType.value === 1) {
  // } else if (pagesType.value === 6) {
  //   state.info.properties.ring_volume = ring_volume.value
  //   handle_volume(ring_volume.value)
  // }
}

// 删除设备
const delFehact = () => {
  if(config.device.is_primary){
    router.push("/deviceDel")
  }else {
    showConfirmDialog({
    title: "提示",
    message: "是否确认删除该设备",
  })
    .then(async () => {
      const res: any = await delFacility(config.product_key, config.device_sn,[],'')
      if (res.code == 0) {
        showSuccessToast("设备删除成功")
        homeStore.getDeviceList(2)
        setTimeout(() => {
          router.push("/home")
        }, 2000)
      }
    })
    .catch(() => {})
  }
}

// 接收APP传过来的时区
const sendTimezone = (time:string)=>{
  if(time){
    set_device_timezone(state.info.device_sn,time).then((res:any)=>{
      if(res.code === 0){
        state.info.timezone = time
      }
    })
  }
}

onMounted(()=>{
  window.sendTimezone = sendTimezone
})

onActivated(() => {
  state.info = config.device
  pagesType.value = Number(route.query.id)
})
</script>

<style lang="less" scoped>
.box-data {
  padding: 20px 16px 16px 16px;
}
.save-btn {
  width: 60px;
}
.details {
  background-color: #fff;
  padding: 20px 15px 0px 15px;
  border-radius: 12px;
  margin-bottom: 15px;
  .details-title {
    height: 45px;
    display: flex;
    align-items: center;
    font-size: 24px;
    color: #333;
    font-weight: bold;
  }
  .detail-item {
    display: flex;
    justify-content: space-between;
    font-size: 15px;
    color: #000;
    height: 70px;
    align-items: center;
    border-bottom: 1px solid #eeeeee;
    &:last-child {
      border: none;
    }
    .detail-item-left {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 60%;
      font-weight: 700;
      .detail-item-left-text {
        font-size: 13px;
        font-weight: 400;
        color: #999;
        padding-top: 10px;
        word-break: break-all;
      }
    }
    .detail-item-right {
      width: 40%;
      font-weight: 400;
      color: #666666;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .copy {
        width: 40px;
        height: 32px;
        border-radius: 7px;
        border: 1px solid #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
.delet {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  font-size: 16px;
  font-weight: 400;
  color: #f65252;
  background-color: #fff;
  border-radius: 12px;
  .del-img {
    width: 20px;
    height: 20px;
    margin-right: 5px;
  }
}
</style>
