# Figma MCP 配置和使用指南

## 已完成的配置

✅ **VS Code MCP 配置已添加**
- 已在 `.vscode/settings.json` 中添加 Figma MCP 服务器配置
- 服务器地址：`http://127.0.0.1:3845/sse`
- 已启用 MCP 发现和聊天代理功能

## 下一步操作（需要你手动完成）

### 1. 安装 Figma 桌面应用
- 下载地址：https://www.figma.com/downloads/
- 确保更新到最新版本

### 2. 启用 Figma MCP 服务器
1. 打开 Figma 桌面应用
2. 创建或打开一个 Figma Design 文件
3. 点击左上角的 **Figma 菜单**
4. 在 **Preferences** 下选择 **Enable Dev Mode MCP Server**
5. 确认看到服务器启用的消息

### 3. 安装 GitHub Copilot（如果还没有）
- VS Code 中的 MCP 功能需要 GitHub Copilot
- 在 VS Code 扩展市场搜索并安装 "GitHub Copilot"

### 4. 重启 VS Code
- 配置生效需要重启 VS Code
- 重启后打开聊天工具栏：`Alt+Cmd+B` (Mac) 或 `Alt+Ctrl+B` (Windows)
- 切换到 **Agent** 模式

## 使用方法

### 方式一：基于选择
1. 在 Figma 桌面应用中选择一个框架或组件
2. 在 VS Code 中打开 Copilot Chat
3. 提示：`请根据我在 Figma 中选择的设计生成 Vue 3 + TypeScript 代码`

### 方式二：基于链接
1. 在 Figma 中右键点击框架/组件 → **Copy link**
2. 在 VS Code 中提示：`请根据这个 Figma 链接生成 Vue 3 代码：[粘贴链接]`

## 推荐的提示模板

```
请根据 Figma 设计生成 Vue 3 + TypeScript + Tailwind CSS 代码：
- 使用 Composition API
- 响应式设计
- 语义化的组件命名
- 提取设计变量（颜色、间距等）
- 生成到 src/components/ 目录
```

## 项目结构建议

基于你的项目，建议在每个项目中创建：
```
src/
├── components/
│   ├── ui/           # 基础 UI 组件
│   ├── layout/       # 布局组件
│   └── business/     # 业务组件
├── assets/
│   ├── styles/       # 全局样式
│   └── images/       # 图片资源
└── types/            # TypeScript 类型定义
```

## 故障排除

如果 MCP 服务器连接失败：
1. 确保 Figma 桌面应用正在运行
2. 确保已启用 Dev Mode MCP Server
3. 重启 VS Code 和 Figma 桌面应用
4. 检查 VS Code 输出面板中的错误信息

## 注意事项

- 需要 Figma Professional/Organization/Enterprise 计划
- 需要 Dev 或 Full 席位
- 只能在 Figma 桌面应用中使用，不支持网页版
- 服务器地址 `127.0.0.1:3845` 只在本地可用
