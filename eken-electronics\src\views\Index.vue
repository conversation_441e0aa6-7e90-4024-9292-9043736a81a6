<template>
  <div class="pages">
    <header class="container-top">
      <div class="nav-box">
        <div class="nav-left" style="font-size: 20px">
          <!-- <img style="width: 30px" src="../assets/logo.png" alt="" /> Aiwit -->
          EKEN ELECTRONICS LIMITED
        </div>
        <div class="nav-right">
          <div
            class="nav-item"
            @click="tabClick(1, '')"
            :class="navActive === 1 ? 'active' : ''"
          >
            Home
          </div>
          <div
            class="nav-item"
            @click="nav('/SecurityReporting')"
            :class="navActive === 2 ? 'active' : ''"
          >
            Vulnerability Disclosure
          </div>
          <div
            class="nav-item"
            @click="nav('/SoftwareUpdates')"
            :class="navActive === 3 ? 'active' : ''"
          >
            Software Updates
          </div>
          <div
            class="nav-item"
            @click="nav('/login')"
            :class="navActive === 4 ? 'active' : ''"
          >
            Log In
          </div>
        </div>
      </div>
    </header>
    <main class="introduce">
      <div class="introduce-box">
        <div class="introduce-left">
          <div class="left-data">
            <div class="left-top">
              Get our mobile app: Aiwit
              <img
                style="
                  width: 40px;
                  margin-left: 15px;
                  transform: translateY(2px);
                "
                src="../assets/logo.png"
                alt=""
              />
            </div>
            <div class="left-text">Free to download!</div>
            <div class="item">
              <img class="gou-img" src="../assets/dagou.png" alt="" />
              Easily set up your camera devices in the Aiwit App.
            </div>
            <div class="item">
              <img class="gou-img" src="../assets/dagou.png" alt="" />
              Monitor your home with high-definition video.
            </div>
            <div class="item">
              <img class="gou-img" src="../assets/dagou.png" alt="" />
              Receive instant notifications.
            </div>
            <div class="item">
              <img class="gou-img" src="../assets/dagou.png" alt="" />
              See, hear, and speak to visitors from anywhere.
            </div>
            <div class="data-box">
              <a
                href="https://apps.apple.com/mo/app/aiwit/id1484584645"
                class="data-btn"
              >
                <img class="btn-img" src="../assets/applestore.svg" alt="" />
              </a>
              <a
                href="https://play.google.com/store/apps/details?id=com.eken.aiwit&hl=en_US&gl=US"
                class="data-btn"
              >
                <img class="btn-img" src="../assets/google.svg" alt="" />
              </a>
            </div>
          </div>
        </div>
        <img class="introduce-img" src="../assets/phone.jpg" alt="" />
      </div>
    </main>
    <footer class="footer">
      <div class="code-box">
        <img class="img-code" src="../assets/aiwit_code.png" alt="" />
      </div>
      <div style="text-align: center; padding-top: 10px">
        Android/Ios App Download
      </div>
      <div class="data-box footer-data">
        <a
          href="https://apps.apple.com/mo/app/aiwit/id1484584645"
          class="data-btn"
        >
          <img class="btn-img" src="../assets/applestore.svg" alt="" />
        </a>
        <a
          href="https://play.google.com/store/apps/details?id=com.eken.aiwit&hl=en_US&gl=US"
          class="data-btn"
        >
          <img class="btn-img" src="../assets/google.svg" alt="" />
        </a>
      </div>
    </footer>

    <p class="coperight">AIWIT ©2024 Aiwit ALL Rights Reserved</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";

const navActive = ref<number>(1);
const tabClick = (index: number, name: string) => {
  if (navActive.value === index) return;
  navActive.value = index;
  if (index === 0) {
    window.scrollTo(0, 0);
  } else {
    document?.getElementById(name)?.scrollIntoView({ behavior: "smooth" });
  }
};

const router = useRouter();
const nav = (url: string) => {
  router.push(url);
};
</script>

<style lang="less" scoped>
.pages {
  min-height: 100vh;
  background-color: #fff;

  .container-top {
    width: 100%;
    height: 800px;
    position: relative;
    background-image: url("../assets/1.png");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    .nav-box {
      width: 80%;
      margin: 0 auto;
      padding-top: 40px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;
      .nav-left {
        font-size: 38px;
        font-weight: bold;
        color: #fff;
      }
    }
    .nav-right {
      width: 36%;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      .nav-item {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        flex-shrink: 0;
        text-align: left;
      }
      .active {
        color: #fff;
        border-bottom: 3px solid #3d7aff;
        padding-bottom: 5px;
      }
    }
    .corporation {
      width: 1200px;
      margin: 0 auto;
      padding-top: 10%;
      .corporation-name {
        font-size: 48px;
        color: #fff;
        margin-bottom: 5px;
      }
      .corporation-tip {
        font-size: 20px;
        color: rgba(255, 255, 255, 0.5);
        .tip-span {
          margin: 0 70px;
        }
      }
    }
    .business {
      position: absolute;
      left: 0;
      bottom: 0;
      background-color: #fff;
      width: 65%;
      height: 180px;
      display: flex;
      justify-content: flex-end;
      .business-box {
        width: 980px;
        display: flex;
        justify-content: space-around;
        padding-top: 30px;
        .buseness-left {
          font-size: 30px;
          color: #222;
        }
        .business-center {
          font-size: 16px;
          color: #999999;
          .business-text {
            margin-bottom: 10px;
          }
        }
      }
    }
  }
  .regard {
    width: 100%;
    height: 800px;
    background-image: url("../assets/2.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    padding-top: 80px;
    .regard-box {
      width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: flex-end;
      .regard-list {
        width: 50%;
        .regard-title {
          font-size: 48px;
          color: #222;
          margin-bottom: 10px;
        }
        .regard-tip {
          font-size: 15px;
          color: #999999;
          margin-bottom: 20px;
        }
        .regard-test {
          font-size: 16px;
          color: #222;
          margin-bottom: 20px;
        }
        .regard-mage {
          width: 118px;
          height: 36px;
          font-size: 14px;
          color: #fff;
          background: #3d7aff;
          display: flex;
          align-items: center;
          justify-content: space-around;
        }
      }
    }
  }
  .principal {
    padding-top: 0;
    background-position: initial;
    background-image: url("../assets/3.png");
    .principal-box {
      width: 1000px;
      margin: 0 auto;
      height: 800px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .principal-left {
        width: 350px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .principal-left-top {
          margin-bottom: 40px;
          .principal-left-title {
            font-size: 40px;
            color: #fff;
            margin-bottom: 5px;
          }
          .principal-left-tip {
            font-size: 15px;
            color: rgba(255, 255, 255, 0.5);
          }
        }
      }
      .principal-right {
        flex: 1;
        padding-top: 65px;
        .principal-data {
          width: 100%;
          display: flex;
          justify-content: space-around;
        }
      }
      .principal-left-center {
        position: relative;
        margin-bottom: 80px;
        .principal-img-text {
          position: absolute;
          left: 16px;
          bottom: 30px;
          font-size: 16px;
          color: #fff;
        }
      }
      .principal-img {
        width: 165px;
        height: 200px;
      }
    }
  }
  .contact {
    padding-top: 0;
    background-image: url("../assets/4.png");
    height: 758px;
    .contact-box {
      width: 1200px;
      margin: 0 auto;
      padding-top: 200px;
      .contact-title {
        font-size: 48px;
        color: #fff;
        margin-bottom: 5px;
      }
      .contact-tip {
        font-size: 15px;
        color: rgba(255, 255, 255, 0.5);
        margin-bottom: 40px;
      }
      .contact-text-top {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.5);
        margin-bottom: 10px;
      }
      .contact-text {
        font-size: 16px;
        color: #fff;
        margin-bottom: 40px;
      }
    }
  }
  .container-buttom {
    height: 120px;
    background-color: #1a1721;
  }

  .top-title {
    max-width: 1200px;
    margin: 0 auto;
    height: 100%;
    display: flex;
    align-items: center;
    font-size: 30px;
    color: #333;
    font-weight: bold;
  }
  .top-img {
    height: 50px;
    margin-right: 10px;
  }
  .slideshow-container {
    width: 100%;
    margin: auto;
    padding-top: 64px;
    background: linear-gradient(89.7deg, #883aff 47.05%, #a971ff 99.16%);
    height: 740px;
    display: flex;
    justify-content: center;
  }
  .banac {
    max-width: 1200px;
    display: flex;
  }
  .banac-title {
    padding-top: 80px;
    font-size: 55px;
    color: #fff;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .banac-left {
    width: 50%;
  }
  .banac-text {
    font-size: 20px;
    color: #fff;
  }
  .text-container {
    position: relative;
    height: 50px; /* 根据需要调整高度 */
    line-height: 50px; /* 与高度保持一致，实现垂直居中 */
    overflow: hidden;
    font-size: 55px;
    color: rgb(218, 253, 42);
    padding-top: 30px;
    margin-bottom: 40px;
  }

  .text {
    position: absolute;
    width: 100%;
    height: 100%;
    animation: scrollText 7s linear infinite;
    opacity: 0;
  }

  .text:nth-child(1) {
    animation-delay: 0s;
  }

  .text:nth-child(2) {
    animation-delay: 2.222s; /* 10s / 3 */
  }

  .text:nth-child(3) {
    animation-delay: 4.444s; /* 10s * 2 / 3 */
  }

  /* 根据你添加的文字数量，继续添加更多的animation-delay */

  @keyframes scrollText {
    0% {
      transform: translateY(100%);
      opacity: 0;
    }
    10%,
    20% {
      opacity: 1;
    }
    30%,
    100% {
      transform: translateY(-100%);
      opacity: 0;
    }
  }

  .introduce {
    overflow: hidden;
    display: flex;
    justify-content: center;
  }

  .introduce-box {
    display: flex;
    justify-content: end;
    position: relative;
    max-width: 1200px;
  }

  .introduce-img {
    max-width: 953px;
    height: auto;
  }
  .introduce-left {
    width: 247px;
    height: auto;
  }
  .left-data {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(-0%, -50%);
    width: 60%;
  }
  .left-top {
    font-size: 45px;
    color: #000;
    margin-bottom: 20px;
    font-weight: bold;
  }
  .left-text {
    font-size: 20px;
    color: #000;
    margin-bottom: 20px;
  }
  .item {
    font-size: 18px;
    color: #000;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
  .gou-img {
    width: 25px;
    height: 25px;
    margin-right: 10px;
  }
  .data-box {
    display: flex;
    padding: 50px 10px 0;
    gap: 30px;
  }
  .data-btn {
    display: block;
    width: 181px;
    height: 54px;
  }
  .btn-img {
    width: 100%;
    height: 100%;
  }

  @media screen and (max-width: 550px) {
    .introduce {
      width: 100vw;
      display: flex;
      flex-direction: column;
      padding: 16px;
    }
    .introduce-left {
      width: 100%;
    }
    .introduce-img {
      display: none;
    }
    .left-data {
      width: 100%;
      position: unset;
      transform: unset;
    }
    .introduce-box {
      display: unset;
      position: unset;
      max-width: unset;
    }
    .left-top {
      font-size: 20px;
      color: #000;
      margin-bottom: 20px;
      font-weight: bold;
    }
  }
  .footer {
    max-width: 1200px;
    margin: 0 auto;
    padding-top: 50px;
  }
  .code-box {
    display: flex;
    justify-content: center;
  }
  .img-code {
    width: 200px;
    height: 200px;
  }
  .footer-data {
    display: flex;
    justify-content: center;
    padding-top: 30px;
  }
  .footer-text {
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    color: #333;
  }
  .banac-right {
    width: 50%;
  }
  .ban-img {
    max-width: 100%;
    height: auto;
  }
}

.coperight {
  background-color: #fff;
  text-align: center;
  padding: 20px 0;
  color: #000;
}
</style>
