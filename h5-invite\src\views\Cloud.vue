<template>
  <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white">
    <van-swipe-item @click="click_home_family" v-if="config.show_type === '1'">
      <main class="container" v-if="config.event === '1'">
        <div class="title">Cloud Storage Vip</div>
        <div class="title-tip">Supports cloud storage for up to 10 devices.</div>
        <div class="data-item">
          <img class="data-img" src="https://cache.gdxp.com/acce/assets/meal/1.png" alt="" />
          Video Review
        </div>
        <div class="data-item">
          <img class="data-img" src="https://cache.gdxp.com/acce/assets/meal/3.png" alt="" />
          Loop Recording
        </div>
        <div class="data-item">
          <img class="data-img" src="https://cache.gdxp.com/acce/assets/meal/2.png" alt="" />
          Video Download
        </div>
        <div class="data-item">
          <img class="data-img" src="https://cache.gdxp.com/acce/assets/meal/4.png" alt="" />
          Unlimited storage
        </div>
        <div class="data-btn">Subscribe</div>
      </main>
      <main class="discount" v-else-if="config.event === '2'">
        <div class="dis-title" :class="config.platform === 'android'?'android-dis-title':''">
          <div class="dis-top">Cloud Storage Vip</div>
        </div>
        <div class="dis-cneter">
          <div class="dis-text" id="dis-text">
            <div>Supports cloud storage</div>
            <div>for up to 10 devices.</div>
          </div>
          <div class="dis-right">
            <div class="dis-tip">
              <div id="dis-num">{{ 100 - Number(config.discount) }}</div>
              <div class="dis-tip-link">%</div>
              <div class="dis-tip-text">OFF</div>
            </div>
          </div>
        </div>
        <div class="dis-item">
          <div class="hours">Hours</div>
          <div class="hours minutes">Minutes</div>
          <div class="hours seconds">Seconds</div>
          <div class="dis-item-left">
            <flipcountdown
              v-if="Number(config.countdown)"
              :deadline="Number(config.countdown)"
            ></flipcountdown>
          </div>
          <div class="dis-item-right" id="dis-item-right">Subscribe</div>
        </div>
      </main>
    </van-swipe-item>
    <van-swipe-item v-for="item in state.goodsList" :key="item.img_link" @click="details(item)">
      <van-image class="item-link-img" :src="item.img_link">
        <template v-slot:loading>
          <van-loading type="spinner" size="30" />
        </template>
      </van-image>
    </van-swipe-item>
  </van-swipe>
</template>

<script setup lang="ts">
import config from "@/config"
import { reactive, onMounted } from "vue"
import { get_product_discount_info } from "@/api"
import type { listData } from "@/api/base"
import { click_home_family, click_discount } from "@/utils"
import flipcountdown from "@/components/Countdown.vue"

const state = reactive({
  goodsList: [] as listData[],
})

const fetch = () => {
  get_product_discount_info(config.token).then((res: any) => {
    if (res.resultCode === 0) {
      if (res.content && res.content.length > 0) {
        state.goodsList = res.content
      }
    }
  })
}

const details = (item: listData) => {
  config.listItem = item
  let url = new URL(`${window.location.href.replace(/discount=\d+/, `discount=${item.discount}`)}guide`)
  url.searchParams.append("discount_code", item.discount_code);
  click_discount(url.href)
}

onMounted(() => {
  fetch()
})
</script>

<style lang="less" scoped>
.my-swipe {
  width: 100%;
  height: 163px;
  .container {
    padding: 0 20px;
    height: 163px;
    background-image: url("../assets/top.png");
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    border-radius: 10px;
    position: relative;
  }
  .title {
    padding-top: 30px;
    font-size: 18px;
    color: #fff;
    font-weight: 400;
  }
  .title-tip {
    font-size: 12px;
    color: #00ffa3;
    margin-bottom: 3px;
  }
  .data-item {
    font-size: 15px;
    color: #fff;
    display: flex;
    align-items: center;
    margin-bottom: 1px;
  }
  .data-img {
    width: 15px;
    height: 15px;
    margin-right: 5px;
  }
  .data-btn {
    position: absolute;
    right: 30px;
    bottom: 14px;
    width: 100px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #0f3172;
    background: linear-gradient(180deg, #00ffa3 0%, #08ecd1 100%);
    border-radius: 17px;
    z-index: 9;
  }
}
.item-link-img {
  width: 100%;
  height: 163px;
}

.discount {
  width: 100vw;
  height: 163px;
  background: linear-gradient(22.38deg, #1d84e2 12.63%, #2261dc 83.46%);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 10px;
  position: relative;
}
.dis-title {
  padding-top: 32px;
  padding-left: 16px;
  padding-right: 16px;
  position: relative;
  z-index: 9;
}
.android-dis-title{
  padding-top: 22px;
}
.dis-cneter {
  padding-top: 3px;
  padding-left: 16px;
  padding-right: 16px;
  margin-bottom: 11px;
  position: relative;
  display: flex;
  justify-content: space-between;
}
.flow-top {
  padding-top: 10px;
}
.dis-text {
  width: 70%;
  font-size: 20px;
  color: #fff;
  line-height: 22px;
  margin-bottom: 5px;
  position: relative;
  z-index: 9;
}
.dis-odd {
  margin-bottom: 5px;
}
.flow-text {
  font-size: 13px;
  line-height: 15px;
}
.dis-tip-link {
  font-size: 18px;
  color: #261505;
  position: absolute;
  right: 6px;
}
.dis-top {
  font-size: 13px;
  color: #fff;
  border-radius: 15.5px;
  display: inline-block;
  padding: 2px 12px;
  background: #11409b;
}
.flow-title {
  background-color: #102e65;
}
.dis-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 9;
  padding: 0 16px;
}
.hours {
  position: absolute;
  left: 15px;
  top: -16px;
  font-size: 8px;
  line-height: 14px;
  color: #ddd;
}
.minutes {
  position: absolute;
  left: 83px;
  top: -16px;
}
.seconds {
  position: absolute;
  left: 150px;
  top: -16px;
}
.dis-item-left {
  display: flex;
}
.dis-right {
  display: flex;
}
.dis-tip {
  position: absolute;
  right: 16px;
  top: -30px;
  width: 76px;
  height: 76px;
  background-color: #ffe500;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: #261505;
  font-weight: 700;
  flex-direction: column;
  z-index: 9;
}
.flow-img {
  position: absolute;
  right: 0;
  top: -35px;
  width: 82px;
  height: 90px;
  z-index: 9;
}
#dis-num {
  padding-right: 15px;
}
.dis-num {
  display: flex;
  line-height: 36px;
}
.dis-tip-text {
  font-size: 16px;
  color: #261505;
}
.dis-time {
  display: flex;
  align-items: center;
}

.dis-item-right {
  width: 105px;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
  border-radius: 17px;
  cursor: pointer;
  position: relative;
  z-index: 9;
  background: linear-gradient(to right, #999, #f441a5, #ffeb3b, #09a8f4);
  background-size: 400%;
  border-radius: 17px;
  z-index: 1;
  animation: streamer 5s infinite;
}
.dis-item-right::before {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  bottom: -1px;
  right: -1px;
  background: linear-gradient(to right, #999, #f441a5, #ffeb3b, #09a8f4);
  background-size: 400%;
  border-radius: 17px;
  z-index: -1;
  filter: blur(8px);
}
@keyframes streamer {
  100% {
    /* 背景位置 */
    background-position: -400% 0;
  }
}
.dis-item-right .round {
  position: absolute;
  z-index: 1;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border: 1px solid #ffffff;
  border-radius: inherit;
  border-radius: 17px;
}
</style>
