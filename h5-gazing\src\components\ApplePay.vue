<template>
  <div id="apple-pay-button"></div>
</template>

<script lang="ts">
import { defineComponent, onMounted } from "vue"
import { createElement, loadAirwallex, destroyElement } from "airwallex-payment-elements"
import config from "@/config"
import { showToast } from "vant"

export default defineComponent({
  props: {
    price: String,
    orderId: String,
  },
  setup(props) {
    onMounted(() => {
      destroyElement("applePayButton")
      // 初始化Airwallex
      loadAirwallex({
        env: "prod", // 环境，可以是 'demo' 或 'prod'
        origin: window.location.origin,
      }).then(() => {
        // 创建Apple Pay支付元素
        const element = createElement("applePayButton", {
          intent_id: config.intentId,
          client_secret: config.clientSecret,
          amount: {
            value: Number(props.price),
            currency: "USD",
          },
          totalPriceLabel: "EKEN",
          origin: window.location.origin,
          countryCode: "US",
        })

        if (element) {
          // 挂载支付元素
          const domElement = element.mount("apple-pay-button")
          if (domElement) {

            domElement.addEventListener("onSuccess", (e: any) => {
              // console.log('Payment successful', e.detail)
              showToast("支付失败")
            })

            domElement.addEventListener("onError", (e: any) => {
              showToast("支付失败")
            })
          }
        }
      })
    })
  },
})
</script>

<style lang="less" scoped></style>
