<template>
  <div class="pages-data">
    <Header title="账户" :left-type="1" />
    <div class="account-content">
      <van-cell-group :border="false">
        <div class="upGroup">
          <router-link to="/cancelEmail">
            <van-cell title="注销账号" is-link class="cell-item">
              <template #icon>
                <img src="../assets/tabbar/deleteAccount.png" class="cell-icon" />
              </template>
            </van-cell>
          </router-link>
          <router-link to="/retrievePass/1">
            <van-cell title="重置密码" is-link class="cell-item">
              <template #icon>
                <img src="../assets/tabbar/reset.png" class="cell-icon" />
              </template>
            </van-cell>
          </router-link>
        </div>
        <div class="downGroup">
          <van-cell title="退出登录" class="cell-item" @click="logOut">
            <template #icon>
              <img src="../assets/tabbar/logOut.png" class="cell-icon" />
            </template>
          </van-cell>
        </div>
      </van-cell-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { showConfirmDialog } from "vant"
import { clearLocalCache } from "@/utils"
import Header from "@/components/Header.vue"

const logOut = () => {
  showConfirmDialog({
    title: "请确认是否退出登陆?",
    message: "",
  }).then(() => {
    clearLocalCache()
  })
}
</script>

<style lang="less" scoped>
.upGroup,
.downGroup {
  background: #fff;
  border-radius: 8px;
  margin: 12px;
  overflow: hidden;
}

.downGroup {
  margin-top: 20px;
}

:deep(.van-cell) {
  padding: 16px;
  border-bottom: none;
  position: relative;

  &__title {
    font-size: 14px;
    color: #333;
    margin-left: 8px;
    position: relative;
    width: fit-content;

    &::after {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      bottom: -16px;
      height: 1px;
      background: #ebedf0;
    }
  }

  .cell-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    vertical-align: middle;
  }

  &__right-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    height: 16px;
    line-height: 16px;
    margin-top: 0;
  }
}
</style>
