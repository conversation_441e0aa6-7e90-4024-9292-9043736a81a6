<template>
  <div class="pages">
    <Header title="环境光强度" :left-type="1">
      <div class="title-edit" v-if="config.device.is_primary" @click="saveVolume">保存</div>
    </Header>
    <div class="lamplight">
      <div class="lamplight-title">
        <div class="lamplight-title-left">灯光感应</div>
      </div>
      <div class="lamplight-tip">
        灯泡机会感应环境光强度，如果当前环境光在设定的范围内，设备灯会自动打开。
      </div>
      <div class="lamplight-data">
        <div class="proen-box">
          <img class="proen-img up-imgs" src="../assets/device/day.png" alt="" />
          <img class="proen-img new-imgs" src="../assets/device/dusk.png" alt="" />
          <img class="proen-img down-imgs" src="../assets/device/night.png" alt="" />
        </div>
        <div class="proen-num">{{ lamplightValue }}</div>
        <div class="proen-top-num">100</div>
        <div class="proen-bottom-num">1</div>
        <van-slider
          vertical
          reverse
          v-model="sliderDisplayValue"
          :bar-height="34"
          @update:model-value="moveValue"
          :active-color="colors"
          :min="1"
          :max="100"
          :inactive-color="'#E7E7E7'"
        >
          <template #button>
            <div class="custom-button" :style="buttonStyle"></div>
          </template>
        </van-slider>
        <div class="proen-item" :style="{ bottom: `${ proenNum }px`}" ref="proenItemRef">
          <div class="data-img">
            <img class="proen-xian" src="../assets/device/xian.png" alt="" />
            <div class="img-yuan">{{ config.light_leve.value }}</div>
          </div>
          <div class="proen-text">当前环境光强度</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import config from "@/config"
import Header from "@/components/Header.vue"
import { ref, onMounted, computed, nextTick, watch } from "vue"
import { setProperty } from "@/components/WebRTCClient";

const lamplightValue = ref<number>(2)
const sliderDisplayValue = ref<number>(1)
const proenNum = ref<number>(0) 
const proenItemRef = ref<HTMLElement | null>(null)
const colors = ref("#F4F5F5")

const buttonStyle = computed(() => {
  const translateY = lamplightValue.value < 3 ? -26 : 0;
  return {
    transform: `translate(50%, ${translateY}px)`,
  }
})

// 将实际值转换为显示值
const convertToSliderValue = (actualValue: number) => {
  if (actualValue <= 30) {
    return Math.round(((actualValue - 2) / 28) * 69 + 1)
  } else {
    return Math.round(((actualValue - 31) / 69) * 29 + 71)
  }
}

// 将显示值转换回实际值
const convertToActualValue = (sliderValue: number) => {
  if (sliderValue <= 70) {
    return Math.round(((sliderValue - 1) / 69) * 28 + 2)
  } else {
    return Math.round(((sliderValue - 71) / 29) * 69 + 31)
  }
}

// 滑动时的处理
const moveValue = (value: number) => {
  lamplightValue.value = convertToActualValue(value)
  setColor(lamplightValue.value)
}

const setColor = (value: number)=>{
  if(value > 85) {
    colors.value = "linear-gradient(10deg, #020E1C 0%,  #020E1C 15%,#E9FEFF 80%)"
  } else if(value > 55 && value <= 85) {
    colors.value = "linear-gradient(10deg, #020E1C 0%,  #020E1C 20%,#E9FEFF 90%)"
  } else if (value > 25 && value <= 55) {
    colors.value = "linear-gradient(10deg, #020E1C 0%, #020E1C 25%, #E9FEFF 100%)"
  } else if (value > 3 && value <= 25) {
    colors.value = "linear-gradient(10deg, #020E1C 100%, #020E1C 100%, #E9FEFF 0%)"
  } else {
    colors.value = "#F4F5F5"
  }
}

const saveVolume = () => {
  setProperty([{ category: 'lamplight', value: lamplightValue.value }])
}

// 计算proenItem的位置
const calculateProenPosition = () => {
  nextTick(() => {
    const slider = document.querySelector('.van-slider') as HTMLElement
    const proenItem = proenItemRef.value as HTMLElement
    if (slider && proenItem && config.light_leve.value !== undefined) {
      // 获取滑块容器的高度
      const sliderHeight = slider.getBoundingClientRect().height

      // 获取proenItem的高度
      const proenItemHeight = proenItem.getBoundingClientRect().height

      // 计算相对位置(0-100的值转换为实际像素)
      const position = (config.light_leve.value / 100) * sliderHeight - (proenItemHeight / 2)

      // 设置proenItem的位置
      if(config.light_leve.value >= 100) {
        proenNum.value = position - 28
      } else if (config.light_leve.value > 70 && config.light_leve.value < 100) {
        proenNum.value = position - 15
      } else if (config.light_leve.value > 50 && config.light_leve.value <= 70) {
        proenNum.value = position - 30
      } else if (config.light_leve.value > 10 && config.light_leve.value <= 50) {
        proenNum.value = position
      } else {
        proenNum.value = position - 4
      }
    }
  })
}

// 监听light_leve值的变化
watch(() => config.light_leve.value, (newValue) => {
  calculateProenPosition()
})

onMounted(() => {
  const initialValue = Number(config.device.attributes.lamplight ?? 2)
  lamplightValue.value = initialValue
  sliderDisplayValue.value = convertToSliderValue(initialValue)
  moveValue(sliderDisplayValue.value)
  calculateProenPosition()
})
</script>

<style lang="less" scoped>
.title-edit {
  font-size: 15px;
  color: #fff;
  background-color: #1ba674;
  padding: 3px 15px;
  border-radius: 20px;
}
.lamplight {
  background-color: #fff;
  border-radius: 12px;
  margin: 0 16px;
  padding: 10px 10px 50px 10px;
  .lamplight-title {
    font-size: 24px;
    color: #222222;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    .lamplight-title-left {
      display: flex;
      align-items: center;
    }
  }
  .lamplight-tip {
    font-size: 15px;
    color: #999;
    padding-top: 5px;
    margin-bottom: 10vh;
  }
  .time-period {
    display: flex;
    height: 50px;
    justify-content: space-between;
    .period-left {
      width: 60%;
    }
    .period-img {
      width: 33px;
      height: 33px;
    }
    .period-right {
      display: flex;
      justify-content: space-between;
    }
  }
  .sliding {
    position: relative;
    .sliding-left {
      font-size: 12px;
      color: #fff;
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translate(0, -50%);
      z-index: 9;
    }
    .sliding-right {
      font-size: 12px;
      color: #666666;
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translate(0, -50%);
      z-index: 9;
    }
    .hua-box {
      position: relative;
      width: 34px;
      height: 34px;
      .hua-top {
        position: absolute;
        left: 50%;
        top: -15px;
        transform: translate(-50%);
        font-size: 12px;
        color: #4774f5;
      }
      .hua-img {
        width: 34px;
        height: 34px;
      }
    }
  }
  .intensity {
    height: 60px;
    display: flex;
    align-items: center;
    .intensity-item {
      display: flex;
      align-items: center;
      .intensity-text {
        font-size: 12px;
        color: #222222;
        margin-right: 5px;
      }
      .intensity-data {
        width: 23.5px;
        height: 24.8px;
        background-image: url("https://cache.gdxp.com/acce/assets/facility/qing.png");
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        color: #4774f5;
        position: relative;
        border-radius: 50%;
        .dian-mm {
          width: 8px;
          height: 18px;
          position: absolute;
          left: 50%;
          top: -20px;
          transform: translate(-50%);
        }
      }
    }
  }
  .lamplight-data {
    height: 35vh;
    display: flex;
    justify-content: center;
    position: relative;
    .proen-num {
      position: absolute;
      left: 50%;
      transform: translate(-50%);
      top: -55px;
      font-size: 36px;
      color: #1e6fe8;
    }
    .proen-top-num {
      font-size: 12px;
      color: #666;
      position: absolute;
      left: 50%;
      transform: translate(-50%);
      top: 10px;
      z-index: 1;
    }
    .proen-bottom-num {
      font-size: 12px;
      color: #fff;
      position: absolute;
      left: 50%;
      transform: translate(-50%);
      bottom: 10px;
      z-index: 1;
    }
    .proen-box {
      position: absolute;
      left: 87px;
      top: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 101%;
      .proen-img {
        width: 33px;
        height: 33px;
      }
      .up-imgs {
        margin-top: -1px;
      }
      .new-imgs {
        margin-bottom: 87px;
      }
      .down-imgs {
        margin-bottom: -1px;
      }
    }
    .proen-item {
      position: absolute;
      right: 5%;
      bottom: 0;
      .data-img {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        .proen-xian {
          width: 36px;
          height: 16px;
          margin-right: 3px;
        }
        .img-yuan {
          width: 25px;
          height: 26.5px;
          background-image: url("../assets/device/yuan.png");
          background-position: center;
          background-repeat: no-repeat;
          background-size: 100% 100%;
          font-size: 10px;
          color: #1e6fe8;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
      .proen-text {
        font-size: 12px;
        color: #222222;
        padding-left: 40px;
      }
    }
    .custom-button {
      width: 52px;
      height: 52px;
      background-image: url("../assets/device/dg.png");
      background-position: center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      position: absolute;
      top: -10px;
      right: 0;
    }
  }
  .proen-data {
    padding-top: 5vh;
    font-size: 13px;
    color: #666;
    line-height: 18px;
    text-align: center;
    p {
      margin-bottom: 5px;
    }
  }
}

:deep(.van-slider) {
  background-color: transparent;
  
  &__button-wrapper {
    z-index: 9;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    height: 0 !important;
  }
}
</style>
