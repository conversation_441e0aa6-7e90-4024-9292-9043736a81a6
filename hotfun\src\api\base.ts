export interface chatData {
  //每日问题列表数据
  id: number
  question: string
  screen:number
}

export interface imgListData {
  //图片数据
  bucket: string
  card_id: string
  endpoint: string
  oss_from_id: number
  images: Array<imagesData>
}

export interface imagesData {
  //图片数据
  type: string
  url: string
  video_url: string
}

export interface ossTotenData {
  region_id: string
  bucket: string
  end_point: string
  credentials: {
    ossFromId: number
    accessKeySecret: string
    accessKeyId: string
    expiration: string
    securityToken: string
  }
}
