import { createApp } from "vue"
import App from "./App.vue"
import router from "./router"
import VueLuckyCanvas from "@lucky-canvas/vue"
// import VConsole from "vconsole"

//样式初始化
import "reset-css"

import { i18n, vantLocales } from "./lang"
//对vant组件进行初始化语言设置
vantLocales(i18n.global.locale.value)

// 1. 引入你需要的组件
import {
  Button,
  Popup,
  Icon,
  Swipe,
  SwipeItem,
  CountDown,
  Image,
  ImagePreview,
  Loading,
} from "vant"
// 2. 引入组件样式
import "vant/lib/index.css"

const app = createApp(App)

app.use(router)
app.use(Icon)
app.use(Button)
app.use(Popup)
app.use(Swipe)
app.use(SwipeItem)
app.use(CountDown)
app.use(Image)
app.use(ImagePreview)
app.use(Loading)
app.use(VueLuckyCanvas)
app.use(i18n)

// new VConsole()
app.mount("#app")
