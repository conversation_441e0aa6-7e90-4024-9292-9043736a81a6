# aiwit-customer项目

## 项目简介

基于vue3+vant+ts+vite+websocket开发的移动端客服聊天

## 项目文件

build - index.html - 打包更新后的index.html需要上传到Git给服务器更新
cypress - 前端自动化
dist - 打包后的文件
node_modules - 第三方插件npm安装的包

    -  api - 调用服务器接口请求地址方法（服务器数据类型校验配置）
    -  assets - 静态资源（图片）
    -  components - 封装的子组件
    -  config - APP传过来的参数（全局变量）
    -  lang - 国际化多语言配置
    
src -  oss - 封装上传图片到阿里云oss

    -  router - 项目页面路由
    -  utils - 全局封装的公共方法
    -  views - 项目页面文件
    -  app.vue - 项目根页面
    -  mian.ts - 生成根页面引入第三方插件

.gitignore - git忽略上传文件
cypress.config - 自动化配置文件
env.d - 全局ts声明
index.html - 项目根index.html
vite.config.ts - 项目打包启动配置文件

```bash
cd aiwt-mall
git remote add origin https://gitlab.kement.cn/h5-team/aiwit-customer
git branch -M main
git push -uf origin main
```

## 启动网站

要在本地启动网站，您需要安装 [Node.js](https://nodejs.org/) 和 [Yarn](https://yarnpkg.com/)。你可以运行以下命令检查是否安装了这两个软件：

## 启动指令

```bash
npm i
npm run dev
```

或者

```bash
pnpm i
pnpm run dev
```

启动后，可在 `http://localhost:****/` 进行实时浏览。
