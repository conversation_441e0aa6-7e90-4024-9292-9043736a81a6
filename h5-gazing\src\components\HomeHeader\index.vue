<template>
  <main>
    <div class="left-container">
      <img @click="showPopup" src="../../assets/home/<USER>/03.png" alt="" />
      <img src="../../assets/home/<USER>/02.png" alt="" />
    </div>
    <div class="right-container">
      <img src="../../assets/home/<USER>/01.png" alt="" v-if="!config.isSilent.value" @click="show = true" />
      <div class="silent-wrapper" v-else>
        <img src="../../assets/home/<USER>/silent.png" alt="" @click="show = true" />
        <span class="duration-badge" v-if="muteDuration">{{ muteDuration }}</span>
      </div>
      <img @click="toQrCode" src="../../assets/home/<USER>/04.png" alt="" />
    </div>
    <van-action-sheet
      v-model:show="show"
      :actions="currentActions"
      cancel-text="取消"
      :description="desTest"
      close-on-click-action
      @select="onSelect"
    />
    <van-popup
      class="left-menu-popup"
      v-model:show="showLeft"
      position="left"
      :style="{ width: '70%', height: '100%' }"
    >
      <div class="popup-container">
        <div class="logo-img">
          <img @click="showPopup" src="../../assets/home/<USER>/03.png" alt="" />
        </div>
        <div class="user-info">
          <p class="email">{{ config.username.value }}</p>
          <p class="email-des">当前账号</p>
        </div>
        <van-cell-group class="cell-group" :border="false">
          <router-link to="/myDevice">
            <van-cell title="我的设备" class="cell-item">
              <template #icon>
                <img src="../../assets/tabbar/device.png" class="cell-icon" />
              </template>
            </van-cell>
          </router-link>
          <van-cell title="清理缓存" class="cell-item">
            <template #icon>
              <img src="../../assets/tabbar/cache.png" class="cell-icon" />
            </template>
            <p class="cell-title-right">{{ "0k" }}</p>
          </van-cell>
          <router-link to="/aboutApp">
            <van-cell title="关于 Camtro" class="cell-item">
              <template #icon>
                <img src="../../assets/tabbar/about.png" class="cell-icon" />
              </template>
            </van-cell>
          </router-link>
          <van-cell title="账户" class="cell-item" @click="toAccount">
            <template #icon>
              <img src="../../assets/tabbar/help.png" class="cell-icon" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </van-popup>
  </main>
</template>
<script lang="ts" setup>
import { showSuccessToast, showFailToast } from "vant"
import { ref, onMounted, nextTick, onBeforeUnmount, watch } from "vue"
import { useRouter } from "vue-router"
import { get_device_share, network_config, mute_notifications } from "@/api/index"
import { useHomeStore } from "@/stores/home"
import config from "@/config"
import { convertMillisecondsToTime } from "@/utils"

const router = useRouter()
const homeStore = useHomeStore()
const show = ref(false)
const desTest = ref<string>("通知静音")
const muteDuration = ref("")
const actions_1 = [
  { name: "30 分钟", color: "#1E6FE8", value: 30 },
  { name: "2 小时", color: "#1E6FE8", value: 120 },
  { name: "12 小时", color: "#1E6FE8", value: 720 },
  { name: "24 小时", color: "#1E6FE8", value: 1440 },
  { name: "关闭", color: "#ee0a24", value: 0 },
]
const actions_2 = [
  { name: "增加15分钟", color: "#1E6FE8", value: 15 },
  { name: "24 小时", color: "#1E6FE8", value: 1440 },
  { name: "关闭", color: "#ee0a24", value: 0 },
]

const currentActions = ref(actions_1)

const toQrCode = () => {
  try {
    if (config.platform == "android") {
      window.android.open_scan_code("打开扫码")
    } else {
      window.webkit.messageHandlers.open_scan_code.postMessage("打开扫码")
    }
  } catch {
    console.log("不在内嵌手机端，走web摄像头")
    router.push("/qrCode")
  }
}
const showLeft = ref(false)
const showPopup = () => {
  showLeft.value = !showLeft.value
}

const send_scan_code = (code: string) => {
  if (!code) return
  if (code.length > 30) {
    network_config(code).then((res: any) => {
      if (res.code === 0) {
        showSuccessToast("注册成功")
        if (homeStore.state.deviceList && homeStore.state.deviceList.length) {
          homeStore.getDeviceList(2)
        } else {
          homeStore.getDeviceList(1)
        }
        setTimeout(() => {
          router.push("/home")
        }, 5000)
      } else {
        showFailToast(res.msg)
      }
    })
  } else {
    get_device_share(code).then((res: any) => {
      if (res.code === 0) {
        showSuccessToast("分享成功")
        if (homeStore.state.deviceList && homeStore.state.deviceList.length) {
          homeStore.getDeviceList(2)
        } else {
          homeStore.getDeviceList(1)
        }
        setTimeout(() => {
          router.push("/home")
        }, 5000)
      } else {
        showFailToast(res.msg)
      }
    })
  }
}

/** 设置通知静音的逻辑 */

// 记录上一次选择的值
const lastSelectedValue = ref(0)

// 添加节流变量
const isRequesting = ref(false)

// 倒计时定时器
let countdownTimer = null as any

// 记录当前倒计时剩余秒数
let currentRemainingSeconds = 0

// 开始倒计时
const startCountdown = (minutes: number, isAdd: boolean = false) => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }

  // 如果是增加15分钟，就用当前剩余时间加上新增的15分钟时间
  let totalSeconds = isAdd ? currentRemainingSeconds + 15 * 60 : minutes * 60

  currentRemainingSeconds = totalSeconds
  muteDuration.value = convertMillisecondsToTime(totalSeconds * 1000, "mute")

  countdownTimer = setInterval(() => {
    currentRemainingSeconds--
    if (currentRemainingSeconds <= 0) {
      clearInterval(countdownTimer)
      countdownTimer = null
      currentRemainingSeconds = 0
      nextTick(() => {
        muteDuration.value = ""
        config.isSilent.value = false
        currentActions.value = actions_1
        desTest.value = "通知静音"
      })
    } else {
      muteDuration.value = convertMillisecondsToTime(currentRemainingSeconds * 1000, "mute")
    }
  }, 1000)
}

// 将设置的时长和类型发送给后端并展示UI静音
const setMuteDuration = async (item: any, finalValue: number, type: string) => {
  if (isRequesting.value) return

  try {
    isRequesting.value = true
    let mute_min = type === "add" ? 15 : finalValue
    const res = (await mute_notifications(mute_min, type)) as any
    if (res.code === 0) {
      if (item.value === 0) {
        if (countdownTimer) {
          clearInterval(countdownTimer)
          countdownTimer = null
        }
        currentRemainingSeconds = 0
        muteDuration.value = ""
        await nextTick(() => {
          config.isSilent.value = false
          show.value = false
        })
        setTimeout(() => {
          desTest.value = "通知静音"
          currentActions.value = actions_1
          lastSelectedValue.value = 0
        }, 300)
      } else {
        await nextTick(async () => {
          config.isSilent.value = true
          desTest.value = ""
          currentActions.value = actions_2
          await nextTick(() => {
            startCountdown(finalValue, type === "add")
          })
        })
      }
    } else {
      showFailToast(res.msg || "设置失败")
    }
  } catch (error) {
    showFailToast("网络异常，请重试")
  } finally {
    isRequesting.value = false
  }
}

// 选择静音时长
const onSelect = async (item: any) => {
  try {
    let finalValue = item.value
    // 处理增加15分钟的情况
    if (item.value === 15) {
      finalValue = lastSelectedValue.value + 15
      lastSelectedValue.value = finalValue // 更新记录的值，用于下次累加
      setMuteDuration(item, finalValue, "add")
    } else {
      lastSelectedValue.value = item.value // 记录非增加操作的值
      setMuteDuration(item, finalValue, "set")
    }
  } catch (error) {
    showFailToast("网络异常，请重试")
  }
}

// 组件销毁时清除定时器
onBeforeUnmount(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
    currentRemainingSeconds = 0
  }
})

// 处理静音倒计时初始化
watch(
  () => homeStore.state.mute_notify_to,
  newVal => {
    if (newVal) {
      const currentTimestamp = Math.floor(Date.now() / 1000)
      const targetTimestamp = Number(newVal)
      if (targetTimestamp > currentTimestamp) {
        const diffMinutes = Math.floor((targetTimestamp - currentTimestamp) / 60)
        if (diffMinutes > 0) {
          config.isSilent.value = true
          desTest.value = ""
          currentActions.value = actions_2
          startCountdown(diffMinutes)
        }
      }
    }
  },
  { immediate: true }
)

onMounted(async () => {
  if(!config.username.value){
    config.username.value = localStorage.getItem('username')
  }
  window.send_scan_code = send_scan_code
})

const toAccount = () => {
  showLeft.value = false
  router.push("/account")
}
</script>
<style lang="less" scoped>
main {
  background: #f1f1f1;
  padding-left: 22px;
  padding-right: 22px;
  padding-top: calc(constant(safe-area-inset-top, 0px) + 11px);
  padding-top: calc(env(safe-area-inset-top, 0px) + 11px);
  padding-bottom: 19px;
  display: flex;
  justify-content: space-between;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 999;

  .left-container {
    display: flex;
    align-items: center;
    img:nth-child(1) {
      width: 36px;
      height: 36px;
      margin-right: 8px;
    }
    img:nth-child(2) {
      width: 4px;
      height: 22px;
    }
  }
  .right-container {
    display: flex;
    align-items: center;
    img:nth-child(1) {
      width: 27px;
      height: 27px;
      margin-right: 19px;
    }
    img:nth-child(2) {
      width: 27px;
      height: 27px;
    }
    .silent-wrapper {
      position: relative;
      img {
        width: 27px;
        height: 27px;
        margin-right: 36px;
      }
      .duration-badge {
        position: absolute;
        right: 10px;
        bottom: 6px;
        background: #f1f1f1;
        border-radius: 8px;
        padding: 1px 4px;
        font-size: 8px;
        color: #f82a2a;
        white-space: nowrap;
      }
    }
  }

  .popup-container {
    padding: 0px 16px;
    overflow-y: auto;
    position: relative;
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
    .logo-img {
      display: flex;
      justify-content: start;
      padding-top: 30px;
      margin-bottom: 10px;
      img {
        width: 52px;
        height: 52px;
      }
    }

    .user-info {
      margin-bottom: 20px;
      .email {
        font-size: 20px;
        color: #222;
        margin-bottom: 10px;
      }
      .email-des {
        font-size: 14px;
        color: #999;
      }
    }

    .cell-group {
      :deep(.van-cell) {
        padding: 16px 0;
        border-bottom: none;
        position: relative;

        &__title {
          font-size: 15px;
          color: #333;
          position: relative;
          display: flex;
          align-items: center;
          &::after {
            content: "";
            position: absolute;
            left: 0;
            right: 0;
            bottom: -16px;
            height: 1px;
            background: #ebedf0;
          }
        }
        .cell-title-right {
          text-align: right;
        }
        .cell-icon {
          width: 26px;
          height: 26px;
          margin-right: 8px;
          vertical-align: middle;
        }
      }
    }
  }
}
</style>
