<template>
  <div class="pages">
    <Header title="更改网络" :left-type="1"></Header>
    
    <div class="wifi-set-pages">
      <div class="input-one">
        <div class="input-wrapper">
          <div class="floating-label">Wi-Fi名称</div>
          <input
            class="input-name"
            v-model="wifiName"
            placeholder="输入Wi-Fi名称"
            name="wifiName"
            type="text"
            @focus="isReEmailFocused = true"
            @blur="isReEmailFocused = false"
          />
        </div>
      </div>
      <div class="input-one">
        <div class="input-wrapper">
          <div class="floating-label">Wi-Fi密码</div>
          <input
            class="input-name"
            v-model="wifiPassword"
            name="wifiPassword"
            placeholder="输入Wi-Fi密码"
            :type="showWifiPassword ? 'text' : 'password'"
            @focus="isWifiPasswordFocused = true"
            @blur="isWifiPasswordFocused = false"
          />
          <div class="icon-box">
            <van-icon
              :name="showWifiPassword ? 'eye-o' : 'closed-eye'"
              class="clear-icon"
              @click="showWifiPassword = !showWifiPassword"
            />
          </div>
        </div>
      </div>
      <div class="login-sub">
        <van-button class="login-btn" round block type="primary" color="#3D5AFD" @click="onSubmit">
          设置
        </van-button>
      </div>
      <div class="tip-text">此设备只支持2.4GHz Wi-Fi</div>
    </div>

  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import { ref, onMounted } from "vue"

const wifiName: string = ref('')
const wifiPassword: string = ref('')
const showWifiPassword: boolean = ref(false)
const isReEmailFocused: boolean = ref(false)
const isWifiPasswordFocused: boolean = ref(false)

// 更改密码
const onSubmit = () => {
  const params = {
    params1: wifiName.value, // Wi-Fi名称
    params2: wifiPassword.value, // Wi-Fi密码
  }
  console.log('wifi set params', params)
}

onMounted(() => {})
</script>

<style lang="less" scoped>
.wifi-set-pages {
  padding: 20px;
}

.input-one {
  margin-bottom: 20px;

  .input-wrapper {
    position: relative;
    width: 100%;

    .floating-label {
      font-size: 14px;
      color: 000;
      padding: 2px 2px 2px 20px;
      pointer-events: none;
      background-color: transparent;
    }

    .icon-box {
      position: absolute;
      bottom: 0;
      right: 0;
      height: 45px;
      .clear-icon {
        position: absolute;
        right: 12px; // 改为35px使图标靠近输入框右边
        top: 50%;
        transform: translateY(-50%);
        color: #999;
        font-size: 16px;
        padding: 5px;
        cursor: pointer;
        z-index: 1;
  
        &:active {
          opacity: 0.8;
        }
      }
    }
  }

  .input-name {
    width: 100%;
    height: 45px;
    background-color: #ffffff !important;
    display: flex;
    align-items: center;
    padding-left: 16px;
    padding-right: 40px;
    border-radius: 26px;
    border: 2px solid #dddddd;
    font-size: 14px;

    &:focus {
      border-color: #1e6fe8;
      outline: none;

      & + .floating-label {
        color: #1e6fe8;
      }
    }
  }

  // 分离出登录模式下的输入框特殊样式
  .input-wrapper .input-name {
    width: 83%;
  }
}

.login-sub {
  padding-top: 5px;
  margin-bottom: 20px;
  .login-btn {
    font-size: 16px;
    font-weight: bold;
    height: 50px;
  }
}

.tip-text {
  padding: 0 16px;
  font-size: 14px;
  color: #333333;
  margin-bottom: 14px;
  text-align: center;
  font-weight: 700;
}
</style>