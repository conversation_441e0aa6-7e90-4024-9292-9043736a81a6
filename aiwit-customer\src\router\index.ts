import { createRouter, createWebHashHistory } from 'vue-router'
import HomeView from '../views/Home.vue'

const router = createRouter({
    history: createWebHashHistory(import.meta.env.BASE_URL),
    routes: [
        {
            path: '/',
            name: 'home',
            component: HomeView
        },
        {
            path: '/invite',
            name: 'invite',
            component: () => import('../views/Invite.vue'),
        },
        {
            path: '/inviteDetails',
            name: 'inviteDetails',
            component: () => import('../views/InviteDetails.vue'),
        },
        {
            path: '/sendUsd',
            name: 'sendUsd',
            component: () => import('../views/SendUsd.vue'),
        },
        {
            path: '/changeDeviceAddress',
            name: 'changeDeviceAddress',
            component: () => import('../views/ChangeDeviceAddress.vue'),
        },
        {
            path: '/changeDeviceDetails',
            name: 'changeDeviceDetails',
            component: () => import('../views/ChangeDeviceDetails.vue'),
        }
    ]
})

export default router
