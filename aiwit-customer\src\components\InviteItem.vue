<template>
  <div class="free-badge-box">
    <img class="free-badge" src="../assets/3.png" alt="" />
  </div>
  <div class="invite-top">Free Gift for You!</div>
  <div class="user-img-box">
    <img class="user-img" :src="imgUrl" alt="加载失败" />
  </div>
  <div class="invite-text">Get a free device.</div>
  <div class="invite-text">Click below to claim now!</div>
  <div class="countdown-container">
    <div class="countdown-title">Offer expires in:</div>
    <van-count-down :time="giftTime" @change="changeTime">
      <template #default="timeData">
        <div class="countdown-timer">
          <div class="countdown-box">
            <div class="countdown-value">{{ timeData.hours }}</div>
            <div class="countdown-label">HOURS</div>
          </div>
          <div class="countdown-box">
            <div class="countdown-value">{{ timeData.minutes }}</div>
            <div class="countdown-label">MINS</div>
          </div>
          <div class="countdown-box">
            <div class="countdown-value">{{ timeData.seconds }}</div>
            <div class="countdown-label">SECS</div>
          </div>
        </div>
      </template>
    </van-count-down>
  </div>
  <div class="invite-btn">
    <div class="invite-box-btn" @click="giftProduct">Claim Now</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue"

defineProps({
  imgUrl: String,
})

// 子传父组件
const emit = defineEmits(["clickGift"])

const giftTime = ref(0)
const changeTime = (value: any) => {
  localStorage.setItem("giftTime", value.total)
}

const giftProduct = () => {
  emit("clickGift")
}

onMounted(() => {
  if (Number(localStorage.getItem("giftTime"))) {
    giftTime.value = Number(localStorage.getItem("giftTime"))
  } else {
    giftTime.value = 24 * 60 * 60 * 1000
  }
})
</script>

<style lang="less" scoped>
.free-badge-box {
  position: absolute;
  right: 0;
  top: 0;
}

.free-badge {
  animation: pulse 2s infinite;
  width: 100px;
  height: 48px;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

.invite-top {
  font-size: 28px;
  color: #fff;
  font-weight: bold;
  margin-bottom: 15px;
  word-break: break-word;
  text-align: center;
  padding-top: 20px;
}
.invite-text {
  width: 80%;
  margin: 0 auto;
  font-size: 14px;
  color: #fff;
  line-height: 18px;
  word-break: break-word;
  margin-bottom: 10px;
  text-align: center;
  .invite-text-sapn {
    color: #ffff00;
  }
}
.countdown-container {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 10px;
  width: 90%;
  margin: 0 auto;
  border-radius: 10px;
  .countdown-title {
    font-size: 14px;
    color: #fff;
    text-align: center;
    margin-bottom: 10px;
  }
  .countdown-timer {
    display: flex;
    justify-content: center;
    gap: 10px;
    .countdown-box {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 5px;
      padding: 10px 10px 5px 10px;
      min-width: 45px;
      color: #fff;
      text-align: center;
      .countdown-value {
        font-size: 20px;
        font-weight: bold;
      }

      .countdown-label {
        font-size: 10px;
        opacity: 0.8;
        text-transform: uppercase;
      }
    }
  }
}
.invite-btn {
  text-align: center;
  padding-top: 20px;
  .invite-box-btn {
    display: inline-block;
    background-color: #4caf50;
    color: white;
    font-size: 18px;
    font-weight: bold;
    padding: 12px 30px;
    border-radius: 30px;
    text-decoration: none;
    position: relative;
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
    transition: all 0.3s ease;
  }
}

.user-img-box {
  width: 100%;
  text-align: center;
  margin-bottom: 10px;
  .user-img {
    max-width: 100%;
    min-width: 320px;
    min-height: 150px;
  }
}
</style>
