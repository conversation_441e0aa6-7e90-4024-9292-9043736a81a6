import http from './request';

// 获取聊天信息
export function getChat(sessionId:string,device_sn:string,screen:number,page:number,src:number) {
    return http(`/chat/list_with_screen_v5/${sessionId}/${device_sn}/${screen}/${page}/${src}`);
}

// 发送聊天信息
export function saveChat(params:object) {
    return http('/chat/publish_message_v2', {
        method: 'POST',
        params: params,
    });
}


// 获取未读消息
export function getUnreadMsg(params:object) {
    return http('/chat_for_question_count_v4', {
        method: 'POST',
        params: params,
    });
}


// 已读消息
export function readMsg(params:object) {
    return http('/chat/read_message_v2', {
        method: 'POST',
        params: params,
    });
}


// 评价服务
export function saveAppraise(sessionId:string,msg_id:any,star:number,solved:number) {
    return http(`/chat/service_satisfaction_survey/${sessionId}/${msg_id}/${star}/${solved}`);
}


// 上传图片
export function imageUpload(params:object) {
    return http('/chat_image_upload', {
        method: 'POST',
        params: params
    },
    {'Content-Type':'multipart/form-data'}
    );
}


// 获取阿里云OSS服务key
export function getOssKey(sessionId:string) {
    return http(`/sts/token/${sessionId}/oss-us-west-1.aliyuncs.com/9`);
}


// 获取兑换卡信息接口
export function getExchange(code_sn:string) {
    return http(`/chat/code_sn_info/${code_sn}`);
}


// 兑换码兑换接口
export function saveExchange(sessionId:string,device_sn:string,code:string) {
    return http(`/pay/redeem_code_v2/${sessionId}/${device_sn}/${code}`);
}

// 获取用户输入地址接口
export function get_auto_customer_star_info(sessionId:string,device_sn:string,sign:string,is_repair:string) {
    return http(`/chat/get_auto_customer_star_info/${sessionId}/${device_sn}/${sign}/${is_repair}`);
}