import { createApp } from "vue"
import { createPinia } from "pinia"
import App from "./App.vue"
import "vant/lib/index.css"
import "./assets/css/global.css"
import router from "./router"
import { i18n, vantLocales } from "./lang"
//对vant组件进行初始化语言设置
vantLocales(i18n.global.locale.value)

import {
  Button,
  Popup,
  NavBar,
  Icon,
  Field,
  DatePicker,
  Calendar,
  Empty,
  Dialog,
  Switch,
  Slider,
  Cell,
  CellGroup,
  Loading,
  List,
  PullRefresh,
  SwipeCell,
  ActionSheet,
  Checkbox,
  CheckboxGroup,
  Badge,
  Progress
} from "vant"

const app = createApp(App)
app.use(createPinia())
app.use(router)
app.use(Button)
app.use(Popup)
app.use(NavBar)
app.use(Icon)
app.use(Field)
app.use(DatePicker)
app.use(Calendar)
app.use(Empty)
app.use(Dialog)
app.use(Switch)
app.use(Slider)
app.use(Cell)
app.use(CellGroup)
app.use(Loading)
app.use(List)
app.use(PullRefresh)
app.use(SwipeCell)
app.use(ActionSheet)
app.use(Checkbox)
app.use(CheckboxGroup)
app.use(Badge)
app.use(Progress)
app.use(i18n)

app.mount("#app")
