<template>
  <Header ref="headerCompRef" class="header-comp" title="客户服务"></Header>
  <div class="container" ref="containerRef" @touchmove="touchStart" @touchend="touchendData">
    <div class="tip">Service Hours：Monday to Friday，9:00 AM - 5:00 PM</div>
    <div class="box">
      <div class="chat-box chat-box1" :style="{opacity: showLastChatList ? 0 : 1}" ref="chatBoxRef" @click="cardData">
        <div
          class="box-content"
          v-for="(item, index) in chatList"
          :key="index"
          :class="'box-content_' + item.class"
        >
          <div class="box-time" v-if="item.role == 1">
            service ({{ item.dateline }})
          </div>
          <div class="box-times" v-else>{{ item.dateline }}</div>
          <div class="box-left" :class="item.role == 0 ? 'box-right' : ''">
            <div
              class="box-p"
              v-if="
                item.content.txt &&
                (item.content.txt.indexOf('.png') !== -1 ||
                  item.content.txt.indexOf('.jpg') !== -1 ||
                  item.content.txt.indexOf('.jpeg') !== -1 ||
                  item.content.txt.indexOf('.svg') !== -1 ||
                  item.content.txt.indexOf('.bmp') !== -1 ||
                  item.content.txt.indexOf('.gif') !== -1)
              "
            >
              <img
                class="user-img"
                :src="item.content.txt"
                alt="加载失败"
                @click="showImage(item.content.txt)"
              />
            </div>
            <div
              class="box-p"
              v-else-if="item.content.txt && item.content.txt.indexOf('.mp4') !== -1"
            >
              <video
                class="banen-video"
                controls
                disablePictureInPicture
                controlslist="nodownload nofullscreen noremoteplayback noplaybackrate"
                :src="item.content.txt"
              ></video>
            </div>
            <div
              class="box-p"
              style="word-break: normal; overflow-wrap: break-word"
              v-else
              v-html="item.content.txt"
            ></div>
          </div>
        </div>
      </div>
      <!-- chat-box1 和 chat-box2 这两个组件样式相同, 区别在于 chat-box2 中的是旧数据, 用来解决因数据加载下一页导致的滚动条一直跳动的问题, 当数据加载完成并且滚动条固定之后, chat-box2 才自动消失 -->
      <div v-if="showLastChatList" class="chat-box chat-box2" @click="cardData">
        <div
          class="box-content"
          v-for="(item, index) in lastChatList"
          :key="index"
          :class="'box-content_' + item.class"
        >
          <div class="box-time" v-if="item.role == 1">
            service ({{ item.dateline }})
          </div>
          <div class="box-times" v-else>{{ item.dateline }}</div>
          <div class="box-left" :class="item.role == 0 ? 'box-right' : ''">
            <div
              class="box-p"
              v-if="
                item.content.txt &&
                (item.content.txt.indexOf('.png') !== -1 ||
                  item.content.txt.indexOf('.jpg') !== -1 ||
                  item.content.txt.indexOf('.jpeg') !== -1 ||
                  item.content.txt.indexOf('.svg') !== -1 ||
                  item.content.txt.indexOf('.bmp') !== -1 ||
                  item.content.txt.indexOf('.gif') !== -1)
              "
            >
              <img
                class="user-img"
                :src="item.content.txt"
                alt="加载失败"
                @click="showImage(item.content.txt)"
              />
            </div>
            <div
              class="box-p"
              v-else-if="item.content.txt && item.content.txt.indexOf('.mp4') !== -1"
            >
              <video
                class="banen-video"
                controls
                disablePictureInPicture
                controlslist="nodownload nofullscreen noremoteplayback noplaybackrate"
                :src="item.content.txt"
              ></video>
            </div>
            <div
              class="box-p"
              style="word-break: normal; overflow-wrap: break-word"
              v-else
              v-html="item.content.txt"
            ></div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer" ref="footerRef">
      <div class="evaluate" v-if="evaluateShow">
        <div class="evaluate-text" @click.stop="openView">
          <img class="evaluate-img" src="../assets/customer/pj.png" alt="" />
          <span>{{ $t("home.evaluationService") }}</span>
        </div>
      </div>
      <div class="footer-title">
        <div class="album-img" id="album" @click.stop="click_album">
          <img class="album-img" src="../assets/customer/album.png" alt="" />
        </div>
        <form
          class="footer-form"
          :style="{ width: '65%' }"
          action="javascript:return true;"
        >
          <input
            class="footer-inputs"
            v-model.trim="textValue"
            @focus="textInputFocusin"
            @blur="textInputFocusout"
            type="text"
            ref="myInput"
            @keyup.enter.native="sendClick"
            placeholder="input message here..."
          />
          <div v-show="config.platform === 'ios' && inputMaskShow" class="input-mask" @click="inputFocus"></div>
        </form>
        <div class="footer-rights">
          <div class="camera" id="camera" @click.stop="click_camera">
            <img class="camera" src="../assets/customer/camera.png" alt="" />
          </div>
          <div class="send-box" id="send" @click.stop="sendClick">
            <img class="send" src="../assets/customer/send.png" alt="" />
          </div>
        </div>
      </div>
    </div>
    <!-- 评价弹框 -->
    <!-- <Evaluate
      ref="evaluateHtml"
      :msgId="msgId"
      :service-uid="serviceUid"
      @send-msg="sendEvaluateMsg"
      v-if="evaluateShow"
      @html-close="eventClose"
    /> -->
  </div>
</template>
<script setup lang="ts">
import Header from "@/components/Header.vue"
import { ref, onMounted, toRefs, reactive, onUnmounted, onUpdated } from "vue"
import { click_camera,click_album } from "@/utils"
import { showNotify, showToast, showImagePreview } from "vant"
import { chat_lists, screen_say, screen_read } from "@/api"
import type { chatLists, screenSayParams } from "@/api/base"
import config from "@/config"
import { i18n } from "@/lang"
import moment from 'moment'
import { useHomeStore } from "@/stores/home"

const homeStore = useHomeStore()

const { t } = i18n.global as any
// 动态接口数据
const state = reactive({
  chatList: [] as chatLists[], //因为后端传来的是List数组，而不是一条数据，这种就是定义成对象数组形式
  sendChat: {} as screenSayParams,
  pagesNum: 1,
  msgList: [] as any[], //未读消息数组
})
const { chatList } = toRefs(state)
const lastChatList = ref<chatLists[]>([])
const showLastChatList = ref(false)
const toolStatus = ref(false)
const textValue = ref("")
const msgId = ref(0) //聊天ID
const serviceUid = ref(0) //聊天uid
const screen = ref(0) //消息通道 0客服消息  1测评消息  2推广消息
const evaluateStatus = ref(false) //是否已评价
const evaluateShow = ref(false) //是否出现评价
const sendStatus = ref(false) //ios发送状态
const showBottom = ref(false) //是否弹出了评价
const footerRef = ref<HTMLElement | any>(null) //ios底部键盘弹出特殊处理
const chatBoxRef = ref<HTMLElement | any>(null) // 聊天区域
const containerBoxRef = ref<HTMLElement | any>(null) // 聊天界面
const isText = ref(true) //是否只有文本
const chatListPageObject = ref<any>({1: []}) // 页码对象数据
const chatListTotal = ref(0) // 聊天消息总数据数
const containerRef = ref<HTMLElement | any>(null) // 主内容区域
const headerCompRef = ref<HTMLElement | any>(null) // 聊天内容区域
const viewportHeight = window.innerHeight
const inputMaskShow = ref(true)

//发送消息跟图片的参数
const sendParams = (text: any, type: any) => {
  state.sendChat = {
    device_sn: config.device_sn,
    screen: screen.value,
    content: {
      txt: text,
    },
  }
}

// 文本输入框聚焦 
const myInput = ref<HTMLElement | any>(null)

// ios端恢复界面空白
const changeBlur = () => {
  setTimeout(() => {
    const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0
    window.scrollTo(0, Math.max(scrollHeight - 1, 0))
  }, 88)
}

// 发送文本消息
const sendClick = () => {
  if (textValue.value === "") {
    // 未输入内容
  } else {
    myInput.value.focus()
    // 添加参数
    sendParams(textValue.value, 0)
    saveChatData()
    textValue.value = ""
  }
}

// 发送评价消息
const sendEvaluateMsg = (data: any) => {
  // 添加参数
  sendParams(data, 1)
  saveChatData()
}

// 发送图片消息
const sendImage = (img: any) => {
  // 添加参数
  sendParams(img, 0)
  saveChatData()
}

// 发送消息
const saveChatData = () => {
  config.scrollStatus = true
  getHttpSaveChat()
}

// 接口发送消息
const getHttpSaveChat = () => {
  screen_say(state.sendChat).then((res: any) => {
    if (res.code === 0 && res.data.msg === "ok") {
      textValue.value = ""
      state.pagesNum = 2 // 更新完之后， 下次翻页请求从第二页开始
      chatListPageObject.value = {1: chatListPageObject.value[1]} // 发送完之后只留下第一页的数据
      handlePushAChat()
      myInput.value.blur()
    }
  })
}

// 手动 push 一条记录
const handlePushAChat = () => {
  let firstPageData = JSON.parse(JSON.stringify(chatListPageObject.value[1]))
  let newChatList = firstPageData.length === 20 ? firstPageData.splice(1, 20) : firstPageData
  // { content: { txt: string; }; dateline: string; ctime: number; role: number; device_sn: string; is_read: number; is_tgmsg: number; tgmsg_id: number; screen: number; id: string; class: string; msg_id: string; }
  // { content: { txt: string; }; dateline: string; ctime: number; role: number; device_sn: string; is_read: number; is_tgmsg: number; tgmsg_id: number; screen: number; id: string; class: string; msg_id: string; }
  const newItem = dealWithData({
    content: {
      txt: state.sendChat.content.txt
    },
    dateline: moment().format('YYYY-MM-DD HH:mm:ss'),
    ctime: 0,
    role: 0,
    device_sn: config.device_sn,
    is_read: 1,
    is_tgmsg: 0,
    tgmsg_id: 0,
    screen: 0,
    id: '',
    class: '',
    msg_id: '',
  }, 20, 1)
  newChatList.push(newItem)
  chatListPageObject.value[1] = newChatList
  state.chatList = newChatList
}

// 打开评价弹框
const evaluateHtml = ref<HTMLElement | any>(null)
const openView = () => {
  if (evaluateStatus.value) {
    showToast(t("home.already"))
    return
  }
  showBottom.value = true
  setTimeout(() => {
    evaluateHtml.value?.openEvaluate()
  }, 100)
}

// 分页是否还有数据
const noData = ref(true)
// 获取客服聊天记录
const fetch = (callback?: () => void) => {
  const params = {
    device_sn: config.device_sn,
    screen: screen.value,
    page: state.pagesNum,
  }
  lastChatList.value = JSON.parse(JSON.stringify(state.chatList))
  chat_lists(params).then((res: any) => {
    if (res.code == 0) {
      if (res.data.list && res.data.list.length) {
        chatListTotal.value = res.data.total
        let list: chatLists[] = []
        if (state.pagesNum === 1) chatListPageObject.value = {}
        res.data.list.reverse()
        res.data.list.map((item: chatLists, index: number) => {
          const newItem = dealWithData(item, index)
          list.push(newItem)
          if (item.is_read == 0) {
            state.msgList.push(item.id)
          }
        })
        if (!chatListPageObject.value[state.pagesNum]) chatListPageObject.value[state.pagesNum] = {}
        chatListPageObject.value[state.pagesNum] = list
        let CL: chatLists[][] = Object.values(chatListPageObject.value)
        CL.reverse()
        state.chatList = CL.flat()

        // 未读消息传给后端
        if (state.msgList.length) {
          screen_read({
            screen: screen.value,
            msg_id: state.msgList,
            device_sn: config.device_sn,
          }).then((res: any) => {
            if (res.data.msg === 'ok') {
              homeStore.get_unread_count()
            }
          })
        }

        if (res.data.total !== state.chatList.length) state.pagesNum++

        if (typeof callback === "function") {
          callback()
        }
      }
    }
  })
}

const dealWithData = (item: chatLists, index: number, page?: number) => {
  if (item.content.txt && item.content.txt.indexOf("\n") !== -1) {
    isText.value = false
  } else if (
    item.content.txt &&
    (item.content.txt!.indexOf("\r") !== -1 || item.content.txt.indexOf("\n") !== -1)
  ) {
    item.content.txt = item.content.txt.replace(/\r\n/g, "<br/>")
    isText.value = false
  } else if (item.content.txt && item.content.txt.indexOf("img[") !== -1) {
    const text = item.content.txt.match(/\[(.*?)\]/)
    text && (item.content.txt = text[1])
    item.content.img = item.content.txt
  } else if (item.content.txt && item.content.txt.indexOf("url[") !== -1) {
    const text = item.content.txt.match(/\[(.*?)\]/)
    text && (item.content.url = text[1])
    isText.value = false
  } else if (item.content.txt && item.content.txt.indexOf("embed[") !== -1) {
    const text = item.content.txt.match(/\[(.*?)\]/)
    text && (item.content.txt = text[1])
    isText.value = false
  }
  item.class = (page || state.pagesNum) + "_" + index
  item.msg_id = item.id

  return item
}

// 取消聚焦
const cardData = () => {
  myInput.value.blur()
  changeBlur()
}

// 滚动时收回键盘
const fn_isShowKeyboard = () => {
  // 加载下一页
  if (
    chatBoxRef.value?.scrollTop === 0 &&
    chatListTotal.value &&
    state.chatList.length &&
    state.chatList.length !== chatListTotal.value
  ) {
    config.scrollStatus = false
    const targetData = state.chatList[0]
    showLastChatList.value = true
    fetch(() => {
      setTimeout(() => {
        const lastChatTxtDom = document.querySelectorAll(
          ".box-content_" + targetData.class
        )[0] as HTMLElement
        chatBoxRef.value?.scrollTo(0, lastChatTxtDom?.offsetTop - 30)
        showLastChatList.value = false
      }, 200)
    })
  }
}

// 滚动到底部
const scroolBody = () => {
  if (state.chatList.length < 6 && isText.value) return
  if (config.scrollStatus) {
    chatBoxRef.value?.scrollTo(0, chatBoxRef.value?.scrollHeight)
  }
}

//图片预览
const showImage = (img: any) => {
  showImagePreview([img])
}

const inputFocus = () => {
  // 手动重新聚焦, 解决ios首次点击input后没有光标的问题(仅在ios app上出现)
  if (config.platform === "ios") {
    myInput.value.focus()
    inputMaskShow.value = false
  }
}

// 键盘弹出
const textInputFocusin = () => {
  if (config.platform === "ios") {
    sendStatus.value = true
  }
  if (showBottom.value) return
  // footerRef.value.style = "padding-bottom: 10px"
  //软键盘弹出的事件处理
  if (toolStatus.value) {
    toolStatus.value = false
  }
  if (state.chatList.length <= 5 && isText.value) {
    if (state.chatList.length <= 3) {
      setTimeout(() => {
        window.scrollTo(0, 40)
      }, 100)
    } else {
      setTimeout(() => {
        window.scrollTo(0, 270)
      }, 100)
    }
  }
}

// 键盘收下
const textInputFocusout = () => {
  if (config.platform === "ios") {
    sendStatus.value = false
    inputMaskShow.value = true
  }
  // footerRef.value.style =
  //   "padding-bottom: constant(safe-area-inset-bottom);padding-bottom: env(safe-area-inset-bottom);"
}

// 节流函数
const debounce = (fn: any, delay = 200) => {
  let timer: any = null
  return function (...args: any[]) {
    if (timer != null) {
      clearTimeout(timer)
      timer = null
    }
    timer = setTimeout(() => {
      fn.call(fn, ...args)
    }, delay)
  }
}

const touchStart = () => {
  if (config.platform === "ios" && sendStatus.value) {
    myInput.value.blur()
    // changeBlur()
    // if (state.chatList.length > 7) {
    //   window.scrollTo(0, document.body.scrollHeight)
    // }
  }
}

// 处理点击屏幕会滚动
let iLastTouchTime: any = null
const touchendData = (e: any) => {
  if (config.platform === "ios") {
    let iNowTime = new Date().getTime()
    iLastTouchTime = iLastTouchTime || iNowTime + 1
    let delta = iNowTime - iLastTouchTime
    if (delta < 500 && delta > 0) {
      e.preventDefault()
      return false
    }
    iLastTouchTime = iNowTime
  }
}

// 评价弹框关闭
// function eventClose() {
//   showBottom.value = false
// }

// 接收原生APP上传后的图片地址
const cameraChages = (file: any)=> {
  if (file) {
    sendImage(file)
  } else {
    showToast("图片地址是空的!")
  }
}

let winTouScrY = 0
const windowTouchScroll = () => {
  winTouScrY = window.scrollY
}

const windowTouchEnd = () => {
  if (winTouScrY) {
    window.scrollTo(0, 0)
  }
}

let once = false
const visualViewResizeHandle = () => {
  const keyboardHeight = viewportHeight - (window.visualViewport?.height || 0)
  console.log('界面/键盘高度:', viewportHeight,'/',keyboardHeight)
  let containerSubHeight = 0
  let hasSaveArea = false
  if (keyboardHeight > 100) {
    containerSubHeight = headerCompRef.value.$el.clientHeight + keyboardHeight
    hasSaveArea = false
    window.addEventListener('scroll', windowTouchScroll)
    window.addEventListener('touchend', windowTouchEnd)
    if (!once) {
      setTimeout(() => {
        window.scrollTo(0, 1)
        window.scrollTo(0, 0)
        chatBoxRef.value?.scrollTo(0, chatBoxRef.value?.scrollHeight)
        once = true
      }, 200)
    }
  } else {
    containerSubHeight = headerCompRef.value.$el.clientHeight
    hasSaveArea = true
    window.removeEventListener('scroll', windowTouchScroll)
    window.removeEventListener('touchend', windowTouchEnd)
    once = false
  }
  containerRef.value.style = `
    height: calc(100vh - ${containerSubHeight}px);
    height: calc(100vh - ${containerSubHeight}px);
  `
  chatBoxRef.value.style = `
    height: calc(100% - 80px - ${hasSaveArea ? 'constant(safe-area-inset-bottom)' : '10px'});
    height: calc(100% - 80px - ${hasSaveArea ? 'env(safe-area-inset-bottom)' : '10px'});
  `
  footerRef.value.style = `
    bottom: ${hasSaveArea ? 'constant(safe-area-inset-bottom)' : '10px'};
    bottom: ${hasSaveArea ? 'env(safe-area-inset-bottom)' : '10px'};
  `
}

onMounted(() => {
  //获取聊天记录
  config.scrollStatus = true
  fetch()
  window.cameraChages = cameraChages
  // 监听滚动加载下一页
  chatBoxRef.value?.addEventListener("scroll", debounce(fn_isShowKeyboard, 20))
  // 主内容区域高度设置
  containerRef.value.style = `
    height: calc(100vh - ${headerCompRef.value.$el.clientHeight}px);
    height: calc(100vh - ${headerCompRef.value.$el.clientHeight}px);
  `
  // 聊天区域高度设置
  chatBoxRef.value.style = `
    height: calc(100% - 80px - constant(safe-area-inset-bottom));
    height: calc(100% - 80px - env(safe-area-inset-bottom));
  `
  // 底部输入框位置设置
  footerRef.value.style = `
    bottom: constant(safe-area-inset-bottom);
    bottom: env(safe-area-inset-bottom);
  `
  /** visualViewport 兼容性
   * iOS Safari‌：≥13 版本支持（iOS 13+，2019年发布）
   */
  if (config.platform === "ios" && ('visualViewport' in window) && containerRef.value) {
    window.visualViewport?.addEventListener('resize', visualViewResizeHandle)
  }
})

onUpdated(() => {
  scroolBody()
})

onUnmounted(() => {
  window.visualViewport?.removeEventListener('resize', visualViewResizeHandle)
  window.removeEventListener('scroll', windowTouchScroll)
  window.removeEventListener('touchend', windowTouchEnd)
})
</script>
<style lang="less" scoped>
.header-comp {
  z-index: 10;
}
.container {
  background-color: #f8f8f8;
  height: calc(100vh - 45px);
  height: calc(100vh - 45px);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.box {
  position: relative;
  height: 100%;

  .new-data {
    min-height: calc(100vh - 280px);
    padding: 0px 15px 0px 15px;
  }

  // .new-datas{
  //     height: calc(100vh - 370px);
  // }
  // .new-dataa{
  //     height: calc(100vh - 250px);
  // }
  // .new-top{
  //     height: calc(100vh - 250px);
  // }
  // .new-datasa{
  //     height: calc(100vh - 390px);
  // }
  // .new-datasaa{
  //     height: calc(100vh - 340px);
  // }
  // .new-datab{
  //     height: calc(100vh - 255px);
  // }
  // 安卓没有底部的样式
  .new-dataf {
    min-height: calc(100vh - 195px);
    padding: 0px 15px 0px 15px;
  }

  // .new-datafs{
  //     height: calc(100vh - 280px);
  // }

  // 安卓没有评价
  .new-datafa {
    min-height: calc(100vh - 165px);
  }

  // Ios没有底部的样式
  .newi-dataf {
    min-height: calc(100vh - 155px - 50px - constant(safe-area-inset-bottom));
    min-height: calc(100vh - 155px - 50px - env(safe-area-inset-bottom));
    padding: 0px 15px 0px 15px;
  }

  // .newi-datafs{
  //     min-height: calc(100vh - 245px);
  // }
  // .newi-datafss{
  //     min-height: calc(100vh - 215px);
  // }
  .newi-datafa {
    min-height: calc(100vh - 105px - 50px - constant(safe-area-inset-bottom));
    min-height: calc(100vh - 105px - 50px - env(safe-area-inset-bottom));
  }

  .newi-footer {
    min-height: calc(100vh - 135px - 50px - constant(safe-area-inset-bottom));
    min-height: calc(100vh - 135px - 50px - env(safe-area-inset-bottom));
  }

  // .newi-datas{
  //     min-height: calc(100vh - 280px);
  // }
  // ios有底部的样式
  .newi-data {
    min-height: calc(100vh - 195px);
    padding: 0px 15px 0px 15px;
  }

  .newi-dataa {
    min-height: calc(100vh - 315px);
  }

  // .newi-off{
  //     min-height: calc(100vh - 220px);
  // }
  // .newi-odd{
  //     min-height: calc(100vh - 310px);
  // }
  // .newi-oaa{
  //     min-height: calc(100vh - 315px);
  // }
  // .newi-occ{
  //     min-height: calc(100vh - 75px);
  // }
  // .new-datax{
  //     min-height: calc(100vh - 286px);
  // }
  // .new-dataxa{
  //     min-height: calc(100vh - 250px);
  // }
  // 小于一屏键盘弹起
  .newi-ddd {
    min-height: 38vh;
  }

  .newi-fff {
    min-height: 70vh;
  }

  // 有评价
  .newi-hhh {
    min-height: 30vh;
  }

  .newi-jjj {
    min-height: 66vh;
  }
  .gift-flex {
    position: fixed;
    bottom: 13%;
    left: 5%;
    z-index: 999;
    animation: pulse 2s infinite;
  }
  .android-gift-flex {
    position: fixed;
    bottom: 13%;
    left: 5%;
    z-index: 999;
    animation: pulse 2s infinite;
  }
  .gift-img {
    width: 36px;
    height: 36px;
  }
}

.tip {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  color: #fff;
  background-color: #555;
  font-weight: 400;
  width: 100%;
}

.chat-box {
  width: 100vw;
  overflow-y: scroll;
  box-sizing: border-box;
  padding: 0px 15px 0px 15px;
  &.chat-box1 {
    position: absolute;
    z-index: 2;
  }
  &.chat-box2 {
    position: absolute;
    z-index: 1;
  }
}

.box-content {
  .box-time {
    font-size: 14px;
    color: #999;
    height: 40px;
    display: flex;
    align-items: center;
  }

  .box-times {
    font-size: 14px;
    color: #999;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 40px;
  }

  .box-left {
    display: flex;

    .box-p {
      display: inline-block;
      font-size: 14px;
      color: #333;
      padding: 10px;
      background-color: #99ccff;
      border-radius: 10px;
      word-break: break-all;
      min-height: 14px;

      .box-p-a {
        display: block;
      }

      .show-btn {
        min-width: 150px;
        display: flex;
        justify-content: space-around;
        padding-top: 10px;
      }
    }
  }

  .box-right {
    display: flex;
    justify-content: flex-end;

    .box-p {
      display: inline-block;
      font-size: 14px;
      color: #333;
      padding: 10px;
      background-color: #fff;
      border-radius: 10px;
      min-height: 14px;
    }
  }

  .user-img {
    max-width: 100%;
    min-width: 150px;
    height: 200px;
    object-fit: contain;
  }

  .banen-video {
    width: 200px;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;

    .play-img {
      width: 64px;
      height: 64px;
    }
  }

  //音量按钮
  video::-webkit-media-controls-mute-button {
    display: none;
  }
}

.footer {
  padding-left: 10px;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 97%;
  background-color: #f8f8f8;
  z-index: 3;
}

.evaluate {
  margin-bottom: 5px;

  .evaluate-text {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 15px;
    color: #333;
    font-size: 14px;
    background-color: #fff;
  }

  .evaluate-img {
    width: 15px;
    height: 15px;
    margin-right: 5px;
    vertical-align: text-top;
  }
}

.evaluates {
  min-height: 25px;
}

.footer-title {
  height: 50px;
  display: flex;
  align-items: center;

  .album-img {
    width: 24px;
    height: 22px;
    margin-right: 10px;
  }

  .footer-input {
    width: 233px;
    height: 40px;
    border-radius: 25px;
    padding-left: 12px;
    border: 1px solid #fff;
    outline: none;
    font-size: 14px;
    color: #333;
    -webkit-appearance: none;
    line-height: 40px;
  }

  .footer-form {
    position: relative;
  }

  .footer-inputs {
    background-color: #fff;
    width: 100%;
    height: 40px;
    border-radius: 25px;
    padding-left: 12px;
    border: 1px solid #fff;
    outline: none;
    font-size: 14px;
    color: #333;
    -webkit-appearance: none;
    box-sizing: border-box;
    &::placeholder {
      font-size: 14px;
    }
  }
  .input-mask {
    width: 100%;
    height: 40px;
    border-radius: 25px;
    padding-left: 12px;
    position: absolute;
    top: 0px;
    box-sizing: border-box;
  }

  .footer-right {
    width: 25%;
    display: flex;
    margin-left: 2%;
    align-items: center;
    justify-content: space-around;

    .camera {
      width: 27px;
      height: 25px;
    }

    .send {
      width: 40px;
      height: 42px;
    }
  }

  .footer-rights {
    width: 21%;
    display: flex;
    padding-left: 10px;
    align-items: center;
    justify-content: space-between;

    .camera {
      width: 25.5px;
      height: 23px;
    }

    .send-box {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .send {
      width: 30px;
      height: 30px;
    }
  }
}

.footer-box {
  display: flex;
  height: 90px;
  align-items: center;
  justify-content: space-around;

  .footer-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .item-top {
    background-color: #fff;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;

    .item-top-img {
      width: 69px;
      height: 49px;
    }
  }

  .item-text {
    font-size: 14px;
    color: #333;
    font-weight: bold;
  }
}

.catek {
  display: none;
}

.new-box-p {
  padding: 20px 10px !important;
  position: relative;
  background: linear-gradient(135deg, #8a2be2, #ff69b4) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
.new-box-d {
  padding: 20px 10px !important;
  position: relative;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
</style>
