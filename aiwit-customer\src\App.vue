<template>
  <RouterView></RouterView>
</template>
<script setup lang="ts">
import { useHomeStore } from '@/stores/home'
import { i18n } from '@/lang'
import config from './config'

const useStore = useHomeStore()
// 获取终端
useStore.getPlatform()

// 线上逻辑
if (window.location.href.indexOf('screen') !== -1) {
  let url = window.location.href
  let index = url.indexOf('screen')
  let str = url.substring(index + 14, index + 7).split('-')
  if (str.length) {
    str.some((item, index) => {
      if (item === '1') {
        useStore.getScreen(index)
        return true
      }
    })
  }

  useStore.increment(str)

  // 截取sessionId
  let index1 = url.indexOf('token')
  let index2 = url.indexOf('device_sn')
  let index3 = url.indexOf('uesrname')
  let index4 = url.indexOf('src')
  let index5 = url.indexOf('lang')
  let index6 = url.indexOf('version')
  let index7 = url.indexOf('app_name')
  config.sessionId = url.substring(index1 + 6, index2)
  // 截取设备ID
  config.device_sn = url.substring(index2 + 10, index3)
  // 截取用户名
  config.from_name = url.substring(index3 + 9, index4)
  // 截取src
  config.src = Number(url.substring(index4 + 5, index4 + 4))
  // 截取版本号兼容以前的
  if (index6 === -1) {
    config.app_version = '2.8.1'
  } else {
    config.app_version = url.substring(index6 + 13, index6 + 8)
  }
  if (index7 !== -1) {
    config.app_name = url.substring(index7 + 9, url.length - 2)
  }
  //截取语言
  if (url.substring(index5 + 7, index5 + 5) === 'cn') {
    localStorage.setItem('language', 'zh')
    i18n.global.locale.value = 'zh'
  } else {
    localStorage.setItem('language', 'en')
    i18n.global.locale.value = 'en'
  }
} else {
  // 设置全局参数适用本地调试
  config.sessionId = '40f0dda882febb9d5fd6f53e07cdd6d7'
  config.device_sn = "EKDB_6460325D-836B-0EBA-1590-E17055D794ED";
  // config.device_sn = "EKDB_D99673A7-EFB3-8732-E07D-202B4E9CA695"; //李敏
  // config.device_sn = "EKDB_5326E9A0-597A-539D-12AC-D0CD1B236341"; //李敏
  // config.device_sn = 'EKDB_40F9CC36-4286-FDF5-B59E-F84D6FD23F1F' //李敏
  // config.from_name = "<EMAIL>";
  config.from_name = '<EMAIL>'
  config.app_version = '2.8.2'

  // 本地调试
  useStore.increment(['1', '0', '0', '1'])
  useStore.getScreen(0)
}
</script>
<style lang="less" scoped>
* {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}
html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Segoe UI, Arial,
    Roboto, "PingFang SC", "miui", "Hiragino Sans GB", "Microsoft Yahei", sans-serif;
}
</style>
