import http from "./request"

// 每日问题列表
export function get_questions(offset:number,limit=20) {
  return http(`/turn_go/daily_questions`, {
    method: "GET",
    params: {offset,limit},
  })
}

// 获取图片资源
export function get_images(card_id:string,session_id:string,sign:string) {
  return http(`/turn_go/card_medias/${card_id}/${session_id}/${sign}`)
}

// 提交问题
export function submit_question(session_id:string,card_id:string,question_id:number,sign:string) {
  return http(`/turn_go/ask/${session_id}/${card_id}/${question_id}/${sign}`)
}


// 获取阿里云OSS服务key
export function getOssKey(sessionId:string) {
  return http(`/sts/token/${sessionId}/oss-us-west-1.aliyuncs.com/9`);
}