{"name": "h5-invite", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "preview": "vite preview", "build": "vite build --mode test", "build:prod": "vite build --mode production", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false"}, "dependencies": {"@lucky-canvas/vue": "^0.1.11", "ali-oss": "^6.18.1", "axios": "^1.5.1", "js-md5": "^0.8.3", "less": "^4.2.0", "reset-css": "^5.0.2", "vant": "^4.7.2", "vconsole": "^3.15.1", "vue": "^3.3.4", "vue-i18n": "^9.5.0", "vue-router": "^4.2.4"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/ali-oss": "^6.16.10", "@types/node": "^18.18.6", "@vitejs/plugin-vue": "^4.3.4", "@vue/tsconfig": "^0.4.0", "cypress": "^13.2.0", "moment": "^2.29.4", "npm-run-all2": "^6.0.6", "start-server-and-test": "^2.0.0", "typescript": "~5.2.0", "vite": "^4.4.9", "vue-tsc": "^1.8.11"}}