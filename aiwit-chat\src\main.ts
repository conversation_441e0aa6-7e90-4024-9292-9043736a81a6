import { createApp } from "vue"
import App from "./App.vue"
import router from "./router"

import "./assets/main.css"
import "vant/lib/index.css"

import { Locale } from "vant"
// 引入英文语言包
import enUS from "vant/es/locale/lang/en-US"
Locale.use("en-US", enUS)
import I18n from "@/languages/index"

import { Badge, Button, PullRefresh, List } from "vant"

const app = createApp(App)
app.use(router).use(I18n).mount("#app")
app.use(Badge)
app.use(Button)
app.use(PullRefresh)
app.use(List)
