<template>
  <div class="page">
    <Header title="选择设备" />
    <div class="dev-list">
      <div class="list-item" v-for="(item, index) in state.deviceList" :key="index" @click="detail(item)">
        <img class="blue-img" v-if="item.bluetooth == 1" src="../assets/device/blue.png" alt="" />
        <div class="item-img">
          <img class="dev-img" :src="item.product_4_img" alt="" />
        </div>
        <div class="item-text">{{ item.name }}</div>
      </div>
    </div>
    <van-empty description="暂无设备" v-if="!state.deviceList" />
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/Header.vue"
import config from "@/config"
import { reactive } from "vue"
import { useRouter } from "vue-router"
import { acquire_location } from "@/utils";
import type { addDeviceItem } from "@/api/base"

const router = useRouter()

const state = reactive({
  deviceList: config.add_device_list,
})

const detail = (item:addDeviceItem) => {
  acquire_location(item)
  config.add_device_item = item
  router.push('deviceReset')
}
</script>

<style lang="less" scoped>
.dev-list {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .list-item {
    width: 48%;
    height: 169px;
    background-color: #f3f3f3;
    border-radius: 8px;
    margin-bottom: 10px;
    position: relative;
    .blue-img {
      width: 26px;
      height: 26px;
      position: absolute;
      top: 10px;
      right: 10px;
    }
    .item-img {
      width: 100%;
      height: 80%;
      display: flex;
      align-items: center;
      justify-content: center;
      .dev-img {
        max-width: 100%;
        max-height: 105px;
        object-fit: contain;
      }
    }
    .item-text {
      font-size: 16px;
      color: #333333;
      font-weight: 700;
      text-align: center;
    }
  }
}
</style>
