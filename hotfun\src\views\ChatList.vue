<template>
  <div class="pages">
    <div class="ios-top"></div>
    <div class="pages-title">
      <div class="title-text">Pick a question</div>
      <van-icon name="cross" size="25" @click="chatHome" color="#222" />
    </div>
    <div class="chat-list">
      <div class="chat-top">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            class="item-text"
            @click="chatClick(item)"
            v-for="item in state.chatList"
            :key="item.id"
          >
            {{ item.question }}
          </div>
        </van-list>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { get_questions } from "@/api"
import { ref, reactive } from "vue"
import type { chatData } from "@/api/base"
import config from "@/config"
import { useRouter } from "vue-router"

const router = useRouter()
const state = reactive({
  chatList: [] as chatData[],
  offset: 0,
})
const loading = ref<boolean>(false)
const finished = ref<boolean>(false)

const chatClick = (item: chatData) => {
  item.screen = 0
  config.chatItem = item
  router.push("/")
}

const chatHome = () => {
  router.push("/")
}

const onLoad = () => {
  get_questions(state.offset).then((res: any) => {
    if (res.resultCode === 0) {
      loading.value = false
      if (res.content.questions && res.content.questions.length < 10) {
        finished.value = true
      }
      if (state.offset === 1) {
        state.chatList = res.content.questions
      } else {
        state.chatList = [...res.content.questions, ...state.chatList]
      }
      state.offset+=20
    }
  })
}
</script>

<style lang="less" scoped>
.pages {
  background-color: #fff3dc;
  min-height: 100vh;
  .ios-top {
    height: 40px;
  }
  .pages-title {
    display: flex;
    justify-content: space-between;
    padding: 0 16px;
    margin-bottom: 20px;
    .title-text {
      font-size: 26px;
      color: #222;
    }
  }
  .chat-list {
    padding: 0 16px 16px 16px;
    .item-text {
      background-color: #cae5ed;
      font-size: 13px;
      color: #222;
      line-height: 18px;
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 8px;
    }
  }
}
</style>
